<template>
  <BaseCard title="价格明细" class="price-detail-table">
    <div class="table-controls">
      <div class="search-box">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索产品..."
          class="search-input"
        />
      </div>
      <div class="filter-controls">
        <select v-model="sortBy" class="sort-select">
          <option value="product_name">按产品名称排序</option>
          <option value="price">按价格排序</option>
          <option value="change_rate">按变化率排序</option>
        </select>
      </div>
    </div>

    <div class="table-wrapper">
      <table class="price-table">
        <thead>
          <tr>
            <th>产品名称</th>
            <th>当前价格 (元/T)</th>
            <th>历史价格 (元/T)</th>
            <th>变化</th>
            <th>变化率</th>
            <th>更新时间</th>
          </tr>
        </thead>
        <tbody v-if="!isLoading && filteredData.length">
          <tr v-for="item in paginatedData" :key="item.id">
            <td class="product-name">{{ item.product_name }}</td>
            <td class="price current-price">
              {{ formatPrice(item.current_price, '', false) }}
            </td>
            <td class="price historical-price">
              {{ formatPrice(item.historical_price, '', false) }}
            </td>
            <td class="price-change" :class="getPriceChangeClass(item.price_change)">
              {{ formatPriceChange(item.price_change) }}
            </td>
            <td class="change-rate" :class="getChangeRateClass(item.current_price, item.historical_price)">
              {{ formatChangeRate(item.current_price, item.historical_price) }}
            </td>
            <td class="update-time">{{ formatDate(item.updated_at) }}</td>
          </tr>
        </tbody>
        <tbody v-else-if="isLoading">
          <tr>
            <td colspan="6" class="loading-cell">
              <div class="loading-spinner">加载中...</div>
            </td>
          </tr>
        </tbody>
        <tbody v-else>
          <tr>
            <td colspan="6" class="empty-cell">暂无数据</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="table-pagination" v-if="totalPages > 1">
      <button
        @click="currentPage--"
        :disabled="currentPage <= 1"
        class="pagination-btn"
      >
        上一页
      </button>
      <span class="pagination-info">
        第 {{ currentPage }} 页，共 {{ totalPages }} 页
      </span>
      <button
        @click="currentPage++"
        :disabled="currentPage >= totalPages"
        class="pagination-btn"
      >
        下一页
      </button>
    </div>
  </BaseCard>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { usePricingStore } from '@/stores/pricing'
import { formatPrice, formatChangeRate } from '@/utils/formatters'
import dayjs from 'dayjs'
import BaseCard from '@/components/common/BaseCard.vue'

const props = defineProps({
  startDate: {
    type: String,
    required: true
  },
  endDate: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['export-data'])

const pricingStore = usePricingStore()
const isLoading = ref(false)
const searchQuery = ref('')
const sortBy = ref('product_name')
const currentPage = ref(1)
const pageSize = ref(20)

// 过滤和排序数据
const filteredData = computed(() => {
  let data = [...pricingStore.priceDetailData]

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    data = data.filter(item =>
      item.product_name.toLowerCase().includes(query)
    )
  }

  // 排序
  data.sort((a, b) => {
    switch (sortBy.value) {
      case 'price':
        return b.current_price - a.current_price
      case 'change_rate':
        return Math.abs(b.change_rate) - Math.abs(a.change_rate)
      default:
        return a.product_name.localeCompare(b.product_name)
    }
  })

  return data
})

// 分页数据
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredData.value.slice(start, end)
})

const totalPages = computed(() => {
  return Math.ceil(filteredData.value.length / pageSize.value)
})

// 格式化函数
const formatPriceChange = (change) => {
  const prefix = change >= 0 ? '+' : ''
  return `${prefix}${formatPrice(change, '', false)}`
}

const formatDate = (dateString) => {
  return dayjs(dateString).format('MM-DD HH:mm')
}

// 样式类
const getPriceChangeClass = (change) => {
  if (change > 0) return 'positive'
  if (change < 0) return 'negative'
  return 'neutral'
}

const getChangeRateClass = (currentPrice, historicalPrice) => {
  if (!currentPrice || !historicalPrice || historicalPrice === 0) return 'stable'
  const rate = ((currentPrice - historicalPrice) / historicalPrice) * 100
  if (rate > 5) return 'high-increase'
  if (rate > 0) return 'increase'
  if (rate < -5) return 'high-decrease'
  if (rate < 0) return 'decrease'
  return 'stable'
}

// 获取数据
const fetchData = async () => {
  try {
    isLoading.value = true
    await pricingStore.fetchPriceDetailData(props.startDate, props.endDate)
  } catch (error) {
    console.error('Failed to load price detail data:', error)
  } finally {
    isLoading.value = false
  }
}

// 监听日期变化
watch([() => props.startDate, () => props.endDate], () => {
  currentPage.value = 1
  fetchData()
})

// 监听搜索和排序变化
watch([searchQuery, sortBy], () => {
  currentPage.value = 1
})

onMounted(fetchData)
</script>

<style scoped>
.price-detail-table {
  height: 100%;
}

.table-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  gap: 16px;
}

.search-box {
  flex: 1;
  max-width: 300px;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.filter-controls {
  display: flex;
  gap: 12px;
}

.sort-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background: white;
}

.table-wrapper {
  overflow-x: auto;
  margin-bottom: 16px;
}

.price-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}

.price-table th,
.price-table td {
  padding: 12px 8px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.price-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
}

.product-name {
  font-weight: 500;
  max-width: 200px;
}

.price {
  text-align: right;
  font-family: 'SF Mono', 'Monaco', 'Consolas', 'Menlo', 'DejaVu Sans Mono', monospace;
  font-variant-numeric: tabular-nums;
}

.current-price {
  color: #1976D2;
  font-weight: 600;
}

.historical-price {
  color: #666;
}

.price-change.positive {
  color: #4CAF50;
}

.price-change.negative {
  color: #F44336;
}

.price-change.neutral {
  color: #666;
}

.change-rate {
  text-align: right;
  font-weight: 500;
}

.change-rate.high-increase {
  color: #4CAF50;
  background-color: #E8F5E8;
}

.change-rate.increase {
  color: #4CAF50;
}

.change-rate.high-decrease {
  color: #F44336;
  background-color: #FFEBEE;
}

.change-rate.decrease {
  color: #F44336;
}

.change-rate.stable {
  color: #666;
}

.update-time {
  color: #666;
  font-size: 12px;
  white-space: nowrap;
}

.loading-cell,
.empty-cell {
  text-align: center;
  padding: 40px;
  color: #666;
}

.loading-spinner {
  display: inline-block;
}

.table-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.pagination-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.pagination-btn:hover:not(:disabled) {
  background-color: #f5f5f5;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  color: #666;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    max-width: none;
  }

  .price-table {
    font-size: 12px;
  }

  .price-table th,
  .price-table td {
    padding: 8px 4px;
  }
}
</style>