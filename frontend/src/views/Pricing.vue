<template>
  <div class="pricing-page">
    <!-- 价格概览卡片 -->
    <div class="pricing-overview">
      <div class="overview-cards">
        <MetricCard
          title="平均单价"
          :value="pricingStore.averagePrice"
          unit="元/T"
          icon="💰"
          variant="primary"
          :loading="pricingStore.isLoading"
        />
        <MetricCard
          title="价格变化"
          :value="pricingStore.priceChangeRate"
          unit="%"
          icon="📈"
          variant="secondary"
          :loading="pricingStore.isLoading"
          :format="formatChangeRate"
          :class="getTrendColorClass(pricingStore.latestPrice, pricingStore.firstPrice)"
        />
        <MetricCard
          title="最高价格"
          :value="pricingStore.maxPrice"
          unit="元/T"
          icon="⬆️"
          variant="tertiary"
          :loading="pricingStore.isLoading"
        />
        <MetricCard
          title="最低价格"
          :value="pricingStore.minPrice"
          unit="元/T"
          icon="⬇️"
          variant="accent"
          :loading="pricingStore.isLoading"
        />
      </div>
    </div>

    <!-- 控制面板 -->
    <div class="pricing-controls">
      <div class="date-range-selector">
        <label>数据范围：</label>
        <input
          v-model="dateRange.start"
          type="date"
          @change="updateDateRange"
        />
        <span>至</span>
        <input
          v-model="dateRange.end"
          type="date"
          @change="updateDateRange"
        />
      </div>

      <div class="month-selector">
        <label>按月选择：</label>
        <MonthSelector
          v-model="monthRange"
          @change="handleMonthChange"
        />
      </div>

      <div class="product-selector">
        <label>对比产品：</label>
        <select v-model="selectedProducts" multiple class="product-select">
          <option v-for="product in availableProducts" :key="product" :value="product">
            {{ product }}
          </option>
        </select>
      </div>

      <div class="control-buttons">
        <BaseButton
          @click="refreshData"
          :loading="pricingStore.isLoading"
          variant="primary"
          size="small"
          icon="🔄"
        >
          刷新数据
        </BaseButton>

        <BaseButton
          @click="exportData"
          variant="secondary"
          size="small"
          icon="📊"
        >
          导出数据
        </BaseButton>
      </div>
    </div>

    <!-- 价格分析图表 -->
    <div class="pricing-charts">
      <div class="chart-section">
        <PriceTrendChart
          :start-date="dateRange.start"
          :end-date="dateRange.end"
          @data-updated="handlePriceDataUpdate"
        />
      </div>

      <div class="chart-section">
        <PriceDistributionChart
          :start-date="dateRange.start"
          :end-date="dateRange.end"
        />
      </div>
    </div>

    <!-- 价格对比分析 -->
    <div class="pricing-comparison">
      <PriceComparisonChart
        :start-date="dateRange.start"
        :end-date="dateRange.end"
        :selected-products="selectedProducts"
      />
    </div>

    <!-- 价格明细表格 -->
    <div class="pricing-details">
      <PriceDetailTable
        :start-date="dateRange.start"
        :end-date="dateRange.end"
        @export-data="handleExportData"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { usePricingStore } from '@/stores/pricing'
import { formatPrice, formatChangeRate, getTrendColorClass } from '@/utils/formatters'
import { useDateRange } from '@/composables/useDateRange'
import MetricCard from '@/components/dashboard/MetricCard.vue'
import PriceTrendChart from '@/components/pricing/PriceTrendChart.vue'
import PriceDistributionChart from '@/components/pricing/PriceDistributionChart.vue'
import PriceComparisonChart from '@/components/pricing/PriceComparisonChart.vue'
import PriceDetailTable from '@/components/pricing/PriceDetailTable.vue'
import BaseButton from '@/components/common/BaseButton.vue'
import MonthSelector from '@/components/common/MonthSelector.vue'

const pricingStore = usePricingStore()
const { dateRange: dynamicDateRange, ensureDateRangeLoaded } = useDateRange()

// 本地状态 - 使用动态计算的默认日期范围
const dateRange = ref({
  start: dynamicDateRange.value.start,
  end: dynamicDateRange.value.end
})
const selectedProducts = ref([])
const monthRange = ref({ start: '', end: '' })

// 计算属性
const availableProducts = computed(() => {
  return pricingStore.availableProducts
})

// 方法
const updateDateRange = async () => {
  try {
    await pricingStore.updateDateRange(dateRange.value.start, dateRange.value.end)
  } catch (error) {
    console.error('Failed to update date range:', error)
  }
}

const refreshData = async () => {
  try {
    await pricingStore.refreshData()
  } catch (error) {
    console.error('Failed to refresh pricing data:', error)
  }
}

const exportData = () => {
  // TODO: 实现数据导出功能
  console.log('Export pricing data')
}

const handlePriceDataUpdate = (data) => {
  console.log('Price data updated:', data.length, 'records')
}

const handleExportData = (data) => {
  console.log('Export data:', data.length, 'records')
}

const handleMonthChange = (range) => {
  // 使用月份选择器的日期范围
  dateRange.value = range
  
  // 触发数据更新
  updateDateRange()
}

// 生命周期
onMounted(async () => {
  try {
    // 确保日期范围已加载
    await ensureDateRangeLoaded()
    
    // 更新本地日期状态
    dateRange.value = {
      start: dynamicDateRange.value.start,
      end: dynamicDateRange.value.end
    }
    
    if (!pricingStore.hasData) {
      await pricingStore.fetchAllPricingData()
    }
  } catch (error) {
    console.error('Failed to load pricing data:', error)
  }
})
</script>

<style scoped>
.pricing-page {
  padding: 0;
}

.pricing-overview {
  margin-bottom: 32px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.pricing-charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.chart-section {
  min-height: 400px;
}

.pricing-comparison {
  margin-bottom: 32px;
}

.pricing-details {
  margin-bottom: 32px;
}

.pricing-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: var(--background-light);
  border-radius: 12px;
  flex-wrap: wrap;
  gap: 16px;
}

.date-range-selector,
.product-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.9em;
}

.date-range-selector label,
.product-selector label {
  color: var(--text-secondary);
  font-weight: 500;
  white-space: nowrap;
}

.date-range-selector input,
.product-select {
  padding: 6px 10px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.9em;
}

.product-select {
  min-width: 150px;
  max-height: 100px;
}

.date-range-selector input:focus,
.product-select:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.control-buttons {
  display: flex;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .pricing-charts {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .overview-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .pricing-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .date-range-selector,
  .product-selector {
    justify-content: center;
    flex-wrap: wrap;
  }

  .control-buttons {
    justify-content: center;
  }
}
</style>
