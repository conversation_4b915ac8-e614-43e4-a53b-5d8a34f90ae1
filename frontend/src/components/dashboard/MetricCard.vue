<template>
  <div class="metric-card" :class="cardClasses">
    <div class="metric-header">
      <div class="metric-icon">{{ icon }}</div>
      <div class="metric-info">
        <h3 class="metric-title">{{ title }}</h3>
        <div class="metric-value">
          <span v-if="loading" class="loading-placeholder">--</span>
          <span v-else class="value-text">
            {{ formattedValue }}
            <span v-if="unit" class="unit">{{ unit }}</span>
          </span>
        </div>
      </div>
    </div>

    <!-- Sparkline 图表 -->
    <div v-if="sparklineData && sparklineData.length > 0" class="metric-sparkline">
      <SparklineChart
        :data="sparklineData"
        :color="sparklineColor"
        height="40px"
      />
    </div>

    <!-- 变化趋势 -->
    <div v-if="change !== null" class="metric-change">
      <span class="change-icon">{{ changeIcon }}</span>
      <span class="change-text">{{ changeText }}</span>
    </div>

    <!-- 额外信息 -->
    <div v-if="$slots.extra" class="metric-extra">
      <slot name="extra" />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { formatNumber, formatCurrency, getTrendIcon, getTrendColorClass } from '@/utils/formatters'
import { CHART_COLORS } from '@/utils/constants'
import SparklineChart from '@/components/charts/SparklineChart.vue'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: [Number, String],
    required: true
  },
  unit: {
    type: String,
    default: ''
  },
  icon: {
    type: String,
    default: '📊'
  },
  variant: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'secondary', 'tertiary', 'accent'].includes(value)
  },
  loading: {
    type: Boolean,
    default: false
  },
  format: {
    type: Function,
    default: null
  },
  sparklineData: {
    type: Array,
    default: () => []
  },
  change: {
    type: Number,
    default: null
  },
  previousValue: {
    type: Number,
    default: null
  }
})

// 计算属性
const cardClasses = computed(() => [
  `variant-${props.variant}`,
  {
    'loading': props.loading,
    'has-sparkline': props.sparklineData.length > 0,
    'has-change': props.change !== null
  }
])

const formattedValue = computed(() => {
  if (props.value === null) {
    return "N/A";
  }
  if (props.loading) return '--'
  
  if (props.format && typeof props.format === 'function') {
    return props.format(props.value)
  }
  
  const numValue = parseFloat(props.value)
  if (isNaN(numValue)) return props.value
  
  // 根据单位选择格式化方式
  return formatNumber(numValue)
})

const sparklineColor = computed(() => {
  const colors = {
    primary: CHART_COLORS.PRIMARY,
    secondary: CHART_COLORS.SECONDARY,
    tertiary: CHART_COLORS.SUCCESS,
    accent: CHART_COLORS.WARNING
  }
  return colors[props.variant] || CHART_COLORS.PRIMARY
})

const changeIcon = computed(() => {
  if (props.change === null) return ''
  return getTrendIcon(props.value, props.previousValue)
})

const changeText = computed(() => {
  if (props.change === null) return ''
  
  const absChange = Math.abs(props.change)
  const sign = props.change >= 0 ? '+' : ''
  return `${sign}${absChange.toFixed(1)}%`
})

const changeColorClass = computed(() => {
  if (props.change === null) return ''
  return getTrendColorClass(props.value, props.previousValue)
})
</script>

<style scoped>
.metric-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.metric-card.loading {
  opacity: 0.7;
}

/* 变体样式 */
.variant-primary {
  border-left: 4px solid var(--primary-blue);
}

.variant-secondary {
  border-left: 4px solid var(--secondary-blue);
}

.variant-tertiary {
  border-left: 4px solid var(--success-green);
}

.variant-accent {
  border-left: 4px solid var(--warning-orange);
}

.metric-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
}

.metric-icon {
  font-size: 2em;
  line-height: 1;
  opacity: 0.8;
}

.metric-info {
  flex: 1;
}

.metric-title {
  font-size: 0.9em;
  color: var(--text-secondary);
  margin: 0 0 8px 0;
  font-weight: 500;
}

.metric-value {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.value-text {
  font-size: 1.8em;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.unit {
  font-size: 0.6em;
  color: var(--text-secondary);
  font-weight: 400;
}

.loading-placeholder {
  font-size: 1.8em;
  color: var(--text-secondary);
  font-weight: 700;
}

.metric-sparkline {
  margin: 12px 0;
  height: 40px;
  opacity: 0.8;
}

.metric-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.8em;
  margin-top: 8px;
}

.change-icon {
  font-size: 1.1em;
}

.change-text {
  font-weight: 500;
}

.metric-extra {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  font-size: 0.85em;
  color: var(--text-secondary);
}

/* 趋势颜色 */
.metric-change .trend-positive {
  color: var(--success-green);
}

.metric-change .trend-negative {
  color: var(--signal-red);
}

.metric-change .trend-neutral {
  color: var(--neutral-gray);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .metric-card {
    padding: 16px;
  }
  
  .metric-header {
    gap: 12px;
    margin-bottom: 12px;
  }
  
  .metric-icon {
    font-size: 1.5em;
  }
  
  .value-text {
    font-size: 1.5em;
  }
  
  .metric-title {
    font-size: 0.85em;
  }
}
</style>
