# 春雪食品产销分析系统 - 开发注意事项

## 1. 概述

本文档记录了在开发和维护春雪食品产销分析系统过程中发现的关键实现细节、常见陷阱和重要警告，旨在帮助未来的开发者避免重复问题并快速理解系统架构。

**最后更新**: 2025年7月30日
**版本**: v4.0 (包含最新技术栈和优化经验)
**状态**: ✅ 基于实际项目经验验证

## 📋 文档导航
- [最关键问题](#-11-最关键问题---数据显示问题)
- [数据导入系统优化](#-12-数据导入系统重大优化-2025-07-29)
- [技术栈注意事项](#2-技术栈关键注意事项)
- [数据导入关键事项](#3-数据导入关键注意事项)
- [前端开发注意事项](#4-前端开发注意事项)
- [后端开发注意事项](#5-后端开发注意事项)
- [部署和维护](#6-部署和维护注意事项)
- [常见问题解决方案](#7-常见问题和解决方案)

## 🚨 1.1 最关键问题 - 数据导入与显示

### 问题背景
系统曾面临两大核心问题，严重影响数据准确性和可用性：
1.  **数据导入缺陷**：导致销量异常、产量缺失、库存错误。
2.  **数据显示错误**：API过滤逻辑不当，导致前端无数据。

本节整合了这两大问题的根本原因、解决方案和预防措施，是理解系统稳定性的关键。

### 1.1.1 数据导入系统重大优化 (2025-07-29)

#### 问题描述
- **销量异常**：单日销量超1000吨（正常应为200-600吨），存在数据重复。
- **产量缺失**：产量数据集中在单日，未按实际日期分布。
- **库存错误**：库存总量仅19吨（实际应为数千吨）。

#### 根本原因
1.  **数据重复**：`groupby` 逻辑包含了 `category`，导致同一产品在不同分类下被计为多条记录。
2.  **单位转换错误**：在数据处理和导入环节对销量进行了两次单位换算（kg → 吨 → kg），导致数据被放大1000倍。
3.  **日期处理缺陷**：生产数据导入时未从Excel中提取实际的“入库日期”，而是错误地使用了统一的业务日期。

#### 核心修复方案
1.  **聚合逻辑修正**：数据聚合时仅使用 `record_date` 和 `product_name` 作为主键，确保唯一性。
    ```python
    # ✅ 修复代码：只按日期和产品聚合
    df_processed = df_processed.groupby(['record_date', 'product_name']).agg({
        'sales_volume': 'sum',
        'tax_free_amount': 'sum',
        'category': 'first'
    }).reset_index()
    ```
2.  **统一数据单位**：数据在整个处理流程中保持 `kg` 单位，仅在前端显示时按需转换为“吨”。
3.  **自动日期提取**：实现动态日期列检测（如 `入库日期`, `生产日期`），确保使用文件中的实际日期。
4.  **健壮的数据合并**：采用字典结构 `records_dict[(product_id, date)]` 来合并库存、生产和销售数据，从根本上杜绝了重复。
5.  **前端日期修正**：前端库存查询使用固定的、有完整数据的业务日期（如 `'2025-07-28'`)，避免因查询最新日期数据不完整而导致的问题。

#### 验证结果
| 指标 | 修复前 | 修复后 | 状态 |
|---|---|---|---|
| 日销量范围 | 1000+吨 | 200-600吨 | ✅ 正常 |
| 库存总量 | 19吨 | 6,440吨 | ✅ 正确 |
| 产量分布 | 集中单日 | 按日分布 | ✅ 正常 |
| 数据重复 | 大量重复 | 无重复 | ✅ 清洁 |

### 1.1.2 API数据显示问题

#### 问题描述
**症状**：前端所有数据显示为"--"，图表空白，API直接返回空数组 `[]`。

#### 根本原因
API查询中的 `WHERE` 条件过严，`p.category IS NOT NULL AND p.category != ''` 直接过滤掉了所有 `category` 字段为 `NULL` 或空字符串的产品，而数据库中大部分产品都属于这种情况。

#### 完整解决方案
将过滤条件从“必须有分类”改为“允许没有分类”，从而兼容实际数据状况。

```typescript
// 🚨 问题代码 (会导致返回空数组)
AND (
  p.category IS NOT NULL
  AND p.category != ''
  AND p.category NOT IN ('副产品', '生鲜品其他')
)

// ✅ 修复代码 (允许空category字段)
AND (
  p.category IS NULL
  OR p.category = ''
  OR p.category NOT IN ('副产品', '生鲜品其他')
)
```

#### 需要修复的API端点
- `/api/inventory/top`
- `/api/trends/ratio`
- `/api/trends/sales-price`

### ⚠️ 关键预防措施
1.  **导入后必须验证**：每次数据导入后，必须执行验证脚本，检查核心指标（总销量、总产量、总库存）是否在合理范围内。
2.  **数据单位一致性**：坚持“后端存 `kg`，前端转吨”原则，避免在数据处理链路中进行不必要的单位转换。
3.  **日期字段优先**：数据导入脚本必须优先使用Excel文件内提供的日期字段，只有在缺失时才回退到默认业务日期。
4.  **唯一性约束**：数据合并逻辑必须基于唯一键（如 `product_id` + `record_date`），从机制上防止数据重复。
5.  **过滤条件需谨慎**：API的过滤条件设计必须基于对实际数据的分析，避免因条件过严而意外过滤掉所有数据。

## 2. 技术栈关键注意事项

### 2.1 ⚠️ 架构概述
**当前架构**: 全栈无服务器应用，基于Cloudflare边缘计算平台
- **前端**: Vue.js 3 SPA + Vite 5.x + Pinia状态管理
- **后端**: Cloudflare Workers + Hono 4.x框架
- **数据库**: Cloudflare D1 (SQLite兼容)
- **部署**: Cloudflare Pages (前端) + Workers (后端)

### 2.2 ⚠️ 关键技术版本要求
```json
{
  "frontend": {
    "vue": "^3.4.0",
    "vite": "^5.x",
    "pinia": "^2.1.7",
    "vue-router": "^4.2.5",
    "echarts": "^5.6.0",
    "axios": "^1.6.2"
  },
  "backend": {
    "hono": "^4.8.3",
    "wrangler": "^4.21.0",
    "typescript": "^5.0.0"
  }
}
```

### 2.3 ⚠️ 开发环境配置
**必需的环境变量**:
```bash
# 前端 (.env.development)
VITE_API_BASE_URL=http://localhost:8787
VITE_APP_TITLE=春雪食品产销分析系统

# 后端 (wrangler.toml)
[vars]
ENVIRONMENT = "development"
JWT_SECRET = "your-development-secret"
```

**开发服务器启动顺序**:
1. 后端: `cd backend && npm run dev` (端口8787)
2. 前端: `cd frontend && npm run dev` (端口3000)
3. 前端自动代理API请求到后端

### 2.4 ⚠️ CORS配置要求
```typescript
// backend/src/index.ts - 必需配置
app.use('*', cors({
    origin: [
        'http://localhost:3000', 
        'http://127.0.0.1:3000',
        'https://your-production-domain.pages.dev'
    ],
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowHeaders: ['Content-Type', 'Authorization'],
    credentials: true
}));
```

## 3. 数据导入关键注意事项

### 3.1 ⚠️ 数据导入系统架构
**推荐导入方式**: 使用Python脚本进行生产数据导入
```bash
# 生产环境推荐
cd data_import
python3 daily_import_main.py

# 开发环境可用
python3 bulk_import_main.py --inventory-date 2025-07-28
```

**⚠️ 严禁在生产环境使用**:
- `import_inventory_data.ts` (仅开发用)
- 任何TypeScript导入脚本
- 直接SQL插入脚本

### 3.2 ⚠️ 库存数据处理的重要发现

**问题**: 库存Excel文件(`收发存汇总表查询.xlsx`)通常不包含日期列，导致所有库存数据被分配到同一个日期。

**解决方案**: 
```python
# 在data_importer.py中实现多日期库存记录生成
if 'record_date' not in df.columns:
    print(f"🔧 FIXING: Creating inventory records for multiple dates...")
    date_range = pd.date_range(start='2025-06-01', end='2025-06-26', freq='D')
    
    expanded_records = []
    for _, row in df.iterrows():
        for date in date_range:
            new_row = row.copy()
            new_row['record_date'] = date.strftime('%Y-%m-%d')
            expanded_records.append(new_row)
    
    df = pd.DataFrame(expanded_records)
```

**⚠️ 警告**: 
- 不要直接使用文件修改时间作为库存日期
- 确保库存数据覆盖完整的分析周期
- 库存数据应该是期末余额，不需要按日累加

### 3.3 ⚠️ 价格数据列名映射

**问题**: Excel文件中的价格列名可能有多种变体。

**解决方案**: 实现优先级映射
```python
# 按优先级尝试不同的价格列名
if '本币含税单价' in df.columns:
    df.rename(columns={'本币含税单价': 'average_price'}, inplace=True)
elif '含税单价' in df.columns:
    df.rename(columns={'含税单价': 'average_price'}, inplace=True)
elif '本币无税单价' in df.columns and '本币无税金额' in df.columns:
    # 计算含税价格: (无税金额 / 主数量) * 1.09
    df['average_price'] = (df['本币无税金额'] / df['sales_volume']) * 1.09
```

**⚠️ 警告**:
- 价格计算时要处理除零错误
- 确保价格单位一致(元/吨)
- 验证计算出的价格是否在合理范围内

### 3.4 ⚠️ 产品名称一致性

**关键要求**: 所有Excel文件中的产品名称必须完全一致，包括空格和特殊字符。

**检查方法**:
```python
# 检查产品名称一致性
inbound_products = set(inbound_df['product_name'].unique())
sales_products = set(sales_df['product_name'].unique())
summary_products = set(summary_df['product_name'].unique())

print(f"仅在生产文件中: {inbound_products - sales_products - summary_products}")
print(f"仅在销售文件中: {sales_products - inbound_products - summary_products}")
print(f"仅在库存文件中: {summary_products - inbound_products - sales_products}")
```

### 3.5 ⚠️ 数据质量验证
**每次导入后必须检查**:
```python
# 数据范围验证
print(f"销量范围: {df['sales_volume'].min():.2f} - {df['sales_volume'].max():.2f} kg")
print(f"日销量总计: {df.groupby('record_date')['sales_volume'].sum().describe()}")

# 重复记录检查
duplicates = df.groupby(['record_date', 'product_id']).size()
if duplicates.max() > 1:
    print(f"⚠️ 发现重复记录: {duplicates[duplicates > 1].sum()}条")

# 数据完整性检查
missing_dates = pd.date_range(start='2025-06-01', end='2025-07-28').difference(
    pd.to_datetime(df['record_date'])
)
if len(missing_dates) > 0:
    print(f"⚠️ 缺失日期: {missing_dates.tolist()}")
```

## 4. 前端开发注意事项

### 4.1 ⚠️ Vue 3 Composition API最佳实践
**推荐组件结构**:
```vue
<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useDashboardStore } from '@/stores/dashboard'

// 响应式数据
const loading = ref(false)
const error = ref(null)

// 状态管理
const dashboardStore = useDashboardStore()

// 计算属性
const formattedData = computed(() => {
    return dashboardStore.data?.map(item => ({
        ...item,
        volume: (item.volume / 1000).toFixed(2) // kg转吨
    }))
})

// 生命周期
onMounted(async () => {
    await loadData()
})

// 方法
const loadData = async () => {
    try {
        loading.value = true
        await dashboardStore.fetchData()
    } catch (err) {
        error.value = err.message
    } finally {
        loading.value = false
    }
}
</script>
```

### 4.2 ⚠️ Pinia状态管理模式
**推荐Store结构**:
```javascript
// stores/dashboard.js
import { defineStore } from 'pinia'
import { api } from '@/utils/api'

export const useDashboardStore = defineStore('dashboard', {
    state: () => ({
        data: null,
        loading: false,
        error: null,
        lastUpdated: null
    }),
    
    getters: {
        formattedData: (state) => {
            return state.data?.map(item => ({
                ...item,
                displayVolume: `${(item.volume / 1000).toFixed(2)}吨`
            }))
        }
    },
    
    actions: {
        async fetchData(dateRange) {
            this.loading = true
            this.error = null
            
            try {
                const response = await api.get('/dashboard/summary', {
                    params: dateRange
                })
                this.data = response.data
                this.lastUpdated = new Date()
            } catch (error) {
                this.error = error.message
                throw error
            } finally {
                this.loading = false
            }
        }
    }
})
```

### 4.3 ⚠️ 自动数据加载机制

**重要发现**: 用户登录后必须自动加载所有数据，不能依赖用户手动操作。

**实现方案**:
```javascript
// 在auth.js中，登录成功后调用
if (typeof window.loadAllData === 'function') {
    window.loadAllData();
} else {
    loadSummaryData();
}

// 在script.js中实现综合数据加载
window.loadAllData = async function() {
    await loadSummaryData();
    
    if (document.getElementById('start-date') && document.getElementById('end-date')) {
        const startDate = document.getElementById('start-date').value || '2025-06-01';
        const endDate = document.getElementById('end-date').value || '2025-06-26';
        
        await Promise.all([
            updateSummaryCards(startDate, endDate),
            updateInventoryChart(endDate),
            updateSalesPriceChart(startDate, endDate),
            updateRatioTrendChart(startDate, endDate)
        ]);
    }
};
```

### 4.4 ⚠️ ECharts 5.6.0图表优化

**推荐的ECharts组件封装**:
```vue
<!-- components/charts/EChartsWrapper.vue -->
<template>
    <div 
        ref="chartRef" 
        :style="{ width: '100%', height: height }"
        class="echarts-container"
    />
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
    option: Object,
    height: { type: String, default: '400px' },
    theme: { type: String, default: 'default' }
})

const chartRef = ref(null)
let chartInstance = null

onMounted(async () => {
    await nextTick()
    initChart()
})

onUnmounted(() => {
    if (chartInstance) {
        chartInstance.dispose()
    }
})

const initChart = () => {
    if (chartRef.value) {
        chartInstance = echarts.init(chartRef.value, props.theme)
        updateChart()
        
        // 响应式调整
        window.addEventListener('resize', handleResize)
    }
}

const updateChart = () => {
    if (chartInstance && props.option) {
        chartInstance.setOption(props.option, true)
    }
}

const handleResize = () => {
    if (chartInstance) {
        chartInstance.resize()
    }
}

watch(() => props.option, updateChart, { deep: true })
</script>
```

**性能优化配置**:
```javascript
// 大数据量图表优化
const chartOption = {
    animation: false, // 关闭动画提升性能
    progressive: 1000, // 渐进式渲染
    progressiveThreshold: 3000,
    dataZoom: [{
        type: 'inside',
        throttle: 50 // 缩放节流
    }],
    series: [{
        type: 'line',
        sampling: 'lttb', // 采样算法优化
        large: true, // 大数据量优化
        largeThreshold: 2000
    }]
}
```

### 4.5 ⚠️ 错误处理和用户反馈

**推荐的错误处理模式**:
```javascript
// utils/errorHandler.js
export class ApiError extends Error {
    constructor(message, status, code) {
        super(message)
        this.name = 'ApiError'
        this.status = status
        this.code = code
    }
}

// utils/api.js
import axios from 'axios'
import { ApiError } from './errorHandler'

const api = axios.create({
    baseURL: import.meta.env.VITE_API_BASE_URL,
    timeout: 10000
})

// 请求拦截器
api.interceptors.request.use(
    config => {
        const token = localStorage.getItem('auth_token')
        if (token) {
            config.headers.Authorization = `Bearer ${token}`
        }
        return config
    },
    error => Promise.reject(error)
)

// 响应拦截器
api.interceptors.response.use(
    response => response,
    error => {
        if (error.response) {
            throw new ApiError(
                error.response.data.message || '服务器错误',
                error.response.status,
                error.response.data.code
            )
        } else if (error.request) {
            throw new ApiError('网络连接失败', 0, 'NETWORK_ERROR')
        } else {
            throw new ApiError('请求配置错误', 0, 'CONFIG_ERROR')
        }
    }
)

export { api }
```

**组件中的错误处理**:
```vue
<script setup>
import { ref } from 'vue'
import { api } from '@/utils/api'
import { ApiError } from '@/utils/errorHandler'

const loading = ref(false)
const error = ref(null)

const fetchData = async () => {
    try {
        loading.value = true
        error.value = null
        
        const response = await api.get('/dashboard/summary')
        return response.data
    } catch (err) {
        if (err instanceof ApiError) {
            error.value = `${err.message} (${err.status})`
        } else {
            error.value = '未知错误，请稍后重试'
        }
        console.error('数据获取失败:', err)
    } finally {
        loading.value = false
    }
}
</script>
```

## 5. 后端开发注意事项

### 5.1 ⚠️ Hono框架最佳实践

**推荐的Hono应用结构**:
```typescript
// src/index.ts
import { Hono } from 'hono'
import { cors } from 'hono/cors'
import { logger } from 'hono/logger'
import { prettyJSON } from 'hono/pretty-json'

// 路由模块
import { dashboardRoutes } from './routes/dashboard'
import { inventoryRoutes } from './routes/inventory'
import { authRoutes } from './routes/auth'

const app = new Hono<{ Bindings: CloudflareBindings }>()

// 中间件配置
app.use('*', logger())
app.use('*', prettyJSON())
app.use('*', cors({
    origin: [
        'http://localhost:3000',
        'http://127.0.0.1:3000',
        'https://your-domain.pages.dev'
    ],
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowHeaders: ['Content-Type', 'Authorization'],
    credentials: true
}))

// 路由注册
app.route('/api/dashboard', dashboardRoutes)
app.route('/api/inventory', inventoryRoutes)
app.route('/api/auth', authRoutes)

// 健康检查
app.get('/health', (c) => {
    return c.json({ 
        status: 'ok', 
        timestamp: new Date().toISOString(),
        version: '1.0.0'
    })
})

export default app
```

**模块化路由示例**:
```typescript
// src/routes/dashboard.ts
import { Hono } from 'hono'
import { getDashboardSummary } from '../controllers/dashboard'
import { authMiddleware } from '../middleware/auth'

const dashboard = new Hono()

dashboard.use('*', authMiddleware) // 认证中间件

dashboard.get('/summary', getDashboardSummary)

export { dashboard as dashboardRoutes }
```

### 5.2 ⚠️ Cloudflare D1数据库优化

**D1数据库连接和查询模式**:
```typescript
// src/db.ts
export class DatabaseService {
    constructor(private db: D1Database) {}
    
    async executeQuery<T>(query: string, params: any[] = []): Promise<T[]> {
        try {
            const stmt = this.db.prepare(query)
            const result = await stmt.bind(...params).all()
            return result.results as T[]
        } catch (error) {
            console.error('Database query failed:', error)
            throw new Error(`Database error: ${error.message}`)
        }
    }
    
    async executeTransaction(queries: Array<{ query: string, params: any[] }>) {
        const statements = queries.map(({ query, params }) => 
            this.db.prepare(query).bind(...params)
        )
        
        try {
            await this.db.batch(statements)
        } catch (error) {
            console.error('Transaction failed:', error)
            throw new Error(`Transaction error: ${error.message}`)
        }
    }
}
```

**性能优化的查询模式**:
```typescript
// 使用预编译语句和批量操作
const getDashboardData = async (c: Context) => {
    const { start_date, end_date } = c.req.query()
    const db = new DatabaseService(c.env.DB)
    
    // 并行执行多个查询
    const [salesData, inventoryData, productionData] = await Promise.all([
        db.executeQuery(`
            SELECT record_date, SUM(sales_volume) as total_sales
            FROM DailyMetrics 
            WHERE record_date BETWEEN ? AND ? 
              AND sales_volume > 0
            GROUP BY record_date 
            ORDER BY record_date
        `, [start_date, end_date]),
        
        db.executeQuery(`
            SELECT product_name, inventory_level
            FROM DailyMetrics dm
            JOIN Products p ON dm.product_id = p.product_id
            WHERE record_date = ?
              AND inventory_level > 0
            ORDER BY inventory_level DESC
            LIMIT 10
        `, [end_date]),
        
        db.executeQuery(`
            SELECT record_date, SUM(production_volume) as total_production
            FROM DailyMetrics 
            WHERE record_date BETWEEN ? AND ?
              AND production_volume > 0
            GROUP BY record_date
        `, [start_date, end_date])
    ])
    
    return c.json({
        sales: salesData,
        inventory: inventoryData,
        production: productionData
    })
}
```

**必需的数据库索引**:
```sql
-- 核心性能索引
CREATE INDEX idx_dailymetrics_date ON DailyMetrics(record_date);
CREATE INDEX idx_dailymetrics_product_id ON DailyMetrics(product_id);
CREATE INDEX idx_dailymetrics_date_product ON DailyMetrics(record_date, product_id);
CREATE INDEX idx_products_name ON Products(product_name);

-- 复合索引用于常见查询
CREATE INDEX idx_dailymetrics_date_sales ON DailyMetrics(record_date, sales_volume) 
WHERE sales_volume > 0;
```

### 5.3 ⚠️ 参数验证和错误处理

**推荐的验证中间件**:
```typescript
// src/middleware/validation.ts
import { z } from 'zod'

const dateRangeSchema = z.object({
    start_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, '日期格式必须为YYYY-MM-DD'),
    end_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, '日期格式必须为YYYY-MM-DD')
})

export const validateDateRange = async (c: Context, next: Next) => {
    try {
        const query = c.req.query()
        const validated = dateRangeSchema.parse(query)
        
        // 验证日期逻辑合理性
        const startDate = new Date(validated.start_date)
        const endDate = new Date(validated.end_date)
        
        if (startDate > endDate) {
            return c.json({ error: '开始日期不能晚于结束日期' }, 400)
        }
        
        const daysDiff = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
        if (daysDiff > 365) {
            return c.json({ error: '查询范围不能超过365天' }, 400)
        }
        
        c.set('dateRange', validated)
        await next()
    } catch (error) {
        if (error instanceof z.ZodError) {
            return c.json({ 
                error: '参数验证失败', 
                details: error.errors 
            }, 400)
        }
        throw error
    }
}
```

**统一错误处理**:
```typescript
// src/middleware/errorHandler.ts
export const errorHandler = async (c: Context, next: Next) => {
    try {
        await next()
    } catch (error) {
        console.error('Request failed:', {
            url: c.req.url,
            method: c.req.method,
            error: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString()
        })
        
        if (error.name === 'ValidationError') {
            return c.json({ error: error.message }, 400)
        }
        
        if (error.name === 'DatabaseError') {
            return c.json({ error: '数据库操作失败' }, 500)
        }
        
        return c.json({ error: '服务器内部错误' }, 500)
    }
}
```

## 6. 部署和维护注意事项

### 6.1 ⚠️ Cloudflare部署流程

**标准部署流程**:
```bash
# 1. 后端部署 (必须先部署)
cd backend
npm run build
wrangler deploy

# 2. 数据库迁移 (如有schema变更)
wrangler d1 execute chunxue-prod-db --file=schema.sql

# 3. 前端构建和部署
cd frontend
npm run build
# Cloudflare Pages会自动部署 (通过Git集成)

# 4. 验证部署
curl "https://your-api.workers.dev/health"
curl "https://your-frontend.pages.dev"
```

**环境配置管理**:
```toml
# wrangler.toml
name = "spring-snow-api"
main = "src/index.ts"
compatibility_date = "2024-01-01"

[env.production]
vars = { ENVIRONMENT = "production" }

[[env.production.d1_databases]]
binding = "DB"
database_name = "chunxue-prod-db"
database_id = "your-database-id"

[env.development]
vars = { ENVIRONMENT = "development" }

[[env.development.d1_databases]]
binding = "DB"
database_name = "chunxue-dev-db"
database_id = "your-dev-database-id"
```

### 6.2 ⚠️ 生产环境监控

**关键监控指标**:
```typescript
// 在Worker中添加监控
app.use('*', async (c, next) => {
    const start = Date.now()
    
    await next()
    
    const duration = Date.now() - start
    
    // 记录性能指标
    console.log(JSON.stringify({
        timestamp: new Date().toISOString(),
        method: c.req.method,
        url: c.req.url,
        status: c.res.status,
        duration,
        userAgent: c.req.header('user-agent')
    }))
    
    // 慢查询警告
    if (duration > 5000) {
        console.warn(`Slow request: ${c.req.method} ${c.req.url} took ${duration}ms`)
    }
})
```

**数据库维护脚本**:
```bash
#!/bin/bash
# scripts/db-maintenance.sh

# 数据库备份
wrangler d1 export chunxue-prod-db --output backup-$(date +%Y%m%d).sql

# 数据完整性检查
wrangler d1 execute chunxue-prod-db --command="
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT product_id) as unique_products,
    MIN(record_date) as earliest_date,
    MAX(record_date) as latest_date
FROM DailyMetrics;
"

# 清理过期数据 (保留最近90天)
wrangler d1 execute chunxue-prod-db --command="
DELETE FROM DailyMetrics 
WHERE record_date < date('now', '-90 days');
"
```

### 6.3 ⚠️ 数据导入维护流程
**定期维护任务**:
```bash
# 每日数据导入和验证
cd data_import
python3 daily_import_main.py

# 如果需要单独验证（不导入）
python3 daily_import_main.py --verify-only

# 每周执行一次全面的数据完整性检查
python3 -c "from data_loader import run_full_validation; run_full_validation()"
```

## 7. 常见问题和解决方案 (FAQ)

### 7.1 ⚠️ 数据显示为空 (--) 或图表空白？
**排查清单**:
1.  **检查API响应**：使用 `curl` 或浏览器开发者工具检查相关API（如 `/api/inventory/top`）是否返回 `[]`。
    ```bash
    curl "http://localhost:8787/api/inventory/top?date=2025-07-28&limit=5"
    ```
2.  **检查数据库**：确认 `DailyMetrics` 表有数据，且 `inventory_level` > 0。
    ```bash
    wrangler d1 execute DB --command="SELECT COUNT(*) FROM DailyMetrics WHERE inventory_level > 0;"
    ```
3.  **检查产品分类**：确认 `Products` 表中 `category` 字段的状态。如果大多数为空，说明API过滤条件可能过严。
    ```bash
    wrangler d1 execute DB --command="SELECT category, COUNT(*) FROM Products GROUP BY category;"
    ```
**常见原因及解决方案**:
- **原因1：API过滤条件过严** → **解决方案**：修改API中的 `WHERE` 子句，允许 `category` 为 `NULL` 或空字符串（参考 [1.1.2](#112-api数据显示问题)）。
- **原因2：日期参数错误** → **解决方案**：确保前端传递的日期 `2025-07-28` 在数据库中有对应的库存数据。
- **原因3：数据库无数据** → **解决方案**：运行数据导入脚本，并检查日志确保成功。

### 7.2 ⚠️ 导入后销量、产量或库存数据异常？
**排查清单**:
1.  **运行验证脚本**：执行 `daily_import_main.py --verify-only`，检查报告的各项指标。
2.  **检查数据重复**：在数据库中检查是否存在 `(record_date, product_id)` 重复的记录。
    ```bash
    wrangler d1 execute DB --command="SELECT record_date, product_id, COUNT(*) FROM DailyMetrics GROUP BY record_date, product_id HAVING COUNT(*) > 1;"
    ```
3.  **检查单位转换**：确认代码中没有双重单位转换。数据应以 `kg` 存储。

**常见原因及解决方案**:
- **原因1：数据重复导入** → **解决方案**：检查 `prepare_daily_metrics_data` 中的字典合并逻辑是否正常工作。
- **原因2：单位转换错误** → **解决方案**：全局搜索代码，确保只有在前端显示时才将 `kg` 除以1000变为“吨”。
- **原因3：日期提取失败** → **解决方案**：检查源Excel文件的日期列名是否在 `data_processors.py` 的 `date_columns` 列表中。

### 7.3 ⚠️ 系统性能缓慢？
**排查清单**:
1.  **慢查询分析**：使用Cloudflare后台或日志检查API响应时间是否超标（> 5秒）。
2.  **数据库索引检查**：确保 `DailyMetrics` 表的核心索引（`idx_dailymetrics_date_product` 等）已创建。
3.  **API并发**：确认前端是否使用 `Promise.all` 来并行加载多个数据源。

**常见原因及解决方案**:
- **原因1：数据库查询未使用索引** → **解决方案**：使用 `EXPLAIN QUERY PLAN` 分析SQL，并创建合适的索引。
- **原因2：API串行请求** → **解决方案**：在前端 `onMounted` 或相关数据加载函数中，使用 `Promise.all` 并发获取数据。
- **原因3：数据量过大** → **解决方案**：为API增加缓存层（如Cloudflare KV），并为ECharts图表启用大数据量优化（`sampling`, `progressive`）。

### 7.4 ⚠️ 部署或环境配置问题？
**排查清单**:
1.  **环境变量**：检查 `wrangler.toml` 和前端的 `.env` 文件是否正确配置了API地址和数据库绑定。
2.  **CORS策略**：确认后端的CORS配置中包含了前端的访问地址（开发和生产环境）。
3.  **部署顺序**：务必先部署后端Worker，再部署前端应用。

**常见原因及解决方案**:
- **原因1：API 404错误** → **解决方案**：确认后端部署成功，且前端 `VITE_API_BASE_URL` 配置正确。
- **原因2：CORS跨域错误** → **解决方案**：在 `backend/src/index.ts` 的 `cors` 中间件里添加正确的源地址。
- **原因3：数据库连接失败** → **解决方案**：检查 `wrangler.toml` 中的 `d1_databases` 绑定是否正确，`database_id` 是否匹配。

### 7.5 ⚠️ 前端组件和状态管理问题？
**排查清单**:
1. **Vue组件响应性**：检查 `ref` 和 `reactive` 是否正确使用，数据更新是否触发视图更新。
2. **Pinia状态同步**：确认多个组件间的状态是否通过Store正确共享。
3. **路由守卫**：检查页面切换时数据是否正确加载和清理。

**常见原因及解决方案**:
- **原因1：数据未响应式更新** → **解决方案**：确保使用 `ref()` 包装基础类型，`reactive()` 包装对象。
- **原因2：Store状态丢失** → **解决方案**：检查是否正确使用 `defineStore`，并在组件中正确调用 `useXxxStore()`。
- **原因3：路由切换数据残留** → **解决方案**：在 `onUnmounted` 中清理定时器和事件监听器。

### 7.6 ⚠️ ECharts图表显示问题？
**排查清单**:
1. **容器尺寸**：确认图表容器有明确的宽高，且在DOM中可见。
2. **数据格式**：检查传入ECharts的数据格式是否符合要求。
3. **响应式调整**：确认窗口大小变化时图表是否正确调整。

**常见原因及解决方案**:
- **原因1：容器无尺寸** → **解决方案**：为图表容器设置明确的CSS尺寸，或使用 `height` prop。
- **原因2：数据格式错误** → **解决方案**：确保数据符合ECharts期望的格式，特别是时间序列数据。
- **原因3：图表未响应式** → **解决方案**：监听窗口 `resize` 事件，调用 `chart.resize()`。

## 8. 开发工具和调试技巧

### 8.1 ⚠️ 推荐的开发工具配置
**VS Code扩展**:
```json
{
    "recommendations": [
        "vue.volar",
        "bradlc.vscode-tailwindcss",
        "ms-vscode.vscode-typescript-next",
        "cloudflare.vscode-wrangler",
        "ms-python.python"
    ]
}
```

**调试配置**:
```json
// .vscode/launch.json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Debug Frontend",
            "type": "node",
            "request": "launch",
            "program": "${workspaceFolder}/frontend/node_modules/.bin/vite",
            "args": ["--mode", "development"],
            "console": "integratedTerminal"
        }
    ]
}
```

### 8.2 ⚠️ 数据库调试技巧
**常用查询**:
```sql
-- 检查数据分布
SELECT 
    record_date,
    COUNT(*) as records,
    SUM(sales_volume)/1000 as total_sales_tons,
    SUM(production_volume)/1000 as total_production_tons
FROM DailyMetrics 
WHERE record_date >= '2025-07-20'
GROUP BY record_date 
ORDER BY record_date;

-- 检查异常数据
SELECT product_name, record_date, sales_volume, production_volume
FROM DailyMetrics dm
JOIN Products p ON dm.product_id = p.product_id
WHERE sales_volume > 50000 OR production_volume > 50000
ORDER BY sales_volume DESC;

-- 检查产品分类分布
SELECT 
    CASE 
        WHEN category IS NULL THEN 'NULL'
        WHEN category = '' THEN 'EMPTY'
        ELSE category 
    END as category_status,
    COUNT(*) as count
FROM Products 
GROUP BY category_status;
```

### 8.3 ⚠️ API测试和验证
**测试脚本示例**:
```bash
#!/bin/bash
# scripts/api-test.sh

API_BASE="http://localhost:8787"
DATE="2025-07-28"

echo "=== API健康检查 ==="
curl -s "$API_BASE/health" | jq

echo -e "\n=== 库存数据测试 ==="
curl -s "$API_BASE/api/inventory/top?date=$DATE&limit=5" | jq

echo -e "\n=== 仪表板数据测试 ==="
curl -s "$API_BASE/api/dashboard/summary?start_date=2025-07-20&end_date=$DATE" | jq

echo -e "\n=== 产销率趋势测试 ==="
curl -s "$API_BASE/api/trends/ratio?start_date=2025-07-20&end_date=$DATE" | jq
```

## 9. 版本更新和迁移指南

### 9.1 ⚠️ 数据库Schema更新流程
```bash
# 1. 备份生产数据
wrangler d1 export chunxue-prod-db --output backup-before-migration.sql

# 2. 在开发环境测试迁移
wrangler d1 execute chunxue-dev-db --file=new-schema.sql

# 3. 验证迁移结果
wrangler d1 execute chunxue-dev-db --command="SELECT COUNT(*) FROM DailyMetrics;"

# 4. 生产环境迁移
wrangler d1 execute chunxue-prod-db --file=new-schema.sql

# 5. 验证生产数据
wrangler d1 execute chunxue-prod-db --command="SELECT COUNT(*) FROM DailyMetrics;"
```

### 9.2 ⚠️ 依赖更新注意事项
**前端依赖更新**:
```bash
# 检查过期依赖
npm outdated

# 更新非破坏性版本
npm update

# 主要依赖更新需要测试
npm install vue@latest
npm install @vue/router@latest
npm install pinia@latest
```

**后端依赖更新**:
```bash
# Hono框架更新
npm install hono@latest

# Wrangler CLI更新
npm install -g wrangler@latest

# 检查兼容性
wrangler --version
```

## 10. 总结和最佳实践

### 10.1 ⚠️ 开发流程最佳实践
1. **数据导入优先**：每次开发前确保有最新的测试数据
2. **API优先开发**：先实现和测试API，再开发前端界面
3. **增量开发**：每次只修改一个功能模块，避免大范围改动
4. **及时测试**：每次修改后立即测试相关功能
5. **文档同步**：代码修改后及时更新相关文档

### 10.2 ⚠️ 代码质量保证
```bash
# 前端代码检查
npm run lint
npm run type-check

# 后端代码检查
npm run build
wrangler dev --local

# 数据导入验证
python3 daily_import_main.py --verify-only
```

### 10.3 ⚠️ 生产环境检查清单
- [ ] 所有API端点正常响应
- [ ] 数据库索引已创建
- [ ] CORS配置正确
- [ ] 环境变量已设置
- [ ] 监控和日志已配置
- [ ] 备份策略已实施
- [ ] 性能指标在正常范围内

---

**重要提醒**: 本文档基于实际项目开发经验总结，包含了2025年7月29日数据导入系统优化的最新内容。建议开发者在修改系统前仔细阅读相关章节，特别是数据显示问题和数据导入优化部分，避免重复遇到已知问题。

**文档维护**: 请在遇到新问题或找到更好解决方案时及时更新本文档，保持内容的准确性和实用性。


---
**文档版本**: v1.0
**最后更新**: 2025-07-30
**负责人**: AI Assistant
