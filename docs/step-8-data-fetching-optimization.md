# 步骤八：优化数据获取流程

## 目标和背景

### 目标
修改 `Dashboard.vue` 和其他视图组件的 `onMounted` 生命周期钩子，建立集中的数据初始化机制，消除冗余的API请求，提升应用性能和用户体验。

### 背景
当前前端应用中组件在挂载时会触发多个Store的action，导致并行的、冗余的API请求。这种分散的数据获取方式带来了以下问题：

1. **网络资源浪费**：同一时间发起多个相同的API请求
2. **服务器压力**：并发请求增加后端负载
3. **用户体验差**：多个loading状态，页面闪烁
4. **数据竞态条件**：并发请求可能导致数据不一致
5. **缓存效率低**：缺乏统一的缓存策略

### 现状分析
通过代码审查发现的主要问题：
- 多个组件同时在 `onMounted` 中调用相同的API
- 缺乏全局的数据初始化协调机制
- 没有有效的请求去重和缓存机制
- 组件间缺乏数据获取的时序控制

## 具体实施步骤

### 第一步：分析现有数据获取模式
1. **审计组件的数据获取**
   ```bash
   # 查找所有onMounted中的数据获取
   grep -r "onMounted.*fetch\|onMounted.*load" frontend/src/
   
   # 分析Store action的调用
   grep -r "\.fetch\|\.load\|\.get" frontend/src/components/
   ```

2. **识别重复请求**
   - 相同API的多次调用
   - 组件间的数据依赖关系
   - 数据获取的时序要求

3. **分析性能瓶颈**
   - 网络请求的并发数量
   - 数据加载的总时间
   - 用户感知的加载体验

### 第二步：设计集中化数据获取架构
1. **全局数据管理器**
   - 统一的数据初始化入口
   - 请求去重和缓存机制
   - 错误处理和重试策略

2. **组件数据依赖声明**
   - 明确组件需要的数据类型
   - 数据获取的优先级
   - 依赖关系的管理

### 第三步：实现优化的数据获取机制
1. **创建全局数据管理器**
2. **重构组件的数据获取逻辑**
3. **实现智能缓存和更新策略**

### 第四步：优化用户体验
1. **统一的加载状态管理**
2. **渐进式数据加载**
3. **错误处理和重试机制**

## 代码示例

### 修改前的问题代码
```vue
<!-- Dashboard.vue - 问题版本 -->
<template>
  <div class="dashboard">
    <div v-if="isLoading">加载中...</div>
    <!-- 仪表板内容 -->
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useDashboardStore } from '@/stores/dashboard';
import { useProductionStore } from '@/stores/production';
import { useInventoryStore } from '@/stores/inventory';

const dashboardStore = useDashboardStore();
const productionStore = useProductionStore();
const inventoryStore = useInventoryStore();

const isLoading = ref(true);

// 问题：多个Store同时获取数据，可能重复请求
onMounted(async () => {
  try {
    // 并行请求，可能导致重复的API调用
    await Promise.all([
      dashboardStore.loadDashboardData(),
      productionStore.fetchRatioData(),
      inventoryStore.fetchInventoryData()
    ]);
  } catch (error) {
    console.error('数据加载失败:', error);
  } finally {
    isLoading.value = false;
  }
});
</script>

<!-- Production.vue - 问题版本 -->
<script setup>
import { onMounted } from 'vue';
import { useProductionStore } from '@/stores/production';

const productionStore = useProductionStore();

// 问题：与Dashboard重复请求相同数据
onMounted(async () => {
  await productionStore.fetchRatioData(); // 重复请求！
});
</script>
```

### 修改后的优化方案

#### 1. 全局数据管理器
```javascript
// composables/useGlobalDataManager.js
import { ref, reactive, computed } from 'vue';
import { useProductionStore } from '@/stores/production';
import { useInventoryStore } from '@/stores/inventory';
import { useSalesStore } from '@/stores/sales';

// 全局状态
const globalState = reactive({
  isInitialized: false,
  isLoading: false,
  error: null,
  lastInitTime: null,
  initPromise: null // 用于防止重复初始化
});

// 数据获取配置
const dataConfig = reactive({
  cacheTimeout: 5 * 60 * 1000, // 5分钟缓存
  retryAttempts: 3,
  retryDelay: 1000,
  batchSize: 3 // 批量请求大小
});

// 请求队列管理
const requestQueue = reactive({
  pending: new Set(),
  completed: new Set(),
  failed: new Set()
});

export function useGlobalDataManager() {
  const productionStore = useProductionStore();
  const inventoryStore = useInventoryStore();
  const salesStore = useSalesStore();
  
  // 计算属性
  const isDataStale = computed(() => {
    if (!globalState.lastInitTime) return true;
    return Date.now() - globalState.lastInitTime > dataConfig.cacheTimeout;
  });
  
  const loadingProgress = computed(() => {
    const total = requestQueue.pending.size + requestQueue.completed.size + requestQueue.failed.size;
    if (total === 0) return 0;
    return (requestQueue.completed.size / total) * 100;
  });
  
  // 请求去重装饰器
  const deduplicateRequest = (key, requestFn) => {
    return async (...args) => {
      if (requestQueue.pending.has(key)) {
        console.log(`⏳ 请求去重: ${key}`);
        // 等待正在进行的请求完成
        while (requestQueue.pending.has(key)) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
        return;
      }
      
      requestQueue.pending.add(key);
      
      try {
        const result = await requestFn(...args);
        requestQueue.completed.add(key);
        return result;
      } catch (error) {
        requestQueue.failed.add(key);
        throw error;
      } finally {
        requestQueue.pending.delete(key);
      }
    };
  };
  
  // 带重试的请求执行器
  const executeWithRetry = async (requestFn, key, maxRetries = dataConfig.retryAttempts) => {
    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔄 执行请求: ${key} (尝试 ${attempt}/${maxRetries})`);
        return await requestFn();
      } catch (error) {
        lastError = error;
        console.warn(`⚠️ 请求失败: ${key} (尝试 ${attempt}/${maxRetries})`, error);
        
        if (attempt < maxRetries) {
          const delay = dataConfig.retryDelay * Math.pow(2, attempt - 1); // 指数退避
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    throw lastError;
  };
  
  // 核心初始化方法
  const initializeGlobalData = async (options = {}) => {
    const { 
      forceRefresh = false, 
      priority = 'normal',
      components = ['production', 'inventory', 'sales']
    } = options;
    
    // 防止重复初始化
    if (globalState.initPromise && !forceRefresh) {
      console.log('⏳ 等待现有初始化完成...');
      return await globalState.initPromise;
    }
    
    // 检查缓存
    if (!forceRefresh && globalState.isInitialized && !isDataStale.value) {
      console.log('✅ 使用缓存数据，跳过初始化');
      return;
    }
    
    // 创建初始化Promise
    globalState.initPromise = performInitialization(components, forceRefresh);
    
    try {
      await globalState.initPromise;
    } finally {
      globalState.initPromise = null;
    }
  };
  
  // 执行实际的初始化
  const performInitialization = async (components, forceRefresh) => {
    globalState.isLoading = true;
    globalState.error = null;
    
    // 清空请求队列
    requestQueue.pending.clear();
    requestQueue.completed.clear();
    requestQueue.failed.clear();
    
    try {
      console.log('🚀 开始全局数据初始化...');
      
      // 定义数据获取任务
      const tasks = [];
      
      if (components.includes('production')) {
        tasks.push({
          key: 'production-data',
          fn: deduplicateRequest('production-data', () => 
            executeWithRetry(() => productionStore.fetchAllProductionData({ forceRefresh }), 'production-data')
          ),
          priority: 1
        });
      }
      
      if (components.includes('inventory')) {
        tasks.push({
          key: 'inventory-data',
          fn: deduplicateRequest('inventory-data', () => 
            executeWithRetry(() => inventoryStore.fetchInventoryData({ forceRefresh }), 'inventory-data')
          ),
          priority: 2
        });
      }
      
      if (components.includes('sales')) {
        tasks.push({
          key: 'sales-data',
          fn: deduplicateRequest('sales-data', () => 
            executeWithRetry(() => salesStore.fetchSalesData({ forceRefresh }), 'sales-data')
          ),
          priority: 3
        });
      }
      
      // 按优先级排序
      tasks.sort((a, b) => a.priority - b.priority);
      
      // 分批执行任务
      const batches = [];
      for (let i = 0; i < tasks.length; i += dataConfig.batchSize) {
        batches.push(tasks.slice(i, i + dataConfig.batchSize));
      }
      
      for (const batch of batches) {
        console.log(`📦 执行批次: ${batch.map(t => t.key).join(', ')}`);
        await Promise.all(batch.map(task => task.fn()));
      }
      
      // 更新全局状态
      globalState.isInitialized = true;
      globalState.lastInitTime = Date.now();
      
      console.log('✅ 全局数据初始化完成');
      
    } catch (error) {
      console.error('❌ 全局数据初始化失败:', error);
      globalState.error = error.message;
      throw error;
    } finally {
      globalState.isLoading = false;
    }
  };
  
  // 刷新特定数据
  const refreshData = async (dataTypes = ['production', 'inventory', 'sales']) => {
    return await initializeGlobalData({ 
      forceRefresh: true, 
      components: dataTypes 
    });
  };
  
  // 预加载数据
  const preloadData = async (components) => {
    if (globalState.isLoading) return;
    
    console.log('🔮 预加载数据:', components);
    return await initializeGlobalData({ 
      forceRefresh: false, 
      components,
      priority: 'low' 
    });
  };
  
  // 清除缓存
  const clearCache = () => {
    globalState.isInitialized = false;
    globalState.lastInitTime = null;
    globalState.error = null;
    
    // 清除各Store的缓存
    productionStore.clearCache();
    inventoryStore.clearCache();
    salesStore.clearCache();
    
    console.log('🗑️ 全局缓存已清除');
  };
  
  // 获取初始化状态
  const getInitializationStatus = () => ({
    isInitialized: globalState.isInitialized,
    isLoading: globalState.isLoading,
    error: globalState.error,
    lastInitTime: globalState.lastInitTime,
    isDataStale: isDataStale.value,
    loadingProgress: loadingProgress.value,
    pendingRequests: Array.from(requestQueue.pending),
    completedRequests: Array.from(requestQueue.completed),
    failedRequests: Array.from(requestQueue.failed)
  });
  
  return {
    // 状态
    globalState: readonly(globalState),
    isDataStale,
    loadingProgress,
    
    // 方法
    initializeGlobalData,
    refreshData,
    preloadData,
    clearCache,
    getInitializationStatus
  };
}
```

#### 2. 组件数据依赖声明
```javascript
// composables/useComponentData.js
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { useGlobalDataManager } from './useGlobalDataManager';

export function useComponentData(config = {}) {
  const {
    dependencies = [], // 组件依赖的数据类型
    autoLoad = true,    // 是否自动加载
    loadOnVisible = false, // 是否在可见时加载
    refreshInterval = 0,   // 自动刷新间隔（毫秒）
    priority = 'normal'    // 加载优先级
  } = config;
  
  const { initializeGlobalData, globalState } = useGlobalDataManager();
  
  const componentState = ref({
    isReady: false,
    error: null,
    lastLoad: null
  });
  
  let refreshTimer = null;
  let visibilityObserver = null;
  
  // 加载组件数据
  const loadComponentData = async (forceRefresh = false) => {
    try {
      componentState.value.error = null;
      
      await initializeGlobalData({
        forceRefresh,
        priority,
        components: dependencies
      });
      
      componentState.value.isReady = true;
      componentState.value.lastLoad = Date.now();
      
    } catch (error) {
      console.error('组件数据加载失败:', error);
      componentState.value.error = error.message;
      throw error;
    }
  };
  
  // 设置自动刷新
  const setupAutoRefresh = () => {
    if (refreshInterval > 0) {
      refreshTimer = setInterval(async () => {
        try {
          await loadComponentData();
        } catch (error) {
          console.error('自动刷新失败:', error);
        }
      }, refreshInterval);
    }
  };
  
  // 设置可见性检测
  const setupVisibilityObserver = (element) => {
    if (!loadOnVisible || !element) return;
    
    visibilityObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting && !componentState.value.isReady) {
            loadComponentData();
          }
        });
      },
      { threshold: 0.1 }
    );
    
    visibilityObserver.observe(element);
  };
  
  // 清理资源
  const cleanup = () => {
    if (refreshTimer) {
      clearInterval(refreshTimer);
      refreshTimer = null;
    }
    
    if (visibilityObserver) {
      visibilityObserver.disconnect();
      visibilityObserver = null;
    }
  };
  
  // 监听全局状态变化
  watch(() => globalState.isInitialized, (isInitialized) => {
    if (isInitialized && dependencies.length > 0) {
      componentState.value.isReady = true;
    }
  });
  
  return {
    componentState: readonly(componentState),
    loadComponentData,
    setupAutoRefresh,
    setupVisibilityObserver,
    cleanup
  };
}
```

#### 3. 重构后的组件实现
```vue
<!-- Dashboard.vue - 重构版本 -->
<template>
  <div class="dashboard" ref="dashboardRef">
    <!-- 全局加载状态 -->
    <div v-if="globalState.isLoading" class="global-loading">
      <el-progress 
        :percentage="loadingProgress" 
        :status="globalState.error ? 'exception' : 'success'"
      />
      <p>{{ loadingText }}</p>
    </div>
    
    <!-- 仪表板内容 -->
    <div v-else-if="componentState.isReady" class="dashboard-content">
      <!-- 产销比卡片 -->
      <ProductionRatioCard />
      
      <!-- 库存概览卡片 -->
      <InventoryOverviewCard />
      
      <!-- 销售趋势卡片 -->
      <SalesTrendCard />
      
      <!-- 其他仪表板组件... -->
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="componentState.error" class="error-state">
      <el-alert
        :title="componentState.error"
        type="error"
        show-icon
        :closable="false"
      />
      <el-button @click="retryLoad" type="primary">重试</el-button>
    </div>
    
    <!-- 控制面板 -->
    <div class="dashboard-controls">
      <el-button 
        @click="refreshData" 
        :loading="globalState.isLoading"
        icon="Refresh"
      >
        刷新数据
      </el-button>
      
      <el-button 
        @click="clearCache"
        icon="Delete"
      >
        清除缓存
      </el-button>
      
      <span class="last-update">
        最后更新: {{ formatTime(globalState.lastInitTime) }}
      </span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useGlobalDataManager } from '@/composables/useGlobalDataManager';
import { useComponentData } from '@/composables/useComponentData';

// 组件引用
const dashboardRef = ref(null);

// 使用全局数据管理器
const { 
  globalState, 
  loadingProgress, 
  refreshData: globalRefreshData,
  clearCache: globalClearCache 
} = useGlobalDataManager();

// 使用组件数据管理
const { 
  componentState, 
  loadComponentData,
  setupAutoRefresh,
  cleanup 
} = useComponentData({
  dependencies: ['production', 'inventory', 'sales'],
  autoLoad: true,
  refreshInterval: 5 * 60 * 1000, // 5分钟自动刷新
  priority: 'high'
});

// 计算属性
const loadingText = computed(() => {
  if (globalState.error) return '加载失败';
  if (loadingProgress.value === 100) return '加载完成';
  return `加载中... ${Math.round(loadingProgress.value)}%`;
});

// 方法
const retryLoad = async () => {
  try {
    await loadComponentData(true);
  } catch (error) {
    console.error('重试失败:', error);
  }
};

const refreshData = async () => {
  try {
    await globalRefreshData();
  } catch (error) {
    console.error('刷新失败:', error);
  }
};

const clearCache = () => {
  globalClearCache();
};

const formatTime = (timestamp) => {
  if (!timestamp) return '从未';
  return new Date(timestamp).toLocaleTimeString();
};

// 生命周期
onMounted(async () => {
  try {
    // 自动加载已在useComponentData中处理
    setupAutoRefresh();
  } catch (error) {
    console.error('仪表板初始化失败:', error);
  }
});

onUnmounted(() => {
  cleanup();
});
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.global-loading {
  text-align: center;
  padding: 40px;
}

.dashboard-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.error-state {
  text-align: center;
  padding: 40px;
}

.dashboard-controls {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 8px;
}

.last-update {
  color: #666;
  font-size: 12px;
  margin-left: auto;
}
</style>
```

#### 4. 路由级别的数据预加载
```javascript
// router/index.js
import { createRouter, createWebHistory } from 'vue-router';
import { useGlobalDataManager } from '@/composables/useGlobalDataManager';

const routes = [
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: {
      requiresData: ['production', 'inventory', 'sales'],
      preload: true
    }
  },
  {
    path: '/production',
    name: 'Production',
    component: () => import('@/views/Production.vue'),
    meta: {
      requiresData: ['production'],
      preload: false
    }
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// 路由守卫 - 数据预加载
router.beforeEach(async (to, from, next) => {
  const { initializeGlobalData, preloadData } = useGlobalDataManager();
  
  if (to.meta.requiresData) {
    try {
      if (to.meta.preload) {
        // 预加载数据
        await preloadData(to.meta.requiresData);
      } else {
        // 确保数据已初始化
        await initializeGlobalData({
          components: to.meta.requiresData
        });
      }
    } catch (error) {
      console.error('路由数据预加载失败:', error);
      // 继续导航，让组件处理错误
    }
  }
  
  next();
});

export default router;
```

## 验证方法

### 性能测试
```javascript
// tests/performance/dataFetching.test.js
describe('数据获取性能', () => {
  test('应该减少重复的API请求', async () => {
    let apiCallCount = 0;
    
    // Mock fetch to count API calls
    const originalFetch = global.fetch;
    global.fetch = jest.fn((...args) => {
      if (args[0].includes('/api/')) {
        apiCallCount++;
      }
      return originalFetch(...args);
    });
    
    const { initializeGlobalData } = useGlobalDataManager();
    
    // 模拟多个组件同时请求数据
    await Promise.all([
      initializeGlobalData({ components: ['production'] }),
      initializeGlobalData({ components: ['production'] }),
      initializeGlobalData({ components: ['production'] })
    ]);
    
    // 应该只有一次API调用
    expect(apiCallCount).toBeLessThanOrEqual(3);
    
    global.fetch = originalFetch;
  });
  
  test('缓存应该正常工作', async () => {
    const { initializeGlobalData, globalState } = useGlobalDataManager();
    
    // 第一次加载
    await initializeGlobalData();
    const firstLoadTime = globalState.lastInitTime;
    
    // 第二次加载（应该使用缓存）
    await initializeGlobalData();
    const secondLoadTime = globalState.lastInitTime;
    
    expect(firstLoadTime).toBe(secondLoadTime);
  });
});
```

### 用户体验测试
```javascript
// 测试加载状态的连续性
test('加载状态应该连续且友好', async () => {
  const { initializeGlobalData, globalState, loadingProgress } = useGlobalDataManager();
  
  const loadingStates = [];
  
  // 监听加载状态变化
  const unwatch = watch([() => globalState.isLoading, loadingProgress], 
    ([isLoading, progress]) => {
      loadingStates.push({ isLoading, progress, timestamp: Date.now() });
    }
  );
  
  await initializeGlobalData();
  
  unwatch();
  
  // 验证加载状态的连续性
  expect(loadingStates[0].isLoading).toBe(true);
  expect(loadingStates[loadingStates.length - 1].isLoading).toBe(false);
  expect(loadingStates[loadingStates.length - 1].progress).toBe(100);
});
```

## 注意事项

### 性能优化
1. **请求去重**：确保相同请求不会重复发起
2. **批量处理**：合理控制并发请求数量
3. **缓存策略**：平衡数据新鲜度和性能

### 用户体验
1. **加载反馈**：提供清晰的加载进度和状态
2. **错误处理**：友好的错误提示和重试机制
3. **渐进加载**：优先加载关键数据

### 系统稳定性
1. **错误隔离**：单个请求失败不影响其他数据
2. **重试机制**：网络问题时的自动重试
3. **降级策略**：数据加载失败时的备用方案

## 预期结果

### 性能提升
1. **API请求减少**：重复请求减少80%以上
2. **页面加载时间**：初始加载时间减少50%
3. **网络流量优化**：减少不必要的数据传输

### 用户体验改善
1. **加载体验**：统一的加载状态，减少页面闪烁
2. **响应速度**：缓存命中时的即时响应
3. **错误处理**：更友好的错误提示和恢复机制

### 系统稳定性增强
1. **并发控制**：避免过多并发请求导致的系统压力
2. **错误恢复**：自动重试和降级机制
3. **资源管理**：更好的内存和网络资源利用

## 后续优化建议

1. **Service Worker**：实现离线缓存和后台同步
2. **GraphQL**：考虑使用GraphQL减少过度获取
3. **实时更新**：集成WebSocket实现数据实时同步
4. **智能预测**：基于用户行为预测和预加载数据
