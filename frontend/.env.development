# 开发环境配置
NODE_ENV=development

# API配置 - 使用本地API进行开发测试
VITE_API_BASE_URL=http://localhost:8787
VITE_API_TIMEOUT=10000

# 应用配置
VITE_APP_TITLE=春雪食品分析系统
VITE_APP_VERSION=2.0.0
VITE_APP_DESCRIPTION=Spring Snow Food Analysis System

# 功能开关
VITE_ENABLE_MOCK=true
VITE_ENABLE_DEBUG=true
VITE_ENABLE_PERFORMANCE_MONITOR=true
VITE_ENABLE_ERROR_REPORTING=false

# 图表配置
VITE_CHART_ANIMATION=true
VITE_CHART_THEME=default

# 缓存配置
VITE_CACHE_ENABLED=true
VITE_CACHE_DURATION=300000

# 日志级别
VITE_LOG_LEVEL=debug
