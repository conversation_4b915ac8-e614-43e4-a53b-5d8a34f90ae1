<template>
  <div class="dashboard-page">
    <!-- 快速统计卡片 -->
    <div class="metrics-grid">
      <MetricCard
        title="产品总数"
        :value="dashboardStore.totalProducts"
        unit="个"
        icon="📦"
        variant="primary"
        :loading="dashboardStore.isLoading"
      />
      <MetricCard
        title="总销量"
        :value="dashboardStore.totalSales"
        unit="T"
        icon="📈"
        variant="secondary"
        :loading="dashboardStore.isLoading"
        :sparkline-data="salesSparklineData"
      />
      <MetricCard
        title="库存总量"
        :value="dashboardStore.totalInventory"
        unit="T"
        icon="📊"
        variant="tertiary"
        :loading="dashboardStore.isLoading"
      />
      <MetricCard
        title="平均单价"
        :value="dashboardStore.averagePrice"
        :format="(value) => formatPrice(value, '元/T', false)"
        unit="元/T"
        icon="💰"
        variant="accent"
        :loading="dashboardStore.isLoading"
      />
      <MetricCard
        title="平均产销率"
        :value="productionStore.averageRatio"
        unit="%"
        icon="🏭"
        variant="primary"
        :loading="productionStore.isLoading"
        :format="(value) => formatPercentage(value, 1, false)"
        @click="debugProductionRatio"
      />
    </div>

    <!-- 主要图表区域 -->
    <div class="charts-grid">
      <!-- 销售价格趋势图 -->
      <div class="chart-section">
        <SalesPriceChart
          :start-date="dashboardStore.dateRange.start"
          :end-date="dashboardStore.dateRange.end"
          @data-updated="handleSalesDataUpdate"
        />
      </div>

      <!-- 库存TOP15 -->
      <div class="chart-section">
        <InventoryTopChart
          :date="dashboardStore.dateRange.end"
          @data-updated="handleInventoryDataUpdate"
        />
      </div>
    </div>

    <!-- 次要图表区域 -->
    <div class="secondary-charts">
      <!-- 产销率趋势 -->
      <div class="chart-section">
        <ProductionRatioChart
          :start-date="dashboardStore.dateRange.start"
          :end-date="dashboardStore.dateRange.end"
        />
      </div>
    </div>

    <!-- 数据刷新控制 -->
    <div class="dashboard-controls">
      <div class="date-range-selector">
        <label>数据范围：</label>
        <input
          v-model="startDate"
          type="date"
          @change="updateDateRange"
        />
        <span>至</span>
        <input
          v-model="endDate"
          type="date"
          @change="updateDateRange"
        />
      </div>
      
      <BaseButton
        @click="refreshAllData"
        :loading="dashboardStore.isLoading"
        variant="primary"
        size="small"
        icon="🔄"
      >
        刷新数据
      </BaseButton>
    </div>

    <!-- 错误提示 -->
    <div v-if="dashboardStore.error" class="error-message">
      <div class="error-content">
        <span class="error-icon">⚠️</span>
        <span class="error-text">{{ dashboardStore.error }}</span>
        <BaseButton
          @click="dashboardStore.clearError"
          variant="ghost"
          size="small"
        >
          关闭
        </BaseButton>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useDashboardStore } from '@/stores/dashboard'
import { useProductionStore } from '@/stores/production'
import { formatInteger, formatPercentage, formatPrice } from '@/utils/formatters'
import MetricCard from '@/components/dashboard/MetricCard.vue'
import SalesPriceChart from '@/components/charts/SalesPriceChart.vue'
import InventoryTopChart from '@/components/inventory/InventoryTopChart.vue'
import ProductionRatioChart from '@/components/production/ProductionRatioChart.vue'
import BaseButton from '@/components/common/BaseButton.vue'

const dashboardStore = useDashboardStore()
const productionStore = useProductionStore()

// 本地状态
const startDate = ref(dashboardStore.dateRange.start)
const endDate = ref(dashboardStore.dateRange.end)
const salesSparklineData = ref([])

// 计算属性
const isLoading = computed(() => dashboardStore.isLoading)


// 方法
const updateDateRange = async () => {
  try {
    await dashboardStore.updateDateRange(startDate.value, endDate.value)
  } catch (error) {
    console.error('Failed to update date range:', error)
  }
}

const refreshAllData = async () => {
  try {
    await dashboardStore.refreshData()
  } catch (error) {
    console.error('Failed to refresh dashboard data:', error)
  }
}

const handleSalesDataUpdate = (data) => {
  // 更新销量sparkline数据
  salesSparklineData.value = data.slice(-7).map(item => item.volume)
}

const handleInventoryDataUpdate = (data) => {
  console.log('Inventory data updated:', data.length, 'items')
}

const debugProductionRatio = () => {
  console.log('🔍 [Dashboard] 产销率调试信息:', {
    productionStoreRatio: productionStore.averageRatio,
    productionStoreStats: productionStore.ratioStats,
    dashboardStoreRatio: dashboardStore.productionSalesRatio,
    timestamp: new Date().toISOString()
  })
}

// 生命周期
onMounted(async () => {
  try {
    console.log('🔄 [Dashboard] 组件挂载，开始加载数据...')

    // 强制刷新数据以确保最新状态
    await Promise.all([
      dashboardStore.fetchSummaryData(),
      productionStore.fetchAllProductionData()
    ])

    console.log('✅ [Dashboard] 数据加载完成:', {
      productionRatio: productionStore.averageRatio,
      dashboardRatio: dashboardStore.productionSalesRatio,
      isConsistent: Math.abs(productionStore.averageRatio - dashboardStore.productionSalesRatio) < 0.1,
      timestamp: new Date().toISOString()
    })

    // 如果数据不一致，输出警告
    if (Math.abs(productionStore.averageRatio - dashboardStore.productionSalesRatio) > 0.1) {
      console.warn('⚠️ [Dashboard] 检测到产销率数据不一致！')
      debugProductionRatio()
    }

  } catch (error) {
    console.error('❌ [Dashboard] 数据加载失败:', error)
  }
})
</script>

<style scoped>
.dashboard-page {
  padding: 0;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.secondary-charts {
  margin-bottom: 32px;
}

.chart-section {
  min-height: 400px;
}

.dashboard-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: var(--background-light);
  border-radius: 12px;
  margin-bottom: 20px;
}

.date-range-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.9em;
}

.date-range-selector label {
  color: var(--text-secondary);
  font-weight: 500;
}

.date-range-selector input {
  padding: 6px 10px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.9em;
}

.date-range-selector input:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.error-message {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.error-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #fee;
  color: var(--signal-red);
  border: 1px solid #fcc;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.error-icon {
  font-size: 1.2em;
}

.error-text {
  font-size: 0.9em;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .dashboard-controls {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .date-range-selector {
    justify-content: center;
    flex-wrap: wrap;
  }
}
</style>
