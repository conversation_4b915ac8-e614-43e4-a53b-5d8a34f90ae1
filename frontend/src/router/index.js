import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// 路由组件懒加载
const Dashboard = () => import('@/views/Dashboard.vue')
const Realtime = () => import('@/views/Realtime.vue')
const Inventory = () => import('@/views/Inventory.vue')
const Production = () => import('@/views/Production.vue')
const Sales = () => import('@/views/Sales.vue')
const Details = () => import('@/views/Details.vue')
const Pricing = () => import('@/views/Pricing.vue')
const PriceMonitoring = () => import('@/views/PriceMonitoring.vue')
const PriceMonitoringDashboard = () => import('@/views/PriceMonitoringDashboard.vue')
const InventoryTurnover = () => import('@/views/InventoryTurnover.vue')
const News = () => import('@/views/News.vue')
const PriceAlerts = () => import('@/views/PriceAlerts.vue')
 
 const routes = [
   {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: {
      title: '分析摘要',
      icon: '📊',
      description: '系统总览和关键指标展示',
      requiresAuth: true
    }
  },
  {
    path: '/realtime',
    name: 'Realtime',
    component: Realtime,
    meta: {
      title: '实时分析',
      icon: '⚡',
      description: '实时数据监控和分析',
      requiresAuth: true
    }
  },
  {
    path: '/inventory',
    name: 'Inventory',
    component: Inventory,
    meta: {
      title: '库存情况',
      icon: '📦',
      description: '库存数据分析和管理',
      requiresAuth: true
    }
  },
  {
    path: '/production',
    name: 'Production',
    component: Production,
    meta: {
      title: '产销率分析',
      icon: '🏭',
      description: '生产与销售比率分析',
      requiresAuth: true
    }
  },
  {
    path: '/sales',
    name: 'Sales',
    component: Sales,
    meta: {
      title: '销售情况',
      icon: '📈',
      description: '销售数据分析和趋势',
      requiresAuth: true
    }
  },
  {
    path: '/details',
    name: 'Details',
    component: Details,
    meta: {
      title: '详细数据',
      icon: '📋',
      description: '详细数据查询和导出',
      requiresAuth: true
    }
  },
  {
    path: '/pricing',
    name: 'Pricing',
    component: Pricing,
    meta: {
      title: '价格波动',
      icon: '💰',
      description: '价格趋势分析和预测',
      requiresAuth: true
    }
  },
  {
    path: '/price-monitoring',
    name: 'PriceMonitoring',
    component: PriceMonitoring,
    meta: {
      title: '价格监控',
      icon: '📊',
      description: '产品价格波动监控和预警',
      requiresAuth: true
    }
  },
  {
    path: '/price-monitoring-dashboard',
    name: 'PriceMonitoringDashboard',
    component: PriceMonitoringDashboard,
    meta: {
      title: '价格监控面板',
      icon: '📈',
      description: '完整的价格监控功能面板',
      requiresAuth: true
    }
  },
  {
    path: '/inventory-turnover',
    name: 'InventoryTurnover',
    component: InventoryTurnover,
    meta: {
      title: '库存周转',
      icon: '🔄',
      description: '库存周转率分析',
      requiresAuth: true
    }
  },
  {
    path: '/news',
    name: 'News',
    component: News,
    meta: {
      title: '桌创资讯',
      icon: '📰',
      description: '行业资讯和公司动态',
      requiresAuth: true
    }
  },
  {
    path: '/price-alerts',
    name: 'PriceAlerts',
    component: PriceAlerts,
    meta: {
      title: '预警管理',
      icon: '🔔',
      description: '管理价格预警规则',
      requiresAuth: true
    }
  },
  {
    path: '/test',
    name: 'Test',
    component: () => import('@/views/TestPage.vue'),
    meta: {
      title: '系统测试',
      icon: '🧪',
      description: '系统功能测试页面',
      requiresAuth: false
    }
  },
  {
    path: '/production-test',
    name: 'ProductionTest',
    component: () => import('@/views/ProductionTest.vue'),
    meta: {
      title: '产销率测试',
      icon: '🔬',
      description: '产销率功能测试页面',
      requiresAuth: false
    }
  },
  {
    path: '/date-range-test',
    name: 'DateRangeTest',
    component: () => import('@/views/DateRangeTest.vue'),
    meta: {
      title: '日期范围测试',
      icon: '📅',
      description: '动态日期范围功能测试页面',
      requiresAuth: false
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面未找到',
      requiresAuth: false
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 页面切换时滚动到顶部
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 春雪食品分析系统`
  }

  // 检查认证状态
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    // 需要认证但未登录，显示登录模态框
    authStore.showAuthModal = true
    next(false)
    return
  }

  // 页面加载进度指示
  if (typeof window !== 'undefined') {
    // 显示加载指示器
    const loadingIndicator = document.getElementById('page-loading')
    if (loadingIndicator) {
      loadingIndicator.style.display = 'block'
    }
  }

  next()
})

router.afterEach((to, from) => {
  // 隐藏加载指示器
  if (typeof window !== 'undefined') {
    setTimeout(() => {
      const loadingIndicator = document.getElementById('page-loading')
      if (loadingIndicator) {
        loadingIndicator.style.display = 'none'
      }
    }, 300)
  }

  // 页面切换动画完成后的处理
  console.log(`✅ Navigated from ${from.path} to ${to.path}`)
})

// 路由错误处理
router.onError((error) => {
  console.error('❌ Router error:', error)

  // 可以在这里添加错误上报逻辑
  if (typeof window !== 'undefined') {
    // 显示错误提示
    const errorMessage = document.createElement('div')
    errorMessage.className = 'router-error-message'
    errorMessage.textContent = '页面加载失败，请刷新重试'
    errorMessage.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #fee;
      color: #c41e3a;
      padding: 12px 16px;
      border-radius: 8px;
      border: 1px solid #fcc;
      z-index: 9999;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    `

    document.body.appendChild(errorMessage)

    // 3秒后自动移除
    setTimeout(() => {
      if (document.body.contains(errorMessage)) {
        document.body.removeChild(errorMessage)
      }
    }, 3000)
  }
})

export default router
