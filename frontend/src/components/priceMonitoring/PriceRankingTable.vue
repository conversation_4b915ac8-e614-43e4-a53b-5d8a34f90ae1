<template>
  <div class="price-ranking-table">
    <div class="table-header">
      <h4>价格降幅排行榜</h4>
      <div class="ranking-info" v-if="periodInfo">
        <span>{{ getPeriodText(periodInfo.period) }}</span>
        <span>{{ formatDate(periodInfo.start_date) }} - {{ formatDate(periodInfo.end_date) }}</span>
      </div>
    </div>

    <div class="table-container" :class="{ 'loading': loading }">
      <div v-if="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <span>加载中...</span>
      </div>

      <div v-else-if="rankings.length === 0" class="no-data">
        <i class="fas fa-chart-bar"></i>
        <span>暂无排行数据</span>
      </div>

      <div v-else class="ranking-list">
        <div
          v-for="(item, index) in rankings"
          :key="item.product_id"
          class="ranking-item"
          :class="{ 'top-three': index < 3 }"
          @click="handleProductClick(item)"
        >
          <div class="rank-badge" :class="`rank-${Math.min(index + 1, 4)}`">
            <span v-if="index < 3" class="rank-medal">
              <i :class="getMedalIcon(index)"></i>
            </span>
            <span v-else class="rank-number">{{ index + 1 }}</span>
          </div>

          <div class="product-info">
            <div class="product-name">{{ item.product_name }}</div>
            <div class="product-category">{{ item.category || '未分类' }}</div>
          </div>

          <div class="ranking-metrics standard-font">
            <div class="metric-item primary">
              <span class="metric-value">
                {{ rankingType === 'drop_percentage' 
                    ? formatPercentage(item.total_drop_percentage, 1, true) 
                    : formatCurrency(item.total_drop_amount, '元/T', true) 
                }}
              </span>
              <span class="metric-label">
                {{ rankingType === 'drop_percentage' ? '降幅比例' : '降幅金额' }}
              </span>
            </div>

            <div class="metric-item">
              <span class="metric-value">{{ item.adjustment_count }}</span>
              <span class="metric-label">调价次数</span>
            </div>

            <div class="metric-item">
              <span class="metric-value">{{ formatCurrency(item.latest_price, '', false) }}</span>
              <span class="metric-label">最新价格 (元/T)</span>
            </div>
            
            <div class="metric-item">
              <span class="metric-value">{{ formatPercentage(item.change_percentage, 1, true) }}</span>
              <span class="metric-label">变化率</span>
            </div>
          </div>

          <div class="ranking-arrow">
            <i class="fas fa-chevron-right"></i>
          </div>
        </div>
      </div>
    </div>

    <div class="table-footer" v-if="rankings.length > 0">
      <div class="footer-info">
        <span>共 {{ rankings.length }} 个产品</span>
        <span>按{{ rankingType === 'drop_percentage' ? '降幅比例' : '降幅金额' }}排序</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { formatCurrency, formatDate, formatPercentage } from '@/utils/formatters'

// Props
const props = defineProps({
  rankings: {
    type: Array,
    default: () => []
  },
  rankingType: {
    type: String,
    default: 'drop_amount'
  },
  loading: {
    type: Boolean,
    default: false
  },
  periodInfo: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['product-click'])

// 方法
const getPeriodText = (period) => {
  const texts = {
    'daily': '日排行',
    'weekly': '周排行',
    'monthly': '月排行'
  }
  return texts[period] || '排行榜'
}

const getMedalIcon = (index) => {
  const icons = ['fas fa-crown', 'fas fa-medal', 'fas fa-award']
  return icons[index] || 'fas fa-trophy'
}

const handleProductClick = (product) => {
  emit('product-click', product)
}
</script>

<style scoped>
.price-ranking-table {
  width: 100%;
  height: 100%;
}

.table-header {
  margin-bottom: 16px;
}

.table-header h4 {
  color: #005BAC;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.ranking-info {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #666;
}

.table-container {
  position: relative;
  min-height: 200px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #005BAC;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-data {
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  gap: 12px;
}

.no-data i {
  font-size: 32px;
  opacity: 0.5;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  cursor: pointer;
  transition: all 0.3s ease;
}

.ranking-item:hover {
  background: #e9ecef;
  border-color: #005BAC;
  transform: translateX(2px);
}

.standard-font {
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

.ranking-item.top-three {
  background: linear-gradient(135deg, #fff7e6 0%, #fff1b8 100%);
  border-color: #ffa940;
}

.ranking-item.top-three:hover {
  background: linear-gradient(135deg, #fff1b8 0%, #ffe58f 100%);
}

.rank-badge {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 12px;
}

.rank-1 {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #ad6800;
}

.rank-2 {
  background: linear-gradient(135deg, #c0c0c0 0%, #e8e8e8 100%);
  color: #666;
}

.rank-3 {
  background: linear-gradient(135deg, #cd7f32 0%, #deb887 100%);
  color: #8b4513;
}

.rank-4 {
  background: #f0f0f0;
  color: #666;
}

.rank-medal i {
  font-size: 16px;
}

.rank-number {
  font-size: 14px;
}

.product-info {
  flex: 1;
  min-width: 0;
}

.product-name {
  font-weight: 600;
  color: #333;
  font-size: 14px;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-category {
  font-size: 11px;
  color: #999;
}

.ranking-metrics {
  display: flex;
  gap: 16px;
  align-items: center;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.metric-item.primary .metric-value {
  color: #D92E2E;
  font-weight: bold;
  font-size: 16px;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.metric-label {
  font-size: 10px;
  color: #666;
  text-align: center;
}

.ranking-arrow {
  flex-shrink: 0;
  color: #ccc;
  font-size: 12px;
}

.ranking-item:hover .ranking-arrow {
  color: #005BAC;
}

.table-footer {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e9ecef;
}

.footer-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #666;
}

@media (max-width: 768px) {
  .ranking-item {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .ranking-metrics {
    justify-content: space-around;
    gap: 8px;
  }

  .metric-item {
    flex: 1;
  }

  .ranking-arrow {
    align-self: center;
  }

  .footer-info {
    flex-direction: column;
    gap: 4px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .ranking-metrics {
    flex-direction: column;
    gap: 4px;
  }

  .metric-item {
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
  }
}
</style>
