/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* CSS 变量定义 - 专业财经报告配色 */
:root {
  --primary-blue: #005BAC;
  --secondary-blue: #49A9E8;
  --signal-red: #D92E2E;
  --text-primary: #333333;
  --text-secondary: #666666;
  --border-light: #E0E0E0;
  --background-white: #ffffff;
  --background-light: #F8F9FA;
  --accent-orange: #ff9500;
  --success-green: #34C759;
  --warning-orange: #FF9500;
  --neutral-gray: #8E8E93;
}

/* 基础字体和布局 */
body {
  font-family: "Microsoft YaHei", "微软雅黑", Arial, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  background: var(--background-white);
  color: var(--text-primary);
  line-height: 1.6;
  font-size: 14px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 链接样式 */
a {
  color: var(--primary-blue);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: #004a94;
}

/* 按钮基础样式 */
button {
  font-family: inherit;
  cursor: pointer;
  border: none;
  outline: none;
  transition: all 0.3s ease;
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 输入框基础样式 */
input, select, textarea {
  font-family: inherit;
  outline: none;
  transition: all 0.3s ease;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #999;
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: bold; }
.font-medium { font-weight: 500; }
.font-normal { font-weight: normal; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-blue { color: var(--primary-blue); }
.text-red { color: var(--signal-red); }
.text-green { color: var(--success-green); }

.bg-white { background-color: var(--background-white); }
.bg-light { background-color: var(--background-light); }
.bg-blue { background-color: var(--primary-blue); }

.border { border: 1px solid var(--border-light); }
.border-radius { border-radius: 8px; }
.border-radius-lg { border-radius: 12px; }

.shadow { box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); }
.shadow-lg { box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15); }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.gap-1 { gap: 4px; }
.gap-2 { gap: 8px; }
.gap-3 { gap: 12px; }
.gap-4 { gap: 16px; }
.gap-5 { gap: 20px; }

.p-1 { padding: 4px; }
.p-2 { padding: 8px; }
.p-3 { padding: 12px; }
.p-4 { padding: 16px; }
.p-5 { padding: 20px; }

.m-1 { margin: 4px; }
.m-2 { margin: 8px; }
.m-3 { margin: 12px; }
.m-4 { margin: 16px; }
.m-5 { margin: 20px; }

.mb-1 { margin-bottom: 4px; }
.mb-2 { margin-bottom: 8px; }
.mb-3 { margin-bottom: 12px; }
.mb-4 { margin-bottom: 16px; }
.mb-5 { margin-bottom: 20px; }

.mt-1 { margin-top: 4px; }
.mt-2 { margin-top: 8px; }
.mt-3 { margin-top: 12px; }
.mt-4 { margin-top: 16px; }
.mt-5 { margin-top: 20px; }

/* 响应式工具类 */
.hidden { display: none; }

@media (max-width: 768px) {
  .md-hidden { display: none; }
  .md-block { display: block; }
  .md-flex { display: flex; }
}

@media (max-width: 480px) {
  .sm-hidden { display: none; }
  .sm-block { display: block; }
  .sm-flex { display: flex; }
}

/* 动画类 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.slide-enter-active, .slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

/* 加载状态 */
.loading {
  position: relative;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--border-light);
  border-top-color: var(--primary-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 错误状态 */
.error {
  color: var(--signal-red);
  border-color: var(--signal-red);
}

.success {
  color: var(--success-green);
  border-color: var(--success-green);
}

.warning {
  color: var(--warning-orange);
  border-color: var(--warning-orange);
}

/* 趋势指示器 */
.trend-positive {
  color: var(--success-green);
}

.trend-negative {
  color: var(--signal-red);
}

.trend-neutral {
  color: var(--neutral-gray);
}

/* 页面加载指示器 */
#page-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-blue), var(--secondary-blue));
  z-index: 9999;
  display: none;
  animation: loading-progress 2s ease-in-out infinite;
}

@keyframes loading-progress {
  0% { transform: translateX(-100%); }
  50% { transform: translateX(0%); }
  100% { transform: translateX(100%); }
}

/* 卡片悬停效果 */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

/* 数据表格样式 */
.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9em;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.data-table th,
.data-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid var(--border-light);
}

.data-table th {
  background: var(--background-light);
  font-weight: 600;
  color: var(--text-primary);
  position: sticky;
  top: 0;
  z-index: 1;
}

.data-table tr:hover {
  background: #f8f9fa;
}

.data-table tr:last-child td {
  border-bottom: none;
}

/* 徽章样式 */
.badge {
  display: inline-block;
  padding: 4px 8px;
  font-size: 0.75em;
  font-weight: 500;
  border-radius: 12px;
  text-align: center;
  white-space: nowrap;
}

.badge-primary {
  background: var(--primary-blue);
  color: white;
}

.badge-success {
  background: var(--success-green);
  color: white;
}

.badge-warning {
  background: var(--warning-orange);
  color: white;
}

.badge-danger {
  background: var(--signal-red);
  color: white;
}

.badge-secondary {
  background: var(--neutral-gray);
  color: white;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-secondary);
}

.empty-state-icon {
  font-size: 3em;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-state-title {
  font-size: 1.2em;
  margin-bottom: 8px;
  color: var(--text-primary);
}

.empty-state-description {
  font-size: 0.9em;
  line-height: 1.5;
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }

  .card-hover {
    box-shadow: none !important;
    transform: none !important;
  }
}
