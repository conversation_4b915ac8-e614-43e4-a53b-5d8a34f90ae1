<template>
  <div class="price-monitoring-view">
    <h1>产品价格波动监控</h1>
    <PriceAlertPanel />
    <ProductPriceSelector @product-selected="updateSelectedProduct" />
    <div v-if="selectedProduct" class="chart-area">
      <h2>{{ selectedProduct }} - 价格趋势</h2>
      <PriceTrendChart :product-name="selectedProduct" />
    </div>
    <div v-else class="placeholder">
      <p>请从上方选择一个产品以查看其价格趋势。</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import ProductPriceSelector from '@/components/priceMonitoring/ProductPriceSelector.vue';
import PriceTrendChart from '@/components/priceMonitoring/PriceTrendChart.vue';
import PriceAlertPanel from '@/components/priceMonitoring/PriceAlertPanel.vue';

const selectedProduct = ref('');

function updateSelectedProduct(productName) {
  selectedProduct.value = productName;
}
</script>

<style scoped>
.price-monitoring-view {
  padding: 2rem;
}
h1 {
  color: #333;
  margin-bottom: 1.5rem;
}
.chart-area {
  margin-top: 2rem;
}
h2 {
  color: #555;
  margin-bottom: 1rem;
}
.placeholder {
  margin-top: 2rem;
  padding: 2rem;
  text-align: center;
  background-color: #f0f0f0;
  border-radius: 8px;
}
</style>
