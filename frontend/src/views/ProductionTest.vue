<template>
  <div class="production-test-page">
    <h1>产销率分析测试页面</h1>
    
    <!-- 基本信息显示 -->
    <div class="info-section">
      <h2>数据状态</h2>
      <div class="status-grid">
        <div class="status-item">
          <label>加载状态:</label>
          <span :class="['status-value', isLoading ? 'loading' : 'ready']">
            {{ isLoading ? '加载中...' : '就绪' }}
          </span>
        </div>
        <div class="status-item">
          <label>错误信息:</label>
          <span :class="['status-value', error ? 'error' : 'success']">
            {{ error || '无错误' }}
          </span>
        </div>
        <div class="status-item">
          <label>数据记录数:</label>
          <span class="status-value">{{ ratioData.length }}</span>
        </div>
        <div class="status-item">
          <label>日期范围:</label>
          <span class="status-value">{{ dateRangeText }}</span>
        </div>
      </div>
    </div>

    <!-- 控制按钮 -->
    <div class="controls-section">
      <h2>操作控制</h2>
      <div class="button-group">
        <button @click="fetchData" :disabled="isLoading" class="btn primary">
          {{ isLoading ? '加载中...' : '获取数据' }}
        </button>
        <button @click="clearData" class="btn secondary">清空数据</button>
        <button @click="testApiDirect" class="btn info">直接测试API</button>
        <button @click="showRawData = !showRawData" class="btn ghost">
          {{ showRawData ? '隐藏' : '显示' }}原始数据
        </button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section" v-if="ratioData.length > 0">
      <h2>统计信息</h2>
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-value">{{ totalProduction.toFixed(1) }}T</div>
          <div class="stat-label">总生产量</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ totalSales.toFixed(1) }}T</div>
          <div class="stat-label">总销售量</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ averageRatio.toFixed(1) }}%</div>
          <div class="stat-label">平均产销率</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ maxRatio.toFixed(1) }}%</div>
          <div class="stat-label">最高产销率</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ minRatio.toFixed(1) }}%</div>
          <div class="stat-label">最低产销率</div>
        </div>
      </div>
    </div>

    <!-- 原始数据显示 -->
    <div class="raw-data-section" v-if="showRawData && ratioData.length > 0">
      <h2>原始数据 (前10条)</h2>
      <div class="data-table">
        <table>
          <thead>
            <tr>
              <th>日期</th>
              <th>生产量(T)</th>
              <th>销售量(T)</th>
              <th>产销率(%)</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in ratioData.slice(0, 10)" :key="index">
              <td>{{ item.date }}</td>
              <td>{{ item.production_volume.toFixed(2) }}</td>
              <td>{{ item.sales_volume.toFixed(2) }}</td>
              <td :class="getRatioClass(item.ratio)">{{ item.ratio.toFixed(2) }}%</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 简单图表 -->
    <div class="chart-section" v-if="ratioData.length > 0">
      <h2>产销率趋势图</h2>
      <ProductionRatioTrendChart
        :start-date="startDate"
        :end-date="endDate"
        @data-updated="handleDataUpdate"
      />
    </div>

    <!-- 调试日志 -->
    <div class="debug-section">
      <h2>调试日志</h2>
      <div class="debug-log" ref="debugLog">
        <div v-for="(log, index) in debugLogs" :key="index" :class="['log-entry', log.type]">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
      <button @click="clearLogs" class="btn small">清空日志</button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useProductionStore } from '@/stores/production'
import { fetchData as apiFetchData } from '@/utils/api'
import ProductionRatioTrendChart from '@/components/production/ProductionRatioTrendChart.vue'

const productionStore = useProductionStore()

import { useDateRange } from '@/composables/useDateRange'

const { dateRange, ensureDateRangeLoaded } = useDateRange()

// 本地状态
const isLoading = ref(false)
const error = ref('')
const showRawData = ref(false)
const debugLogs = ref([])
const startDate = ref(dateRange.value.start)
const endDate = ref(dateRange.value.end)

// 计算属性
const ratioData = computed(() => productionStore.ratioData)

const totalProduction = computed(() => {
  return ratioData.value.reduce((sum, item) => sum + (item.production_volume || 0), 0)
})

const totalSales = computed(() => {
  return ratioData.value.reduce((sum, item) => sum + (item.sales_volume || 0), 0)
})

const averageRatio = computed(() => {
  if (totalProduction.value === 0) return 0
  return (totalSales.value / totalProduction.value) * 100
})

const maxRatio = computed(() => {
  if (ratioData.value.length === 0) return 0
  return Math.max(...ratioData.value.map(item => item.ratio))
})

const minRatio = computed(() => {
  if (ratioData.value.length === 0) return 0
  return Math.min(...ratioData.value.map(item => item.ratio))
})

const dateRangeText = computed(() => {
  if (ratioData.value.length === 0) return '无数据'
  return `${ratioData.value[0]?.date} - ${ratioData.value[ratioData.value.length - 1]?.date}`
})

// 方法
const addLog = (message, type = 'info') => {
  const time = new Date().toLocaleTimeString()
  debugLogs.value.push({ time, message, type })
  console.log(`[${type.toUpperCase()}] ${message}`)
}

const fetchData = async () => {
  isLoading.value = true
  error.value = ''
  addLog('开始获取产销率数据...', 'info')
  
  try {
    await productionStore.fetchProductionRatioData(startDate.value, endDate.value)
    addLog(`✅ 数据获取成功，共 ${ratioData.value.length} 条记录`, 'success')
  } catch (err) {
    error.value = err.message || '数据获取失败'
    addLog(`❌ 数据获取失败: ${error.value}`, 'error')
  } finally {
    isLoading.value = false
  }
}

const clearData = () => {
  productionStore.clearData()
  addLog('数据已清空', 'info')
}

const testApiDirect = async () => {
  addLog('直接测试API调用...', 'info')
  
  try {
    const response = await apiFetchData(`/api/trends/ratio?start_date=${startDate.value}&end_date=${endDate.value}`)
    addLog(`✅ 直接API调用成功，返回 ${response.length} 条记录`, 'success')
    addLog(`样本数据: ${JSON.stringify(response.slice(0, 2))}`, 'info')
  } catch (err) {
    addLog(`❌ 直接API调用失败: ${err.message}`, 'error')
  }
}

const clearLogs = () => {
  debugLogs.value = []
}

const getRatioClass = (ratio) => {
  if (ratio > 100) return 'ratio-high'
  if (ratio < 90) return 'ratio-low'
  return 'ratio-normal'
}

const handleDataUpdate = (data) => {
  addLog(`图表组件数据更新: ${data.length} 条记录`, 'info')
}

// 生命周期
onMounted(async () => {
  addLog('产销率测试页面已加载', 'info')
  
  // 确保日期范围已加载
  await ensureDateRangeLoaded()
  
  // 更新本地日期状态
  startDate.value = dateRange.value.start
  endDate.value = dateRange.value.end
  
  fetchData()
})
</script>

<style scoped>
.production-test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h1, h2 {
  color: #333;
  margin-bottom: 16px;
}

.info-section, .controls-section, .stats-section, .raw-data-section, .chart-section, .debug-section {
  margin-bottom: 32px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.status-grid, .stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.status-item label {
  font-weight: 600;
  color: #666;
}

.status-value {
  font-weight: 600;
}

.status-value.loading {
  color: #007bff;
}

.status-value.ready {
  color: #28a745;
}

.status-value.error {
  color: #dc3545;
}

.status-value.success {
  color: #28a745;
}

.button-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn.primary {
  background: #007bff;
  color: white;
}

.btn.primary:hover:not(:disabled) {
  background: #0056b3;
}

.btn.secondary {
  background: #6c757d;
  color: white;
}

.btn.info {
  background: #17a2b8;
  color: white;
}

.btn.ghost {
  background: transparent;
  color: #007bff;
  border: 1px solid #007bff;
}

.btn.small {
  padding: 6px 12px;
  font-size: 0.9em;
}

.stat-card {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 1.8em;
  font-weight: bold;
  color: #007bff;
  margin-bottom: 8px;
}

.stat-label {
  color: #666;
  font-size: 0.9em;
}

.data-table {
  overflow-x: auto;
}

.data-table table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

.data-table th {
  background: #f8f9fa;
  font-weight: 600;
}

.ratio-high {
  color: #28a745;
  font-weight: 600;
}

.ratio-low {
  color: #dc3545;
  font-weight: 600;
}

.ratio-normal {
  color: #007bff;
  font-weight: 600;
}

.debug-log {
  max-height: 300px;
  overflow-y: auto;
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  font-family: monospace;
  font-size: 0.9em;
  margin-bottom: 12px;
}

.log-entry {
  margin-bottom: 4px;
  display: flex;
  gap: 12px;
}

.log-time {
  color: #666;
  min-width: 80px;
}

.log-entry.info .log-message {
  color: #333;
}

.log-entry.success .log-message {
  color: #28a745;
}

.log-entry.error .log-message {
  color: #dc3545;
}

.chart-section {
  min-height: 500px;
}
</style>
