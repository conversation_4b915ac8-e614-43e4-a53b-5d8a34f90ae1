/**
 * Price Data Export Manager - 价格数据导出管理器
 * 支持多种格式的数据导出功能
 */

import { apiRequest } from './api.js';

class PriceExportManager {
  constructor() {
    this.exportFormats = ['csv', 'json', 'xlsx'];
    this.downloadQueue = [];
    this.isProcessing = false;
  }

  /**
   * 导出价格数据
   * @param {Object} options 导出选项
   * @param {string[]} options.products - 产品列表
   * @param {string} options.format - 导出格式 (csv, json, xlsx)
   * @param {string} options.startDate - 开始日期
   * @param {string} options.endDate - 结束日期
   * @param {boolean} options.includeStats - 是否包含统计信息
   * @param {Function} options.onProgress - 进度回调
   */
  async exportPriceData(options = {}) {
    const {
      products = [],
      format = 'csv',
      startDate = null,
      endDate = null,
      includeStats = false,
      onProgress = null
    } = options;

    if (!products || products.length === 0) {
      throw new Error('请至少选择一个产品');
    }

    if (!this.exportFormats.includes(format)) {
      throw new Error(`不支持的导出格式: ${format}`);
    }

    try {
      if (onProgress) onProgress({ status: 'preparing', progress: 0 });

      const params = {
        products: products.join(','),
        format,
        includeStats: includeStats.toString()
      };

      if (startDate) params.startDate = startDate;
      if (endDate) params.endDate = endDate;

      if (onProgress) onProgress({ status: 'fetching', progress: 20 });

      // 获取导出数据
      const response = await apiRequest('/api/prices/export', 'GET', null, params);

      if (onProgress) onProgress({ status: 'processing', progress: 60 });

      // 处理不同格式的响应
      if (format === 'json') {
        await this.downloadJSON(response, options);
      } else if (format === 'csv') {
        await this.downloadCSV(response, options);
      } else if (format === 'xlsx') {
        await this.downloadExcel(response, options);
      }

      if (onProgress) onProgress({ status: 'completed', progress: 100 });

      return { success: true, message: '导出成功' };

    } catch (error) {
      if (onProgress) onProgress({ status: 'error', progress: 0, error: error.message });
      throw error;
    }
  }

  /**
   * 批量导出数据
   * @param {Array} exportTasks - 导出任务列表
   */
  async batchExport(exportTasks = []) {
    this.downloadQueue = [...exportTasks];
    this.isProcessing = true;

    const results = [];

    for (let i = 0; i < exportTasks.length; i++) {
      const task = exportTasks[i];
      try {
        const result = await this.exportPriceData({
          ...task,
          onProgress: (progress) => {
            task.onProgress?.({
              ...progress,
              taskIndex: i,
              totalTasks: exportTasks.length
            });
          }
        });
        results.push({ ...result, task });
      } catch (error) {
        results.push({ success: false, error: error.message, task });
      }
    }

    this.isProcessing = false;
    this.downloadQueue = [];

    return results;
  }

  /**
   * 下载 JSON 格式数据
   */
  async downloadJSON(data, options) {
    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: 'application/json;charset=utf-8'
    });
    
    const filename = this.generateFilename('json', options);
    this.downloadBlob(blob, filename);
  }

  /**
   * 下载 CSV 格式数据
   */
  async downloadCSV(data, options) {
    const blob = new Blob([data], {
      type: 'text/csv;charset=utf-8'
    });
    
    const filename = this.generateFilename('csv', options);
    this.downloadBlob(blob, filename);
  }

  /**
   * 下载 Excel 格式数据
   */
  async downloadExcel(data, options) {
    // 服务器返回的是二进制数据，需要转换为 Blob
    const blob = new Blob([data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    
    const filename = this.generateFilename('xlsx', options);
    this.downloadBlob(blob, filename);
  }

  /**
   * 生成文件名
   */
  generateFilename(format, options) {
    const date = new Date().toISOString().split('T')[0];
    const products = options.products?.slice(0, 3).join('-') || 'export';
    return `价格数据_${products}_${date}.${format}`;
  }

  /**
   * 下载 Blob 文件
   */
  downloadBlob(blob, filename) {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    
    // 清理
    setTimeout(() => {
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    }, 100);
  }

  /**
   * 预览导出数据
   */
  async previewExportData(options = {}) {
    const previewOptions = {
      ...options,
      format: 'json',
      limit: 10 // 只预览前10条
    };

    try {
      const response = await apiRequest('/api/prices/export', 'GET', null, {
        products: previewOptions.products.join(','),
        format: 'json',
        startDate: previewOptions.startDate,
        endDate: previewOptions.endDate,
        includeStats: 'false',
        limit: previewOptions.limit
      });

      return response;
    } catch (error) {
      console.error('Failed to preview export data:', error);
      throw error;
    }
  }

  /**
   * 获取导出格式信息
   */
  getFormatInfo(format) {
    const formatInfo = {
      csv: {
        name: 'CSV',
        description: 'CSV格式，可用Excel打开',
        icon: '📊',
        mimeType: 'text/csv',
        features: ['兼容Excel', '文件较小', '易于处理']
      },
      json: {
        name: 'JSON',
        description: 'JSON格式，适合程序处理',
        icon: '📄',
        mimeType: 'application/json',
        features: ['结构化数据', '易于解析', '包含元数据']
      },
      xlsx: {
        name: 'Excel',
        description: 'Excel格式，功能丰富',
        icon: '📑',
        mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        features: ['多工作表', '格式化', '包含统计']
      }
    };

    return formatInfo[format] || null;
  }

  /**
   * 验证导出参数
   */
  validateExportParams(params) {
    const errors = [];

    if (!params.products || params.products.length === 0) {
      errors.push('请选择要导出的产品');
    }

    if (params.products && params.products.length > 50) {
      errors.push('一次最多导出50个产品的数据');
    }

    if (params.startDate && params.endDate) {
      const start = new Date(params.startDate);
      const end = new Date(params.endDate);
      
      if (start > end) {
        errors.push('开始日期不能晚于结束日期');
      }
      
      const daysDiff = (end - start) / (1000 * 60 * 60 * 24);
      if (daysDiff > 365) {
        errors.push('导出时间范围不能超过一年');
      }
    }

    if (!this.exportFormats.includes(params.format)) {
      errors.push(`不支持的导出格式: ${params.format}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 取消导出任务
   */
  cancelExport() {
    this.downloadQueue = [];
    this.isProcessing = false;
  }

  /**
   * 获取导出队列状态
   */
  getQueueStatus() {
    return {
      isProcessing: this.isProcessing,
      queueLength: this.downloadQueue.length,
      queue: this.downloadQueue
    };
  }
}

// 创建单例实例
const exportManager = new PriceExportManager();

// 导出管理器和便捷方法
export default exportManager;

export const {
  exportPriceData,
  batchExport,
  previewExportData,
  getFormatInfo,
  validateExportParams,
  cancelExport,
  getQueueStatus
} = {
  exportPriceData: exportManager.exportPriceData.bind(exportManager),
  batchExport: exportManager.batchExport.bind(exportManager),
  previewExportData: exportManager.previewExportData.bind(exportManager),
  getFormatInfo: exportManager.getFormatInfo.bind(exportManager),
  validateExportParams: exportManager.validateExportParams.bind(exportManager),
  cancelExport: exportManager.cancelExport.bind(exportManager),
  getQueueStatus: exportManager.getQueueStatus.bind(exportManager)
};