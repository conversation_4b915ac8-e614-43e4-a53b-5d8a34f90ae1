# 数据导入最佳实践指南

本指南旨在为处理复杂数据集的导入任务提供一系列经过验证的最佳实践。遵循这些原则可以显著提高数据操作的可靠性、可预测性和安全性。

## 核心策略：“精确删除后插入”以保证数据完整性

在需要重复导入或更新时间序列数据的场景中，保证数据完整性是最大的挑战之一。错误的方法可能导致数据重复、数据丢失或不一致。我们强烈推荐采用**“精确删除后插入” (Precision-Delete-Then-Insert)** 策略作为保证数据完整性的黄金标准。

### 为什么这个策略至关重要？

在一个复杂的业务数据库中，多个数据类型（如销售、生产、库存）可能共享相同的时间维度。例如，数据库中同时存在7月1日的销售额和7月1日的产量。当我们需要更新7月1日的销售额时，我们必须确保不会影响到当天的产量数据。

### 对比常见的数据导入策略

让我们来比较三种常见的策略，以理解“精确删除”的优越性：

| 策略 | 实现方式 | 优点 | 缺点和风险 |
| :--- | :--- | :--- | :--- |
| **1. `INSERT OR REPLACE`** | 使用 `INSERT OR REPLACE INTO ...` SQL语句。 | 简单，能避免主键冲突。 | **致命缺陷：** 会导致聚合数据计算错误（如销量翻倍）。当一条记录被“替换”时，依赖它的触发器或后续计算可能会将其视为一条全新的记录，导致重复累加。**极不推荐**在有下游数据处理的场景中使用。 |
| **2. 宽泛的“先删后插”** | `DELETE FROM table WHERE date BETWEEN ? AND ?;` 然后 `INSERT ...` | 解决了数据重复累加的问题。 | **高风险：** 存在严重的数据误删风险。它会删除指定日期范围内的**所有**数据，而不管其具体类型。例如，更新7月份的销售数据时，会把7月份的生产和库存数据也一并删除。 |
| **3. (最佳实践) “精确删除后插入”** | `DELETE FROM table WHERE date BETWEEN ? AND ? AND data_type = ?;` 然后 `INSERT ...` | **健壮和安全** | 实现上需要额外的逻辑来检测数据类型，但这点开销对于数据安全来说是完全值得的。 |

### “精确删除后插入”策略的优势

1.  **靶向操作，杜绝误伤**
    *   通过在 `DELETE` 语句的 `WHERE` 子句中同时包含**日期范围**和**数据类型**，我们可以像外科手术一样精确地定位到需要被替换的旧数据。
    *   这保证了更新某一类型的数据（如销售）时，绝对不会影响到共享同一时间维度的其他任何数据（如生产、库存）。

2.  **保证操作的幂等性**
    *   幂等性意味着无论一个操作执行一次还是多次，结果都是相同的。
    *   使用此策略，重复导入相同的数据文件，数据库的最终状态将是完全一致的，这使得数据恢复和修正工作变得极其安全和可预测。

3.  **数据完整性的最终保障**
    *   将精确的 `DELETE` 和后续的 `INSERT` 放在一个原子事务中执行，可以确保操作的完整性。如果插入失败，删除操作也会被回滚，防止出现数据被删除但新数据未插入的“数据真空”状态。

### 实施建议

*   **务必检测数据类型**：在执行任何删除操作之前，你的导入脚本必须有能力分析待导入的数据，并准确识别出其中包含的数据类型。
*   **动态生成SQL**：根据检测到的数据类型，为每一种类型动态生成独立的、精确的 `DELETE` 语句。
*   **拥抱原子事务**：将所有 `DELETE` 和 `INSERT` 操作捆绑在同一个事务中执行，这是保证数据一致性的最后一道防线。

通过将“精确删除后插入”策略作为您数据导入流程的核心，您可以构建一个既强大又可靠的数据管理系统，有效避免那些最常见且最具破坏性的数据完整性问题。

## 数据质量与验证最佳实践

数据质量是数据导入成功的基石。除了采用正确的导入策略，还必须在数据处理的早期阶段就进行严格的质量控制。

### 关键实践1：源头数据清洗（以“7步筛选法”为例）

在数据进入数据库之前，必须对其进行严格的清洗和筛选。一个典型的例子是我系统中用于处理库存数据的 **“7步筛选法”**：

1.  **移除无效记录**：删除完全为空或关键字段（如“物料名称”）缺失的行。
2.  **特殊情况优先处理**：为避免被通用规则误伤，可暂时分离特殊产品（如“凤肠”）。
3.  **基于业务规则筛选**：根据业务定义，移除不符合条件的记录（如“客户”或“物料分类”为“副产品”的条目）。
4.  **关键词排除**：移除物料名称中包含特定排除性词语（如“鲜”）的记录。
5.  **合并特殊情况**：将之前分离的特殊产品重新合并回数据集。

这种多步骤、有针对性的筛选流程，是保证最终入库数据高度准确的关键。

### 关键实践2：数据聚合与去重

- **正确定义聚合维度**：在进行 `GROUP BY` 操作时，必须确保聚合的键（`key`）能够唯一标识一条业务记录。例如，聚合销售数据时应使用 `(record_date, product_name)`，而不是 `(record_date, product_name, category)`，以避免因一个产品属于多个分类而产生重复记录。
- **利用字典去重**：在合并来自不同源（如库存、生产、销售）的数据时，使用以唯一业务键 `(product_id, record_date)` 为 `key` 的字典来构建最终记录集。这是一种高效且可靠的去重方法，可以确保每个产品每天只有一条记录。

## 常见问题与故障排除 (FAQ)

### Q1: 为什么导入后发现销量、产量或金额数据异常高？

**可能原因 1：数据重复**
- **症状**: 单个产品的销量远超预期，或总销量翻倍。
- **排查**: 检查数据处理脚本中的 `groupby` 聚合逻辑。是否包含了不必要的维度（如 `category`），导致同一产品被多次计算？
- **解决方案**: 确保聚合键的唯一性。

**可能原因 2：单位转换错误**
- **症状**: 数据量级错误，例如放大了1000倍。
- **排查**: 检查整个数据处理链路，确认单位（如 `kg` vs `吨`）是否一致。是否存在多重转换（例如，在一个模块中除以1000，在另一个模块中又乘以1000）？
- **解决方案**: 统一数据存储单位（推荐在数据库中统一使用基础单位，如 `kg`），仅在前端展示时进行转换。

### Q2: 为什么某些日期范围的数据缺失，或全部集中在某一天？

- **症状**: 生产数据或销售数据显示为0，或者所有数据都错误地标记为同一个日期。
- **排查**: 检查数据加载模块是否正确地从源Excel文件中提取了日期列。代码是否错误地使用了某个固定的配置日期，而不是记录中的实际日期？
- **解决方案**: 实现一个健壮的日期提取函数，能够自动检测并使用源文件中存在的日期列（如 `入库日期`, `生产日期`, `单据日期` 等）。

### Q3: `wrangler d1 execute` 命令执行失败，该如何处理？

- **排查 1 (环境问题)**: 确认 `wrangler` CLI 已正确安装并登录 (`npx wrangler login`)。
- **排查 2 (配置问题)**: 检查 `backend` 目录下的 `wrangler.toml` 文件是否存在且配置正确，特别是数据库ID。
- **排查 3 (网络问题)**: 如果使用 `--remote` 标志，请确保网络连接稳定。

## 性能调优与监控

### 选择合适的导入策略

- **大批量/全量导入**: 强烈推荐使用 **SQL文件导入** (`bulk_import_main.py`)。它将所有数据库操作打包成一个SQL文件，通过 `wrangler` 一次性执行。这种方式避免了成千上万次API调用的网络开销，性能极高且具备事务原子性。
- **小批量/增量更新**: 可以使用 **API批次导入** (`daily_import_main.py`)。虽然性能较低，但提供了更高的实时性。

### API导入性能参数

如果在必须使用API导入的场景下，可以通过 `config.py` 文件调整以下参数来平衡性能和稳定性：
- `D1_BATCH_SIZE`: 每个API请求发送的记录数（默认为200）。
- `D1_MAX_RETRIES`: 失败请求的最大重试次数（默认为5）。
- `D1_BATCH_DELAY`: 每个批次请求之间的延迟，以避免触发API速率限制（默认为0.3秒）。

### 日常监控

1.  **日志审查**: 定期检查导入脚本生成的日志，关注错误信息和警告。
2.  **数据验证**: 在导入完成后，执行简单的SQL查询来验证关键指标（如总销量、总产量、记录数）是否在预期范围内。
3.  **性能趋势**: 监控导入任务的执行时间。如果时间显著增加，可能意味着数据量大幅增长或处理逻辑存在瓶颈。

---
**文档版本**: v1.0
**最后更新**: 2025-07-30
**负责人**: AI Assistant