<template>
  <div class="date-range-test">
    <div class="test-header">
      <h1>动态日期范围测试</h1>
      <p>此页面用于测试动态日期范围功能，确保所有组件都能正确获取数据库中的实际日期范围</p>
    </div>

    <div class="test-sections">
      <!-- 日期范围状态 -->
      <div class="test-section">
        <h2>📅 日期范围状态</h2>
        <div class="status-grid">
          <div class="status-item">
            <label>加载状态:</label>
            <span :class="isLoading ? 'loading' : 'loaded'">
              {{ isLoading ? '加载中...' : '已加载' }}
            </span>
          </div>
          <div class="status-item">
            <label>开始日期:</label>
            <span class="date-value">{{ startDate || '未设置' }}</span>
          </div>
          <div class="status-item">
            <label>结束日期:</label>
            <span class="date-value">{{ endDate || '未设置' }}</span>
          </div>
          <div class="status-item">
            <label>数据源:</label>
            <span class="source-value">{{ isLoaded ? '数据库' : '备用值' }}</span>
          </div>
        </div>
      </div>

      <!-- 后端API测试 -->
      <div class="test-section">
        <h2>🔧 后端API测试</h2>
        <div class="api-test">
          <BaseButton @click="testDateRangeAPI" :loading="apiTesting" variant="primary">
            测试日期范围API
          </BaseButton>
          <div v-if="apiResult" class="api-result">
            <h3>API响应:</h3>
            <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
          </div>
        </div>
      </div>

      <!-- 组件测试 -->
      <div class="test-section">
        <h2>📊 组件日期测试</h2>
        <div class="component-tests">
          <div class="test-component">
            <h3>销量图表</h3>
            <SalesVolumeChart 
              :start-date="startDate" 
              :end-date="endDate" 
            />
          </div>
          <div class="test-component">
            <h3>库存趋势</h3>
            <InventoryTrendChart 
              :start-date="startDate" 
              :end-date="endDate" 
            />
          </div>
        </div>
      </div>

      <!-- 手动刷新 -->
      <div class="test-section">
        <h2>🔄 手动操作</h2>
        <div class="manual-controls">
          <BaseButton @click="refreshDateRange" :loading="isLoading" variant="secondary">
            刷新日期范围
          </BaseButton>
          <BaseButton @click="clearAndReload" variant="warning">
            清除并重新加载
          </BaseButton>
        </div>
      </div>

      <!-- 调试信息 -->
      <div class="test-section">
        <h2>🐛 调试信息</h2>
        <div class="debug-info">
          <div class="debug-item">
            <label>Store状态:</label>
            <pre>{{ debugStoreState }}</pre>
          </div>
          <div class="debug-item">
            <label>错误信息:</label>
            <div v-if="error" class="error-message">{{ error }}</div>
            <div v-else class="no-error">无错误</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useDateRange } from '@/composables/useDateRange'
import { useDateRangeStore } from '@/stores/dateRange'
import { fetchData } from '@/utils/api'
import BaseButton from '@/components/common/BaseButton.vue'
import SalesVolumeChart from '@/components/sales/SalesVolumeChart.vue'
import InventoryTrendChart from '@/components/inventory/InventoryTrendChart.vue'

// 使用日期范围组合式函数
const { startDate, endDate, isLoading, isLoaded, refreshDateRange: refresh } = useDateRange()

// 直接访问store用于调试
const dateRangeStore = useDateRangeStore()

// 本地状态
const apiTesting = ref(false)
const apiResult = ref(null)

// 计算属性
const debugStoreState = computed(() => ({
  availableDateRange: dateRangeStore.availableDateRange,
  isLoading: dateRangeStore.isLoading,
  error: dateRangeStore.error,
  lastUpdated: dateRangeStore.lastUpdated,
  isDateRangeLoaded: dateRangeStore.isDateRangeLoaded
}))

const error = computed(() => dateRangeStore.error)

// 方法
const testDateRangeAPI = async () => {
  apiTesting.value = true
  try {
    const result = await fetchData('/api/system/date-range')
    apiResult.value = result
    console.log('✅ Date range API test result:', result)
  } catch (err) {
    apiResult.value = { error: err.message }
    console.error('❌ Date range API test failed:', err)
  } finally {
    apiTesting.value = false
  }
}

const refreshDateRange = async () => {
  try {
    await refresh()
    console.log('✅ Date range refreshed successfully')
  } catch (err) {
    console.error('❌ Failed to refresh date range:', err)
  }
}

const clearAndReload = async () => {
  try {
    dateRangeStore.clearData()
    await dateRangeStore.fetchAvailableDateRange()
    console.log('✅ Date range cleared and reloaded')
  } catch (err) {
    console.error('❌ Failed to clear and reload:', err)
  }
}

// 生命周期
onMounted(async () => {
  console.log('🚀 DateRangeTest component mounted')
  
  // 测试API
  await testDateRangeAPI()
})
</script>

<style scoped>
.date-range-test {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 32px;
}

.test-header h1 {
  color: var(--primary-blue);
  margin-bottom: 8px;
}

.test-header p {
  color: var(--text-secondary);
  font-size: 0.95em;
}

.test-sections {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.test-section {
  background: var(--background-light);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid var(--border-light);
}

.test-section h2 {
  margin: 0 0 16px 0;
  color: var(--text-primary);
  font-size: 1.1em;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid var(--border-light);
}

.status-item label {
  font-weight: 500;
  color: var(--text-secondary);
}

.loading {
  color: var(--warning-orange);
}

.loaded {
  color: var(--success-green);
}

.date-value {
  font-family: monospace;
  color: var(--primary-blue);
  font-weight: 500;
}

.source-value {
  color: var(--text-primary);
  font-weight: 500;
}

.api-test {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.api-result {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid var(--border-light);
}

.api-result h3 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
}

.api-result pre {
  margin: 0;
  font-size: 0.85em;
  color: var(--text-secondary);
  white-space: pre-wrap;
  word-break: break-word;
}

.component-tests {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.test-component {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid var(--border-light);
}

.test-component h3 {
  margin: 0 0 12px 0;
  color: var(--text-primary);
  font-size: 1em;
}

.manual-controls {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.debug-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.debug-item {
  background: white;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid var(--border-light);
}

.debug-item label {
  display: block;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.debug-item pre {
  margin: 0;
  font-size: 0.8em;
  color: var(--text-primary);
  white-space: pre-wrap;
  word-break: break-word;
}

.error-message {
  color: var(--danger-red);
  font-weight: 500;
}

.no-error {
  color: var(--success-green);
  font-weight: 500;
}

@media (max-width: 768px) {
  .component-tests {
    grid-template-columns: 1fr;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .manual-controls {
    justify-content: center;
  }
}
</style>