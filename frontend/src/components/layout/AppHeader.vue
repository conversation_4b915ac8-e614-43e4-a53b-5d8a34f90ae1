<template>
  <header class="header">
    <div class="header-content">
      <div class="header-main">
        <h1>春雪食品生产销售分析报告</h1>
        <p class="header-subtitle">Spring Snow Food Production & Sales Analysis Report</p>
      </div>
      <div class="header-meta">
        <div class="report-info">
          <div class="info-item">
            <span class="label">报告日期</span>
            <span class="value">{{ currentDate }}</span>
          </div>
          <div class="info-item">
            <span class="label">数据范围</span>
            <span class="value">{{ dateRange }}</span>
          </div>
          <div class="info-item">
            <span class="label">系统状态</span>
            <span class="value status-online">在线</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 装饰性背景元素 -->
    <div class="header-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>
  </header>
</template>

<script setup>
import { computed } from 'vue'
import { formatDate, formatDateRange } from '@/utils/formatters'
import { getDefaultDateRange } from '@/utils/constants'

const currentDate = computed(() => formatDate(new Date(), 'YYYY年MM月DD日'))
const dateRange = computed(() => {
  const defaultRange = getDefaultDateRange()
  return formatDateRange(defaultRange.START, defaultRange.END)
})
</script>

<style scoped>
.header {
  background: linear-gradient(135deg, var(--primary-blue) 0%, #003d7a 100%);
  color: white;
  border-radius: 12px;
  padding: 32px 40px;
  margin-bottom: 32px;
  box-shadow: 0 8px 32px rgba(0, 91, 172, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}

.header-main h1 {
  font-size: 2.2em;
  font-weight: 700;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-subtitle {
  font-size: 1em;
  opacity: 0.9;
  font-weight: 300;
  letter-spacing: 0.5px;
}

.header-meta {
  text-align: right;
}

.report-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.9em;
}

.label {
  opacity: 0.8;
  min-width: 60px;
}

.value {
  font-weight: 600;
  background: rgba(255, 255, 255, 0.1);
  padding: 4px 12px;
  border-radius: 16px;
  backdrop-filter: blur(10px);
}

.status-online {
  color: #4ade80;
  background: rgba(74, 222, 128, 0.2);
}

.header-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
}

.circle-1 {
  width: 120px;
  height: 120px;
  top: -60px;
  right: -60px;
}

.circle-2 {
  width: 80px;
  height: 80px;
  bottom: -40px;
  left: -40px;
}

.circle-3 {
  width: 60px;
  height: 60px;
  top: 50%;
  right: 15%;
  transform: translateY(-50%);
}

@media (max-width: 768px) {
  .header {
    padding: 24px 20px;
  }
  
  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .header-main h1 {
    font-size: 1.8em;
  }
  
  .header-meta {
    text-align: center;
  }
  
  .report-info {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .info-item {
    flex-direction: column;
    gap: 4px;
    text-align: center;
  }
}
</style>
