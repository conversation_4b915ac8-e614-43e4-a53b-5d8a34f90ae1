import { computed, onMounted } from 'vue'
import { useDateRangeStore } from '@/stores/dateRange'

/**
 * 日期范围组合式函数
 * 提供统一的日期范围管理，避免硬编码
 */
export function useDateRange() {
  const dateRangeStore = useDateRangeStore()

  // 计算属性
  const startDate = computed(() => dateRangeStore.defaultStartDate)
  const endDate = computed(() => dateRangeStore.defaultEndDate)
  const dateRange = computed(() => dateRangeStore.dateRange)
  const isLoading = computed(() => dateRangeStore.isLoading)
  const isLoaded = computed(() => dateRangeStore.isDateRangeLoaded)

  // 方法
  const ensureDateRangeLoaded = async () => {
    if (!dateRangeStore.isDateRangeLoaded && !dateRangeStore.isLoading) {
      await dateRangeStore.fetchAvailableDateRange()
    }
  }

  const refreshDateRange = async () => {
    return await dateRangeStore.refreshDateRange()
  }

  // 获取格式化的日期范围
  const getFormattedRange = (format = 'YYYY-MM-DD') => {
    return {
      start: startDate.value,
      end: endDate.value
    }
  }

  // 自动加载日期范围（可选）
  const autoLoad = async () => {
    await ensureDateRangeLoaded()
  }

  return {
    // 响应式数据
    startDate,
    endDate,
    dateRange,
    isLoading,
    isLoaded,
    
    // 方法
    ensureDateRangeLoaded,
    refreshDateRange,
    getFormattedRange,
    autoLoad
  }
}

/**
 * 获取默认日期值的函数
 * 这些函数可以在 defineProps 中安全使用
 */
export function getDefaultStartDate() {
  const dateRangeStore = useDateRangeStore()
  return dateRangeStore.isDateRangeLoaded 
    ? dateRangeStore.defaultStartDate 
    : '2025-06-01'
}

export function getDefaultEndDate() {
  const dateRangeStore = useDateRangeStore()
  return dateRangeStore.isDateRangeLoaded 
    ? dateRangeStore.defaultEndDate 
    : '2025-07-29'
}

/**
 * 用于需要立即获取日期范围的场景
 * 返回 Promise，确保日期范围已加载
 */
export async function getLoadedDateRange() {
  const dateRangeStore = useDateRangeStore()
  
  if (!dateRangeStore.isDateRangeLoaded) {
    await dateRangeStore.fetchAvailableDateRange()
  }
  
  return {
    start: dateRangeStore.defaultStartDate,
    end: dateRangeStore.defaultEndDate
  }
}