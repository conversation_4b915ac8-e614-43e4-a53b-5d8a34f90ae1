<template>
  <div
    ref="chartRef"
    :style="{ width, height }"
    class="echarts-container"
  />
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick, shallowRef } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  options: {
    type: Object,
    required: true
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '400px'
  },
  theme: {
    type: String,
    default: 'default'
  },
  loading: {
    type: Boolean,
    default: false
  },
  loadingOptions: {
    type: Object,
    default: () => ({
      text: '加载中...',
      color: '#005BAC',
      textColor: '#666',
      maskColor: 'rgba(255, 255, 255, 0.8)',
      zlevel: 0
    })
  },
  autoResize: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['chart-ready', 'chart-click', 'chart-dblclick', 'chart-mouseover', 'chart-mouseout'])

const chartRef = ref(null)
const chartInstance = shallowRef(null)
let resizeObserver = null

const initChart = () => {
  // 核心前置条件检查
  if (!chartRef.value || (chartRef.value.offsetWidth === 0 && chartRef.value.offsetHeight === 0)) {
    // console.log('Chart container not ready or not visible, skipping initialization.');
    return;
  }

  // 如果已经有实例，则不再重复创建
  if (chartInstance.value) {
    return;
  }

  try {
    // 创建新的图表实例
    chartInstance.value = echarts.init(chartRef.value, props.theme)
    console.log('✅ Chart instance created.');

    // 设置图表选项
    chartInstance.value.setOption(props.options, true)

    // 绑定事件
    bindEvents()

    // 设置加载状态
    if (props.loading) {
      chartInstance.value.showLoading(props.loadingOptions)
    }

    // 触发图表就绪事件
    emit('chart-ready', chartInstance.value)
    console.log('✅ Chart initialized and ready.');
  } catch (error) {
    console.error('❌ Failed to initialize chart:', error)
  }
}

const bindEvents = () => {
  if (!chartInstance.value) return

  // 点击事件
  chartInstance.value.on('click', (params) => {
    emit('chart-click', params)
  })

  // 双击事件
  chartInstance.value.on('dblclick', (params) => {
    emit('chart-dblclick', params)
  })

  // 鼠标悬停事件
  chartInstance.value.on('mouseover', (params) => {
    emit('chart-mouseover', params)
  })

  // 鼠标离开事件
  chartInstance.value.on('mouseout', (params) => {
    emit('chart-mouseout', params)
  })
}

const resizeChart = () => {
  if (chartInstance.value) {
    chartInstance.value.resize()
  }
}

const updateChart = (newOptions) => {
  if (chartInstance.value) {
    chartInstance.value.setOption(newOptions, true)
  }
}

// 监听选项变化
watch(() => props.options, (newOptions, oldOptions) => {
  // 如果图表实例已存在，则直接更新
  if (chartInstance.value) {
    if (newOptions) {
      updateChart(newOptions);
    }
  }
  // 如果实例不存在，但我们现在有了有效的options，尝试初始化
  else if (newOptions && newOptions.series && newOptions.series.length > 0) {
    // 使用 nextTick 确保DOM更新完成
    nextTick(() => {
      initChart();
    });
  }
}, { deep: true })

// 监听加载状态变化
watch(() => props.loading, (newLoading) => {
  if (!chartInstance.value) return
  
  if (newLoading) {
    chartInstance.value.showLoading(props.loadingOptions)
  } else {
    chartInstance.value.hideLoading()
  }
})

// 监听主题变化
watch(() => props.theme, () => {
  initChart()
})

// 设置自动调整大小
const setupAutoResize = () => {
  if (!props.autoResize || !chartRef.value) return

  // 使用 ResizeObserver 监听容器大小变化
  if (window.ResizeObserver) {
    // 增加防抖，避免过于频繁的重绘
    const debouncedResize = () => {
      // 在重绘前检查实例是否存在
      if (chartInstance.value) {
        resizeChart();
      }
    };
    
    resizeObserver = new ResizeObserver((entries) => {
      // 当容器从不可见到可见时，尝试初始化图表
      const entry = entries[0];
      if (entry.contentRect.width > 0 && entry.contentRect.height > 0 && !chartInstance.value) {
        console.log('Container became visible, attempting to initialize chart.');
        initChart();
      }
      debouncedResize();
    });
    resizeObserver.observe(chartRef.value)
  } else {
    // 降级到 window resize 事件
    window.addEventListener('resize', resizeChart)
  }
}

const cleanupAutoResize = () => {
  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  } else {
    window.removeEventListener('resize', resizeChart)
  }
}

onMounted(() => {
  // onMounted时不再直接调用initChart，而是让ResizeObserver和watch来驱动
  setupAutoResize()
  // 初始调用一次，以防容器已经可见但ResizeObserver未立即触发
  nextTick(() => {
      if (chartRef.value && chartRef.value.offsetWidth > 0 && chartRef.value.offsetHeight > 0) {
          initChart();
      }
  });
})

onUnmounted(() => {
  cleanupAutoResize()
  
  if (chartInstance.value) {
    chartInstance.value.dispose()
    chartInstance.value = null
  }
})

// 暴露方法给父组件
defineExpose({
  getChart: () => chartInstance.value,
  resize: resizeChart,
  updateChart,
  showLoading: (options = props.loadingOptions) => {
    if (chartInstance.value) {
      chartInstance.value.showLoading(options)
    }
  },
  hideLoading: () => {
    if (chartInstance.value) {
      chartInstance.value.hideLoading()
    }
  }
})
</script>

<style scoped>
.echarts-container {
  position: relative;
  overflow: hidden;
}
</style>
