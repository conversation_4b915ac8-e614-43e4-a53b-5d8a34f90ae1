# 春雪食品产销一体化分析平台

## 1. 项目概述

春雪食品产销一体化分析平台是一个现代化的数据驱动决策支持系统。它旨在整合公司的核心业务数据，包括生产、销售、库存和价格，通过直观的可视化界面，为管理层提供实时、准确的业务洞察。

平台的核心价值在于：
- **数据集中化**：打破数据孤岛，将分散在不同Excel表格中的数据统一管理。
- **决策智能化**：通过多维度数据分析和趋势预测，辅助制定更科学的生产和销售策略。
- **流程自动化**：提供高效、可靠的数据导入机制，减少人工操作，确保数据准确性。

---

## 2. 技术栈

本平台采用前后端分离架构，并配备了强大的独立数据处理模块。

### 2.1. 前端 (Frontend)

- **框架**: [Vue 3](https://vuejs.org/) + [Vite](https://vitejs.dev/)
- **状态管理**: [Pinia](https://pinia.vuejs.org/)
- **路由**: [Vue Router](https://router.vuejs.org/)
- **数据可视化**: [ECharts](https://echarts.apache.org/)
- **HTTP客户端**: [Axios](https://axios-http.com/)
- **日期处理**: [Day.js](https://day.js.org/)
- **包管理器**: `npm`

### 2.2. 后端 (Backend)

- **运行环境**: [Cloudflare Workers](https://workers.cloudflare.com/)
- **Web框架**: [Hono](https://hono.dev/)
- **数据库**: [Cloudflare D1](https://developers.cloudflare.com/d1/) (基于 SQLite)
- **ORM**: [Drizzle ORM](https://orm.drizzle.team/)
- **认证**: [JWT](https://jwt.io/) (JSON Web Tokens)
- **部署工具**: `wrangler`

### 2.3. 数据处理 (Data Import)

- **语言**: [Python 3](https://www.python.org/)
- **核心库**: [Pandas](https://pandas.pydata.org/)
- **数据源**: Excel 文件 (`.xlsx`)

---

## 3. 核心功能模块

1.  **综合仪表盘 (Dashboard)**
    - 展示关键业务指标（KPIs），如总销售额、总产量、库存水平等。
    - 提供核心数据的快速概览和图表汇总。

2.  **库存管理 (Inventory Management)**
    - 实时库存水平监控与查询。
    - 库存周转率和库存天数分析。
    - 基于历史数据的库存趋势预测。

3.  **销售分析 (Sales Analysis)**
    - 按产品、区域、时间等多维度分析销售数据。
    - 销售额、销量和平均售价的趋势图表。
    - 关键产品的销售表现追踪。

4.  **生产监控 (Production Monitoring)**
    - 每日/每周/每月产量统计。
    - 产销比分析，帮助优化生产计划。
    - 生产数据与销售数据对比分析。

5.  **价格监控 (Price Monitoring)**
    - 追踪超过250种核心产品的价格变化。
    - 不同产品间的价格对比和趋势分析。
    - 价格波动预警机制。

6.  **数据导入系统 (Data Import System)**
    - 支持两种模式：SQL批量导入和API增量导入。
    - 内置“7步筛选法”等核心业务逻辑，确保数据清洗的准确性。
    - 提供详细的日志和错误处理机制。

---

## 4. 系统架构

平台采用经典的云原生架构，具备高可用性和可扩展性。

```
┌──────────────────┐      ┌───────────────────────┐      ┌─────────────────┐
│   用户浏览器      │──────▶│  Cloudflare CDN/WAF   │──────▶│   前端 (Vue SPA)  │
└──────────────────┘      └───────────────────────┘      └─────────────────┘
                                      │
                                      ▼
┌──────────────────┐      ┌───────────────────────┐      ┌─────────────────┐
│ Python数据导入脚本 │──────▶│ Cloudflare Workers API│◀─────▶│ Cloudflare D1 DB│
└──────────────────┘      └───────────────────────┘      └─────────────────┘
```

---

## 5. 数据导入

数据导入是本系统的基石。为确保数据导入的效率和稳定性，系统设计了两种核心策略。详情请参考 [`data_import/README.md`](data_import/README.md)。

- **SQL文件导入 (`bulk_import_main.py`)**: **（推荐）**
  - **原理**: 在本地生成完整的SQL脚本，通过 `wrangler d1 execute` 命令一次性执行。
  - **优点**: 性能极高、稳定可靠、保证数据事务的原子性。
  - **适用场景**: 首次全量数据迁移、月度/季度数据归档、大规模数据修复。

- **API批次导入 (`daily_import_main.py`)**: **（备选）**
  - **原理**: 通过HTTP API，分批次向Cloudflare Worker发送JSON数据。
  - **优点**: 灵活性高，可实现更复杂的实时逻辑。
  - **适用场景**: 每日增量数据更新、小批量数据修正。

---

## 6. 开发与部署

### 6.1. 环境准备

- [Node.js](https://nodejs.org/) (v18+) 和 `npm`
- [Python 3](https://www.python.org/) 和 `pip`
- 全局安装 `wrangler`: `npm install -g wrangler`
- 登录 Cloudflare: `wrangler login`

### 6.2. 开发

```bash
# 1. 配置环境 (首次运行时需要)
# 根据我们新创建的模板文件，复制并配置您的本地环境
cp frontend/.env.example frontend/.env.development
cp backend/wrangler.toml.example backend/wrangler.toml
# 注意：请根据需要修改 .env.development 和 wrangler.toml 文件中的内容。

# 2. 安装依赖并启动服务
# 启动前端开发服务器 (http://localhost:5173)
cd frontend
npm install
npm run dev

# 启动后端开发服务器 (http://localhost:8787)
cd backend
npm install
npm run dev
```

### 6.3. 部署

```bash
# 部署后端API到Cloudflare
cd backend
npm run deploy

# 构建并部署前端到Cloudflare Pages
cd frontend
npm run build
# 部署命令通常与CI/CD集成，或使用wrangler pages deploy
```

### 6.4. 数据导入

```bash
# 确保Python依赖已安装
cd data_import
# 该命令会使用我们新创建的 data_import/requirements.txt 文件
pip install -r requirements.txt

# 执行最稳定、推荐的SQL文件导入
python3 bulk_import_main.py --mode sql
```

---

## 7. 维护注意事项

1.  **部署顺序**: 务必先部署后端，再部署前端，以避免API不兼容问题。
2.  **数据备份**: 在执行大规模数据导入或删除操作前，请使用 `wrangler` 手动备份D1数据库。
3.  **缓存问题**: 前端更新后，提示用户清除浏览器缓存或使用 `Ctrl+F5` 强制刷新。
4.  **环境变量**: 生产环境和开发环境的API端点等配置存储在 `.env` 文件中，请勿将其提交到版本库。

---

## 8. 联系方式

如果在使用或开发过程中遇到任何问题，请通过以下方式联系：
- **提交 Issue**: 在本仓库的 "Issues" 页面提交详细的问题描述。
- **联系技术团队**: <EMAIL>