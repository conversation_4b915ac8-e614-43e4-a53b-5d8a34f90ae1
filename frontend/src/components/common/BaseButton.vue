<template>
  <button
    :type="type"
    :disabled="disabled || loading"
    :class="buttonClasses"
    @click="handleClick"
  >
    <span v-if="loading" class="button-loading">
      <div class="loading-spinner"></div>
    </span>
    <span v-if="icon && !loading" class="button-icon">
      {{ icon }}
    </span>
    <span class="button-text">
      <slot>{{ text }}</slot>
    </span>
  </button>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  type: {
    type: String,
    default: 'button',
    validator: (value) => ['button', 'submit', 'reset'].includes(value)
  },
  variant: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'secondary', 'success', 'warning', 'danger', 'ghost', 'link'].includes(value)
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  text: {
    type: String,
    default: ''
  },
  icon: {
    type: String,
    default: ''
  },
  loading: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  block: {
    type: Boolean,
    default: false
  },
  round: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

const buttonClasses = computed(() => [
  'base-button',
  `btn-${props.variant}`,
  `btn-${props.size}`,
  {
    'btn-block': props.block,
    'btn-round': props.round,
    'btn-loading': props.loading,
    'btn-disabled': props.disabled
  }
])

const handleClick = (event) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style scoped>
.base-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: none;
  border-radius: 8px;
  font-family: inherit;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.base-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 91, 172, 0.2);
}

/* 尺寸变体 */
.btn-small {
  padding: 6px 12px;
  font-size: 0.8em;
  min-height: 32px;
}

.btn-medium {
  padding: 10px 16px;
  font-size: 0.9em;
  min-height: 40px;
}

.btn-large {
  padding: 14px 20px;
  font-size: 1em;
  min-height: 48px;
}

/* 颜色变体 */
.btn-primary {
  background: var(--primary-blue);
  color: white;
}

.btn-primary:hover:not(.btn-disabled):not(.btn-loading) {
  background: #004a94;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 91, 172, 0.3);
}

.btn-secondary {
  background: var(--secondary-blue);
  color: white;
}

.btn-secondary:hover:not(.btn-disabled):not(.btn-loading) {
  background: #3a98d9;
  transform: translateY(-1px);
}

.btn-success {
  background: var(--success-green);
  color: white;
}

.btn-success:hover:not(.btn-disabled):not(.btn-loading) {
  background: #2db653;
  transform: translateY(-1px);
}

.btn-warning {
  background: var(--warning-orange);
  color: white;
}

.btn-warning:hover:not(.btn-disabled):not(.btn-loading) {
  background: #e6850e;
  transform: translateY(-1px);
}

.btn-danger {
  background: var(--signal-red);
  color: white;
}

.btn-danger:hover:not(.btn-disabled):not(.btn-loading) {
  background: #c41e3a;
  transform: translateY(-1px);
}

.btn-ghost {
  background: transparent;
  color: var(--primary-blue);
  border: 1px solid var(--primary-blue);
}

.btn-ghost:hover:not(.btn-disabled):not(.btn-loading) {
  background: var(--primary-blue);
  color: white;
  transform: translateY(-1px);
}

.btn-link {
  background: transparent;
  color: var(--primary-blue);
  text-decoration: underline;
  border: none;
  padding: 4px 8px;
}

.btn-link:hover:not(.btn-disabled):not(.btn-loading) {
  color: #004a94;
  text-decoration: none;
}

/* 状态变体 */
.btn-block {
  width: 100%;
}

.btn-round {
  border-radius: 20px;
}

.btn-loading {
  pointer-events: none;
  opacity: 0.8;
}

.btn-disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* 加载状态 */
.button-loading {
  display: flex;
  align-items: center;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.button-icon {
  display: flex;
  align-items: center;
  font-size: 1.1em;
}

.button-text {
  display: flex;
  align-items: center;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .btn-large {
    padding: 12px 18px;
    font-size: 0.9em;
  }
  
  .btn-medium {
    padding: 8px 14px;
    font-size: 0.85em;
  }
  
  .btn-small {
    padding: 6px 10px;
    font-size: 0.75em;
  }
}
</style>
