<template>
  <BaseCard title="库存趋势分析" :loading="loading" :error="error">
    <template #extra>
      <div class="chart-controls">
        <select v-model="selectedProducts" @change="updateProducts" class="product-selector" multiple>
          <option v-for="product in availableProducts" :key="product" :value="product">
            {{ product }}
          </option>
        </select>
        
        <select v-model="trendType" @change="updateTrendType" class="trend-selector">
          <option value="total">总库存趋势</option>
          <option value="top5">TOP5产品趋势</option>
          <option value="selected">选定产品趋势</option>
        </select>
      </div>
    </template>

    <LineChart
      :data="chartData"
      x-field="date"
      y-field="value"
      :series-field="trendType === 'total' ? null : 'product'"
      :loading="loading"
      :height="height"
      :smooth="true"
      :area="trendType === 'total'"
      :colors="chartColors"
      :y-axis-formatter="yAxisFormatter"
      :tooltip-formatter="tooltipFormatter"
      @chart-ready="onChartReady"
      @chart-click="onChartClick"
    />

    <div class="trend-summary">
      <div class="summary-item">
        <span class="label">趋势方向：</span>
        <span class="value" :class="trendColorClass">
          {{ trendIcon }} {{ trendText }}
        </span>
      </div>
      <div class="summary-item">
        <span class="label">变化幅度：</span>
        <span class="value">{{ changeAmplitude }}</span>
      </div>
      <div class="summary-item">
        <span class="label">数据点数：</span>
        <span class="value">{{ dataPoints }}个</span>
      </div>
      <div class="summary-item">
        <span class="label">平均库存：</span>
        <span class="value">{{ formatInventory(averageInventory) }}</span>
      </div>
    </div>
  </BaseCard>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { fetchData } from '@/utils/api'
import { formatInventory, getTrendIcon, getTrendColorClass } from '@/utils/formatters'
import { CHART_COLORS } from '@/utils/constants'
import BaseCard from '@/components/common/BaseCard.vue'
import LineChart from '@/components/charts/LineChart.vue'
const props = defineProps({
  startDate: {
    type: String,
    default: '2025-06-01' // 备用默认值，实际值由父组件传入
  },
  endDate: {
    type: String,
    default: '2025-07-29' // 备用默认值，实际值由父组件传入
  },
  height: {
    type: String,
    default: '400px'
  }
})

const emit = defineEmits(['chart-ready', 'chart-click', 'data-updated'])

const loading = ref(false)
const error = ref('')
const trendData = ref([])
const trendType = ref('total')
const selectedProducts = ref([])

// 计算属性
const availableProducts = computed(() => {
  const products = new Set()
  trendData.value.forEach(item => {
    if (item.product_name) {
      products.add(item.product_name)
    }
  })
  return Array.from(products).sort()
})

const chartData = computed(() => {
  if (trendType.value === 'total') {
    // 总库存趋势
    const dailyTotals = new Map()
    
    trendData.value.forEach(item => {
      const date = item.date
      if (!dailyTotals.has(date)) {
        dailyTotals.set(date, 0)
      }
      dailyTotals.set(date, dailyTotals.get(date) + (item.inventory_level || 0))
    })
    
    return Array.from(dailyTotals.entries()).map(([date, value]) => ({
      date,
      value
    })).sort((a, b) => a.date.localeCompare(b.date))
  } else if (trendType.value === 'top5') {
    // TOP5产品趋势
    const productTotals = new Map()
    
    // 计算每个产品的总库存
    trendData.value.forEach(item => {
      const product = item.product_name
      if (!productTotals.has(product)) {
        productTotals.set(product, 0)
      }
      productTotals.set(product, productTotals.get(product) + (item.inventory_level || 0))
    })
    
    // 获取TOP5产品
    const top5Products = Array.from(productTotals.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([product]) => product)
    
    // 生成TOP5产品的趋势数据
    return trendData.value
      .filter(item => top5Products.includes(item.product_name))
      .map(item => ({
        date: item.date,
        product: item.product_name,
        value: item.inventory_level || 0
      }))
      .sort((a, b) => a.date.localeCompare(b.date))
  } else {
    // 选定产品趋势
    return trendData.value
      .filter(item => selectedProducts.value.includes(item.product_name))
      .map(item => ({
        date: item.date,
        product: item.product_name,
        value: item.inventory_level || 0
      }))
      .sort((a, b) => a.date.localeCompare(b.date))
  }
})

const dataPoints = computed(() => chartData.value.length)

const averageInventory = computed(() => {
  if (chartData.value.length === 0) return 0
  const total = chartData.value.reduce((sum, item) => sum + item.value, 0)
  return total / chartData.value.length
})

const trendDirection = computed(() => {
  if (chartData.value.length < 2) return 'stable'
  
  const first = chartData.value[0]?.value || 0
  const last = chartData.value[chartData.value.length - 1]?.value || 0
  
  if (last > first * 1.05) return 'increasing'
  if (last < first * 0.95) return 'decreasing'
  return 'stable'
})

const trendIcon = computed(() => {
  const icons = {
    increasing: '📈',
    decreasing: '📉',
    stable: '➡️'
  }
  return icons[trendDirection.value]
})

const trendText = computed(() => {
  const texts = {
    increasing: '上升趋势',
    decreasing: '下降趋势',
    stable: '平稳趋势'
  }
  return texts[trendDirection.value]
})

const trendColorClass = computed(() => {
  const classes = {
    increasing: 'trend-positive',
    decreasing: 'trend-negative',
    stable: 'trend-neutral'
  }
  return classes[trendDirection.value]
})

const changeAmplitude = computed(() => {
  if (chartData.value.length < 2) return '--'
  
  const values = chartData.value.map(item => item.value)
  const max = Math.max(...values)
  const min = Math.min(...values)
  
  if (min === 0) return '--'
  
  const amplitude = ((max - min) / min) * 100
  return amplitude.toFixed(1) + '%'
})

const chartColors = computed(() => [
  CHART_COLORS.PRIMARY,
  CHART_COLORS.SECONDARY,
  CHART_COLORS.ACCENT,
  CHART_COLORS.SUCCESS,
  CHART_COLORS.WARNING
])

// 格式化函数
const yAxisFormatter = (value) => {
  return formatInventory(value)
}

const tooltipFormatter = (params) => {
  if (Array.isArray(params)) {
    let result = `${params[0].axisValue}<br/>`
    params.forEach(param => {
      result += `${param.marker}${param.seriesName}: ${formatInventory(param.value)}<br/>`
    })
    return result
  } else {
    return `${params.axisValue}<br/>${params.seriesName}: ${formatInventory(params.value)}`
  }
}

// 方法
const fetchTrendData = async () => {
  loading.value = true
  error.value = ''
  
  try {
    // 模拟API调用 - 实际应该调用库存趋势API
    const response = await fetchData(`/api/inventory/trend?start_date=${props.startDate}&end_date=${props.endDate}`)
    
    if (response && Array.isArray(response)) {
      trendData.value = response
      emit('data-updated', trendData.value)
      console.log('✅ Inventory trend data loaded:', trendData.value.length, 'records')
    } else {
      throw new Error('Invalid data format')
    }
  } catch (err) {
    error.value = '数据加载失败'
    console.error('❌ Failed to load inventory trend data:', err)
    
    // 使用模拟数据
    trendData.value = generateMockData()
  } finally {
    loading.value = false
  }
}

const generateMockData = () => {
  const mockData = []
  const products = ['产品A', '产品B', '产品C', '产品D', '产品E']
  const startDate = new Date(props.startDate)
  const endDate = new Date(props.endDate)
  
  for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
    const dateStr = d.toISOString().split('T')[0]
    
    products.forEach(product => {
      mockData.push({
        date: dateStr,
        product_name: product,
        inventory_level: Math.random() * 1000 + 500
      })
    })
  }
  
  return mockData
}

const updateProducts = () => {
  console.log('Selected products updated:', selectedProducts.value)
}

const updateTrendType = () => {
  console.log('Trend type updated to:', trendType.value)
}

const onChartReady = (chart) => {
  emit('chart-ready', chart)
}

const onChartClick = (params) => {
  emit('chart-click', params)
}

// 监听props变化
watch([() => props.startDate, () => props.endDate], () => {
  fetchTrendData()
})

onMounted(() => {
  fetchTrendData()
})

// 暴露方法
defineExpose({
  refresh: fetchTrendData
})
</script>

<style scoped>
.chart-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.product-selector,
.trend-selector {
  padding: 6px 12px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.9em;
  background: white;
  cursor: pointer;
}

.product-selector {
  min-width: 150px;
  max-height: 100px;
}

.product-selector:focus,
.trend-selector:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.trend-summary {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  padding: 16px;
  background: var(--background-light);
  border-radius: 8px;
  border-top: 1px solid #f0f0f0;
}

.summary-item {
  text-align: center;
  flex: 1;
}

.label {
  display: block;
  color: var(--text-secondary);
  font-size: 0.85em;
  margin-bottom: 4px;
}

.value {
  display: block;
  color: var(--primary-blue);
  font-weight: 600;
  font-size: 1em;
}

.trend-positive {
  color: var(--success-green);
}

.trend-negative {
  color: var(--signal-red);
}

.trend-neutral {
  color: var(--neutral-gray);
}

@media (max-width: 768px) {
  .chart-controls {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .trend-summary {
    flex-direction: column;
    gap: 12px;
  }
  
  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .label, .value {
    display: inline;
    margin: 0;
  }
}
</style>
