import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { fetchData } from '@/utils/api'
import { getDefaultDateRange } from '@/utils/constants'

export const usePricingStore = defineStore('pricing', () => {
  // 状态
  const priceData = ref([])
  const priceHistoryData = ref([])
  const priceDetailData = ref([])
  const isLoading = ref(false)
  const error = ref(null)
  const lastUpdated = ref(null)
  
  // 日期范围 - 使用动态计算的默认日期范围
  const defaultRange = getDefaultDateRange()
  const dateRange = ref({
    start: defaultRange.START,
    end: defaultRange.END
  })

  // 计算属性
  const averagePrice = computed(() => {
    if (priceData.value.length === 0) return 0
    const total = priceData.value.reduce((sum, item) => sum + (item.price || 0), 0)
    return total / priceData.value.length
  })

  const maxPrice = computed(() => {
    if (priceData.value.length === 0) return 0
    return Math.max(...priceData.value.map(item => item.price || 0))
  })

  const minPrice = computed(() => {
    if (priceData.value.length === 0) return 0
    return Math.min(...priceData.value.map(item => item.price || 0))
  })

  const latestPrice = computed(() => {
    return priceData.value.length > 0 ? 
      priceData.value[priceData.value.length - 1]?.price || 0 : 0
  })

  const firstPrice = computed(() => {
    return priceData.value.length > 0 ? 
      priceData.value[0]?.price || 0 : 0
  })

  const priceChangeRate = computed(() => {
    if (firstPrice.value === 0) return 0
    return ((latestPrice.value - firstPrice.value) / firstPrice.value) * 100
  })

  const availableProducts = computed(() => {
    const products = new Set()
    priceHistoryData.value.forEach(item => {
      if (item.product_name) {
        products.add(item.product_name)
      }
    })
    return Array.from(products).sort()
  })

  const hasData = computed(() => priceData.value.length > 0 || priceHistoryData.value.length > 0)

  // 数据是否过期（超过5分钟）
  const isDataStale = computed(() => {
    if (!lastUpdated.value) return true
    const now = new Date()
    const updated = new Date(lastUpdated.value)
    return (now - updated) > 5 * 60 * 1000 // 5分钟
  })

  // 方法
  const fetchPriceData = async (startDate = dateRange.value.start, endDate = dateRange.value.end) => {
    isLoading.value = true
    error.value = null

    try {
      console.log('🔄 Fetching price data...')
      const response = await fetchData(`/api/trends/sales-price?start_date=${startDate}&end_date=${endDate}`)
      
      if (response && Array.isArray(response)) {
        priceData.value = response.map(item => ({
          date: item.date,
          price: parseFloat(item.price) || 0,
          volume: parseFloat(item.volume) || 0,
          amount: parseFloat(item.amount) || 0
        }))
        
        // 更新日期范围
        dateRange.value = { start: startDate, end: endDate }
        
        console.log('✅ Price data loaded:', priceData.value.length, 'records')
        return priceData.value
      } else {
        throw new Error('Invalid data format')
      }
    } catch (err) {
      error.value = err.message || '获取价格数据失败'
      console.error('❌ Failed to fetch price data:', err)
      
      // 使用模拟数据
      priceData.value = generateMockPriceData(startDate, endDate)
      return priceData.value
    } finally {
      isLoading.value = false
    }
  }

  const fetchPriceHistoryData = async (startDate = dateRange.value.start, endDate = dateRange.value.end) => {
    try {
      console.log('🔄 Fetching price history data...')
      const response = await fetchData(`/api/sales?start_date=${startDate}&end_date=${endDate}`)
      
      if (response && Array.isArray(response)) {
        priceHistoryData.value = response.map(item => ({
          date: item.date,
          product_name: item.product_name,
          price: parseFloat(item.price) || 0,
          volume: parseFloat(item.volume) || 0,
          amount: parseFloat(item.amount) || 0
        }))
        
        console.log('✅ Price history data loaded:', priceHistoryData.value.length, 'records')
        return priceHistoryData.value
      } else {
        throw new Error('Invalid data format')
      }
    } catch (err) {
      console.error('❌ Failed to fetch price history data:', err)
      
      // 使用模拟数据
      priceHistoryData.value = generateMockPriceHistoryData(startDate, endDate)
      return priceHistoryData.value
    }
  }

  const fetchAllPricingData = async (startDate = dateRange.value.start, endDate = dateRange.value.end) => {
    isLoading.value = true
    error.value = null

    try {
      // 并行获取数据
      const [priceDataResult, historyDataResult] = await Promise.all([
        fetchPriceData(startDate, endDate),
        fetchPriceHistoryData(startDate, endDate)
      ])

      lastUpdated.value = new Date().toISOString()
      return { priceData: priceDataResult, historyData: historyDataResult }
    } catch (err) {
      error.value = err.message || '获取价格数据失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const refreshData = async () => {
    return await fetchAllPricingData(dateRange.value.start, dateRange.value.end)
  }

  const updateDateRange = async (startDate, endDate) => {
    dateRange.value = { start: startDate, end: endDate }
    return await fetchAllPricingData(startDate, endDate)
  }

  const clearData = () => {
    priceData.value = []
    priceHistoryData.value = []
    error.value = null
    lastUpdated.value = null
  }

  const clearError = () => {
    error.value = null
  }

  // 生成模拟数据
  const generateMockPriceData = (startDate, endDate) => {
    const mockData = []
    const start = new Date(startDate)
    const end = new Date(endDate)
    let basePrice = 5000 + Math.random() * 2000 // 基础价格5000-7000元/T
    
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split('T')[0]
      
      // 价格波动 ±5%
      const priceChange = (Math.random() - 0.5) * 0.1
      basePrice = basePrice * (1 + priceChange)
      
      const volume = Math.random() * 100 + 50
      const amount = basePrice * volume
      
      mockData.push({
        date: dateStr,
        price: basePrice,
        volume: volume,
        amount: amount
      })
    }
    
    return mockData
  }

  const generateMockPriceHistoryData = (startDate, endDate) => {
    const mockData = []
    const products = ['产品A', '产品B', '产品C', '产品D', '产品E']
    const start = new Date(startDate)
    const end = new Date(endDate)
    
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split('T')[0]
      
      products.forEach(product => {
        const basePrice = 4000 + Math.random() * 3000 // 4000-7000元/T
        const volume = Math.random() * 50 + 20
        const amount = basePrice * volume
        
        mockData.push({
          date: dateStr,
          product_name: product,
          price: basePrice,
          volume: volume,
          amount: amount
        })
      })
    }
    
    return mockData
  }

  // 获取特定日期的价格
  const getPriceByDate = (date) => {
    return priceData.value.find(item => item.date === date)
  }

  // 获取特定产品的价格历史
  const getPriceByProduct = (productName) => {
    return priceHistoryData.value.filter(item => item.product_name === productName)
  }

  // 获取价格趋势（增长/下降）
  const getPriceTrend = () => {
    if (priceData.value.length < 2) return 'stable'
    
    const recent = priceData.value.slice(-7) // 最近7天
    const earlier = priceData.value.slice(-14, -7) // 之前7天
    
    if (recent.length === 0 || earlier.length === 0) return 'stable'
    
    const recentAvg = recent.reduce((sum, item) => sum + item.price, 0) / recent.length
    const earlierAvg = earlier.reduce((sum, item) => sum + item.price, 0) / earlier.length
    
    if (recentAvg > earlierAvg * 1.05) return 'increasing'
    if (recentAvg < earlierAvg * 0.95) return 'decreasing'
    return 'stable'
  }

  // 获取价格分布统计
  const getPriceDistribution = () => {
    if (priceData.value.length === 0) return []
    
    const prices = priceData.value.map(item => item.price)
    const min = Math.min(...prices)
    const max = Math.max(...prices)
    const range = max - min
    const bucketSize = range / 10 // 分10个区间
    
    const distribution = []
    for (let i = 0; i < 10; i++) {
      const bucketMin = min + i * bucketSize
      const bucketMax = min + (i + 1) * bucketSize
      const count = prices.filter(price => price >= bucketMin && price < bucketMax).length
      
      distribution.push({
        range: `${bucketMin.toFixed(0)}-${bucketMax.toFixed(0)}`,
        count: count,
        percentage: (count / prices.length) * 100
      })
    }
    
    return distribution
  }

  // --- Price Monitoring Extension ---
  
  // 价格监控状态
  const trendData = ref([])
  const alerts = ref([])
  const rankings = ref([])
  const priceStats = ref({})
  const alertConfig = ref({})
  const monitoringLoading = ref(false)

  // 价格监控计算属性
  const unacknowledgedAlerts = computed(() =>
    alerts.value.filter(alert => !alert.is_acknowledged)
  )

  const alertsCountByLevel = computed(() => {
    const counts = { critical: 0, warning: 0, info: 0 }
    alerts.value.forEach(alert => {
      const level = alert.alert_level.toLowerCase()
      if (counts[level] !== undefined) {
        counts[level]++
      }
    })
    return counts
  })

  // 价格监控方法
  const fetchTrendData = async (timeRange = '30d', productId = null, productName = null) => {
    monitoringLoading.value = true
    try {
      const params = new URLSearchParams({ time_range: timeRange })
      if (productId) params.append('product_id', productId)
      if (productName) params.append('product_name', productName)
      
      const response = await fetchData(`/api/prices/trends?${params.toString()}`)
      
      if (response.success) {
        trendData.value = response.data.trends
        priceStats.value = response.data.summary
        return response.data
      } else {
        throw new Error(response.message || '获取价格趋势数据失败')
      }
    } catch (err) {
      error.value = err.message || '获取价格趋势数据失败'
      console.error('❌ Failed to fetch trend data:', err)
      throw err
    } finally {
      monitoringLoading.value = false
    }
  }

  const fetchAlerts = async (filters = {}) => {
    try {
      const params = new URLSearchParams()
      Object.keys(filters).forEach(key => {
        if (filters[key] !== undefined && filters[key] !== null) {
          params.append(key, filters[key])
        }
      })
      
      const response = await fetchData(`/api/prices/alerts?${params.toString()}`)
      
      if (response.success) {
        alerts.value = response.data.alerts
        return response.data
      } else {
        throw new Error(response.message || '获取价格预警数据失败')
      }
    } catch (err) {
      error.value = err.message || '获取价格预警数据失败'
      console.error('❌ Failed to fetch alerts:', err)
      throw err
    }
  }

  const fetchRankings = async (period = 'daily', rankingType = 'drop_amount') => {
    try {
      const params = new URLSearchParams({
        period,
        ranking_type: rankingType,
        limit: '20'
      })
      
      const response = await fetchData(`/api/prices/rankings?${params.toString()}`)
      
      if (response.success) {
        rankings.value = response.data.rankings
        return response.data
      } else {
        throw new Error(response.message || '获取价格排行榜失败')
      }
    } catch (err) {
      error.value = err.message || '获取价格排行榜失败'
      console.error('❌ Failed to fetch rankings:', err)
      throw err
    }
  }

  const fetchAlertConfig = async () => {
    try {
      const response = await fetchData('/api/prices/alert-configs')
      
      if (response.success) {
        alertConfig.value = response.data.active_config || {}
        return response.data
      } else {
        throw new Error(response.message || '获取预警配置失败')
      }
    } catch (err) {
      error.value = err.message || '获取预警配置失败'
      console.error('❌ Failed to fetch alert config:', err)
      throw err
    }
  }

  const acknowledgeAlert = async (alertId) => {
    try {
      const response = await fetchData(`/api/prices/alerts/${alertId}/acknowledge`, {
        method: 'POST'
      })

      if (response.success) {
        // 更新本地状态
        const alert = alerts.value.find(a => a.alert_id === alertId)
        if (alert) {
          alert.is_acknowledged = true
        }
        return response
      } else {
        throw new Error(response.message || '确认预警失败')
      }
    } catch (err) {
      error.value = err.message || '确认预警失败'
      console.error('❌ Failed to acknowledge alert:', err)
      throw err
    }
  }

  const acknowledgeAllAlerts = async () => {
    const unacknowledged = unacknowledgedAlerts.value
    const promises = unacknowledged.map(alert => acknowledgeAlert(alert.alert_id))
    
    try {
      await Promise.all(promises)
      return { success: true, count: unacknowledged.length }
    } catch (err) {
      throw err
    }
  }

  const runAlertDetection = async (date = null) => {
    try {
      const targetDate = date || new Date().toISOString().split('T')[0]
      const response = await fetchData('/api/prices/run-alert-detection', {
        method: 'POST',
        body: JSON.stringify({ date: targetDate })
      })

      if (response.success) {
        // 刷新预警数据
        await fetchAlerts()
        return response
      } else {
        throw new Error(response.message || '运行预警检测失败')
      }
    } catch (err) {
      error.value = err.message || '运行预警检测失败'
      console.error('❌ Failed to run alert detection:', err)
      throw err
    }
  }

  const saveAlertConfig = async (config) => {
    try {
      const response = await fetchData('/api/prices/alert-configs', {
        method: 'POST',
        body: JSON.stringify(config)
      })

      if (response.success) {
        alertConfig.value = { ...config, config_id: response.config_id }
        return response
      } else {
        throw new Error(response.message || '保存预警配置失败')
      }
    } catch (err) {
      error.value = err.message || '保存预警配置失败'
      console.error('❌ Failed to save alert config:', err)
      throw err
    }
  }

  const refreshMonitoringData = async () => {
    monitoringLoading.value = true
    try {
      await Promise.all([
        fetchTrendData(),
        fetchAlerts(),
        fetchRankings(),
        fetchAlertConfig()
      ])
      lastUpdated.value = new Date().toISOString()
    } catch (err) {
      console.error('❌ Failed to refresh monitoring data:', err)
      throw err
    } finally {
      monitoringLoading.value = false
    }
  }

  const fetchPriceDetailData = async (startDate = dateRange.value.start, endDate = dateRange.value.end) => {
    isLoading.value = true
    error.value = null

    try {
      console.log('🔄 Fetching price detail data...')
      
      // 使用现有的价格历史数据构建详细数据
      if (priceHistoryData.value.length === 0) {
        await fetchPriceHistoryData(startDate, endDate)
      }
      
      // 将价格历史数据转换为详细数据格式
      const productGroups = {}
      priceHistoryData.value.forEach(item => {
        if (!productGroups[item.product_name]) {
          productGroups[item.product_name] = []
        }
        productGroups[item.product_name].push(item)
      })
      
      priceDetailData.value = Object.entries(productGroups).map(([productName, items]) => {
        // 按日期排序
        items.sort((a, b) => new Date(a.date) - new Date(b.date))
        
        const currentPrice = items[items.length - 1]?.price || 0
        const historicalPrice = items[0]?.price || 0
        const priceChange = currentPrice - historicalPrice
        const changeRate = historicalPrice > 0 ? (priceChange / historicalPrice) * 100 : 0
        
        return {
          id: productName,
          product_name: productName,
          current_price: currentPrice,
          historical_price: historicalPrice,
          price_change: priceChange,
          change_rate: changeRate,
          updated_at: items[items.length - 1]?.date || new Date().toISOString()
        }
      })
      
      console.log('✅ Price detail data loaded:', priceDetailData.value.length, 'products')
      return priceDetailData.value
    } catch (err) {
      error.value = err.message || '获取价格详情数据失败'
      console.error('❌ Failed to fetch price detail data:', err)
      
      // 使用模拟数据
      priceDetailData.value = generateMockPriceDetailData()
      return priceDetailData.value
    } finally {
      isLoading.value = false
    }
  }

  const generateMockPriceDetailData = () => {
    const products = ['产品A', '产品B', '产品C', '产品D', '产品E']
    return products.map((product, index) => {
      const currentPrice = 4000 + Math.random() * 3000
      const historicalPrice = 4000 + Math.random() * 3000
      const priceChange = currentPrice - historicalPrice
      const changeRate = (priceChange / historicalPrice) * 100
      
      return {
        id: product,
        product_name: product,
        current_price: currentPrice,
        historical_price: historicalPrice,
        price_change: priceChange,
        change_rate: changeRate,
        updated_at: new Date().toISOString()
      }
    })
  }

  return {
    // 原有状态
    priceData,
    priceHistoryData,
    priceDetailData,
    isLoading,
    error,
    lastUpdated,
    dateRange,
    
    // 原有计算属性
    averagePrice,
    maxPrice,
    minPrice,
    latestPrice,
    firstPrice,
    priceChangeRate,
    availableProducts,
    hasData,
    isDataStale,
    
    // 原有方法
    fetchPriceData,
    fetchPriceHistoryData,
    fetchPriceDetailData,
    fetchAllPricingData,
    refreshData,
    updateDateRange,
    clearData,
    clearError,
    getPriceByDate,
    getPriceByProduct,
    getPriceTrend,
    getPriceDistribution,

    // 价格监控状态
    trendData,
    alerts,
    rankings,
    priceStats,
    alertConfig,
    monitoringLoading,

    // 价格监控计算属性
    unacknowledgedAlerts,
    alertsCountByLevel,

    // 价格监控方法
    fetchTrendData,
    fetchAlerts,
    fetchRankings,
    fetchAlertConfig,
    acknowledgeAlert,
    acknowledgeAllAlerts,
    runAlertDetection,
    saveAlertConfig,
    refreshMonitoringData
  }
})
