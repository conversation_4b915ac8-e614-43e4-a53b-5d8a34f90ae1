# 产销分析系统错误处理文档

## 1. 概述

健壮的错误处理是确保系统稳定性和提供良好用户体验的关键。本系统在前端、后端API和数据处理流程中都设计了明确的错误处理机制。

## 2. 后端API (Cloudflare Worker)

后端API是错误处理的核心。所有端点都遵循统一的错误响应格式。

### 2.1. 错误响应结构

当错误发生时，API应返回一个标准的JSON对象，并设置合适的HTTP状态码。

```json
{
  "error": "错误的简短描述",
  "details": "(可选) 错误的详细信息或技术细节"
}
```

### 2.2. HTTP状态码

*   **`200 OK`:** 请求成功。
*   **`400 Bad Request`:** 客户端请求错误。这通常是由于缺少必要的查询参数或参数格式不正确。响应体中应包含具体原因。
    *   *示例:* `GET /api/trends/sales-price` 但未提供 `start_date`。
*   **`401 Unauthorized` / `403 Forbidden`:** (用于未来扩展) 身份验证或授权失败。
*   **`404 Not Found`:** 请求的API端点不存在。
*   **`500 Internal Server Error`:** 服务器内部错误。这通常是由于数据库查询失败、代码中出现意外异常或与其他服务（如D1）的通信问题。详细的错误信息应记录在服务器日志中（通过 `console.error`），但不应直接暴露给最终用户，以避免泄露敏感信息。

### 2.3. 具体处理逻辑

所有API端点都应包含在 `try...catch` 块中。

```typescript
// Hono框架中的错误处理示例
app.get('/api/some-data', async (c) => {
  try {
    const { param } = c.req.query();
    if (!param) {
      return c.json({ error: '缺少 param 查询参数' }, 400);
    }

    // ... 数据库查询逻辑 ...
    const { results } = await c.env.DB.prepare(...).all();
    return c.json(results);

  } catch (e: any) {
    console.error('API Error:', e); // 在Cloudflare日志中记录详细错误
    return c.json({ error: '服务器内部错误', details: e.message }, 500);
  }
});
```

## 3. 前端 (JavaScript)

前端负责优雅地向用户展示错误信息，并防止因API错误导致整个页面崩溃。

### 3.1. API请求处理

所有 `fetch` 调用都应使用 `.catch()` 或 `try...catch` (在使用 `async/await` 时) 来捕获网络错误或API返回的非2xx响应。

```javascript
async function fetchData(url) {
  try {
    const response = await fetch(url);

    if (!response.ok) {
      // 处理API返回的已知错误 (4xx, 5xx)
      const errorData = await response.json();
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    return await response.json();

  } catch (error) {
    // 处理网络故障或上述抛出的错误
    console.error('Failed to fetch data:', error);
    // 在UI上显示一个友好的错误消息
    showUIMessage(`数据加载失败: ${error.message}`, 'error');
    return null; // 返回null或空数组，防止后续代码出错
  }
}
```

### 3.2. UI反馈

*   **图表加载:** 当图表对应的数据加载失败时，应在图表容器内显示一条错误消息（例如，“无法加载图表数据”），而不是一个空的或损坏的图表。
*   **数据上传:** 文件上传过程中的任何错误（文件格式不符、文件过大、处理失败等）都应在状态显示区域明确地告知用户。

## 4. 数据导入流程

### 4.1. 离线Python脚本

*   脚本应包含对文件路径、Excel工作表名称的验证。
*   使用 `try...except` 捕获Pandas读取或处理数据时可能发生的异常。
*   在关键步骤（如读取文件、写入SQL）打印清晰的日志信息，方便调试。

### 4.2. 在线上传Worker

*   **文件验证:** 在处理前检查 `file` 对象是否存在且为 `File` 类型。
*   **解析错误:** 使用 `try...catch` 包围 `XLSX.read()` 和 `XLSX.utils.sheet_to_json()`，以捕获因文件损坏或格式不兼容导致的解析错误。
*   **批量插入:** D1的 `batch()` API会返回一个结果数组，其中包含每个语句的成功状态。应检查这些结果，以确定是否有部分插入失败，并据此返回更精确的成功或失败信息。


---
**文档版本**: v1.0
**最后更新**: 2025-07-30
**负责人**: AI Assistant
