<template>
  <EChartsWrapper
    :options="chartOptions"
    :width="width"
    :height="height"
    :loading="loading"
    @chart-ready="onChartReady"
    @chart-click="onChartClick"
  />
</template>

<script setup>
import { computed } from 'vue'
import EChartsWrapper from './EChartsWrapper.vue'
import { getBaseChartOption, getPieSeriesConfig } from '@/utils/charts'
import { CHART_COLORS } from '@/utils/constants'

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  nameField: {
    type: String,
    default: 'name'
  },
  valueField: {
    type: String,
    default: 'value'
  },
  title: {
    type: String,
    default: ''
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '400px'
  },
  loading: {
    type: Boolean,
    default: false
  },
  radius: {
    type: Array,
    default: () => ['40%', '70%']
  },
  center: {
    type: Array,
    default: () => ['50%', '50%']
  },
  showLabel: {
    type: Boolean,
    default: true
  },
  showLabelLine: {
    type: Boolean,
    default: true
  },
  colors: {
    type: Array,
    default: () => [
      CHART_COLORS.PRIMARY,
      CHART_COLORS.SECONDARY,
      CHART_COLORS.ACCENT,
      CHART_COLORS.SUCCESS,
      CHART_COLORS.WARNING,
      '#8B5CF6',
      '#06B6D4',
      '#84CC16',
      '#F59E0B',
      '#EF4444'
    ]
  },
  labelFormatter: {
    type: Function,
    default: (params) => `${params.name}: ${params.value} (${params.percent}%)`
  },
  tooltipFormatter: {
    type: Function,
    default: (params) => `${params.name}<br/>${params.value} (${params.percent}%)`
  }
})

const emit = defineEmits(['chart-ready', 'chart-click'])

// 处理数据
const processedData = computed(() => {
  if (!props.data || props.data.length === 0) return []

  return props.data.map((item, index) => ({
    name: item[props.nameField],
    value: item[props.valueField],
    itemStyle: {
      color: props.colors[index % props.colors.length]
    }
  }))
})

// 图表配置
const chartOptions = computed(() => {
  const data = processedData.value
  
  return {
    ...getBaseChartOption(),
    title: props.title ? {
      text: props.title,
      left: 'center',
      textStyle: {
        color: CHART_COLORS.PRIMARY,
        fontSize: 16,
        fontWeight: 'bold'
      }
    } : undefined,
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: CHART_COLORS.PRIMARY,
      borderWidth: 1,
      textStyle: {
        color: '#333333',
        fontSize: 12
      },
      formatter: props.tooltipFormatter
    },
    legend: {
      type: 'scroll',
      orient: 'vertical',
      right: 10,
      top: 20,
      bottom: 20,
      data: data.map(item => item.name),
      textStyle: {
        color: '#666666',
        fontSize: 12
      }
    },
    series: [
      getPieSeriesConfig('数据', data, {
        radius: props.radius,
        center: props.center,
        showLabel: props.showLabel,
        showLabelLine: props.showLabelLine,
        labelFormatter: props.labelFormatter
      })
    ]
  }
})

const onChartReady = (chart) => {
  emit('chart-ready', chart)
}

const onChartClick = (params) => {
  emit('chart-click', params)
}
</script>
