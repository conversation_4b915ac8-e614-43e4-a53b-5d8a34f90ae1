<template>
  <BaseCard title="销量分布分析" :loading="loading" :error="error">
    <template #extra>
      <select v-model="chartType" @change="updateChartType" class="chart-type-selector">
        <option value="bar">柱状图</option>
        <option value="line">折线图</option>
        <option value="area">面积图</option>
      </select>
    </template>

    <EChartsWrapper
      :options="chartOptions"
      height="400px"
      :loading="loading"
      @chart-ready="onChartReady"
      @chart-click="onChartClick"
    />

    <div class="volume-summary">
      <div class="summary-item">
        <span class="label">日均销量：</span>
        <span class="value">{{ formatSalesVolume(dailyAverage) }}</span>
      </div>
      <div class="summary-item">
        <span class="label">最高销量：</span>
        <span class="value">{{ formatSalesVolume(maxVolume) }}</span>
      </div>
      <div class="summary-item">
        <span class="label">最低销量：</span>
        <span class="value">{{ formatSalesVolume(minVolume) }}</span>
      </div>
      <div class="summary-item">
        <span class="label">波动系数：</span>
        <span class="value">{{ volatilityCoefficient }}</span>
      </div>
    </div>
  </BaseCard>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useSalesStore } from '@/stores/sales'
import { formatSalesVolume } from '@/utils/formatters'
import { getBaseChartOption, getTooltipConfig, getXAxisConfig, getYAxisConfig, getLineSeriesConfig, getBarSeriesConfig, getGridConfig } from '@/utils/charts'
import { CHART_COLORS } from '@/utils/constants'
import BaseCard from '@/components/common/BaseCard.vue'
import EChartsWrapper from '@/components/charts/EChartsWrapper.vue'

const props = defineProps({
  startDate: {
    type: String,
    default: '2025-06-01' // 备用默认值，实际值由父组件传入
  },
  endDate: {
    type: String,
    default: '2025-07-29' // 备用默认值，实际值由父组件传入
  }
})

const emit = defineEmits(['chart-ready', 'chart-click', 'data-updated'])

const salesStore = useSalesStore()
const loading = ref(false)
const error = ref('')
const chartType = ref('bar')
const chartInstance = ref(null)

// 计算属性
const volumeData = computed(() => {
  return salesStore.salesPriceData.map(item => ({
    date: item.date,
    volume: item.volume
  }))
})

const dailyAverage = computed(() => {
  if (volumeData.value.length === 0) return 0
  const total = volumeData.value.reduce((sum, item) => sum + item.volume, 0)
  return total / volumeData.value.length
})

const maxVolume = computed(() => {
  if (volumeData.value.length === 0) return 0
  return Math.max(...volumeData.value.map(item => item.volume))
})

const minVolume = computed(() => {
  if (volumeData.value.length === 0) return 0
  return Math.min(...volumeData.value.map(item => item.volume))
})

const volatilityCoefficient = computed(() => {
  if (volumeData.value.length === 0 || dailyAverage.value === 0) return '--'
  
  const variance = volumeData.value.reduce((sum, item) => {
    const diff = item.volume - dailyAverage.value
    return sum + (diff * diff)
  }, 0) / volumeData.value.length
  
  const standardDeviation = Math.sqrt(variance)
  const coefficient = (standardDeviation / dailyAverage.value) * 100
  
  return coefficient.toFixed(1) + '%'
})

// 图表配置
const chartOptions = computed(() => {
  if (volumeData.value.length === 0) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#999',
          fontSize: 14
        }
      }
    }
  }

  const dates = volumeData.value.map(item => item.date)
  const volumes = volumeData.value.map(item => item.volume)

  const baseConfig = {
    ...getBaseChartOption(),
    title: {
      text: '销量分布分析',
      left: 'center',
      textStyle: {
        color: CHART_COLORS.PRIMARY,
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: getTooltipConfig((params) => {
      const point = params[0]
      return `${point.axisValue}<br/>销量: ${formatSalesVolume(point.value)}`
    }),
    xAxis: getXAxisConfig(dates, { rotate: 45 }),
    yAxis: getYAxisConfig({
      name: '销量(T)',
      formatter: (value) => formatSalesVolume(value)
    }),
    grid: getGridConfig()
  }

  // 根据图表类型生成不同的系列配置
  switch (chartType.value) {
    case 'line':
      baseConfig.series = [
        getLineSeriesConfig('销量', volumes, {
          color: CHART_COLORS.PRIMARY,
          smooth: true
        })
      ]
      break
    case 'area':
      baseConfig.series = [
        getLineSeriesConfig('销量', volumes, {
          color: CHART_COLORS.PRIMARY,
          smooth: true,
          area: true
        })
      ]
      break
    case 'bar':
    default:
      baseConfig.series = [
        getBarSeriesConfig('销量', volumes, {
          color: CHART_COLORS.PRIMARY,
          showLabel: true,
          labelFormatter: (params) => formatSalesVolume(params.value)
        })
      ]
      break
  }

  // 添加平均线
  baseConfig.series[0].markLine = {
    data: [{
      yAxis: dailyAverage.value,
      lineStyle: {
        color: CHART_COLORS.ACCENT,
        type: 'dashed',
        width: 2
      },
      label: {
        formatter: `平均值: ${formatSalesVolume(dailyAverage.value)}`,
        position: 'end'
      }
    }]
  }

  return baseConfig
})

// 方法
const fetchData = async () => {
  loading.value = true
  error.value = ''
  
  try {
    await salesStore.fetchSalesPriceData(props.startDate, props.endDate)
    emit('data-updated', volumeData.value)
  } catch (err) {
    error.value = '数据加载失败'
    console.error('Failed to load sales volume data:', err)
  } finally {
    loading.value = false
  }
}

const updateChartType = () => {
  console.log('Chart type updated to:', chartType.value)
}

const onChartReady = (chart) => {
  chartInstance.value = chart
  emit('chart-ready', chart)
}

const onChartClick = (params) => {
  emit('chart-click', params)
}

// 监听props变化
watch([() => props.startDate, () => props.endDate], () => {
  fetchData()
})

onMounted(() => {
  fetchData()
})

// 暴露方法
defineExpose({
  refresh: fetchData,
  getChart: () => chartInstance.value
})
</script>

<style scoped>
.chart-type-selector {
  padding: 6px 12px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.9em;
  background: white;
  cursor: pointer;
}

.chart-type-selector:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.volume-summary {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  padding: 16px;
  background: var(--background-light);
  border-radius: 8px;
  border-top: 1px solid #f0f0f0;
}

.summary-item {
  text-align: center;
  flex: 1;
}

.label {
  display: block;
  color: var(--text-secondary);
  font-size: 0.85em;
  margin-bottom: 4px;
}

.value {
  display: block;
  color: var(--primary-blue);
  font-weight: 600;
  font-size: 1em;
}

@media (max-width: 768px) {
  .volume-summary {
    flex-direction: column;
    gap: 12px;
  }
  
  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .label, .value {
    display: inline;
    margin: 0;
  }
}
</style>
