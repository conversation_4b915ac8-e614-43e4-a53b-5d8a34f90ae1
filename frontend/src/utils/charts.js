/**
 * 图表配置工具函数
 */

import { CHART_COLORS, CHART_THEME } from './constants'
import { formatNumber, formatCurrency, formatPercentage } from './formatters'

// 基础图表配置
export const getBaseChartOption = () => ({
  backgroundColor: CHART_THEME.backgroundColor,
  textStyle: CHART_THEME.textStyle,
  animation: true,
  animationDuration: 300,
  animationEasing: 'cubicOut'
})

// 获取通用的tooltip配置
export const getTooltipConfig = (formatter = null) => ({
  trigger: 'axis',
  axisPointer: {
    type: 'cross',
    crossStyle: {
      color: CHART_COLORS.NEUTRAL
    }
  },
  backgroundColor: 'rgba(255, 255, 255, 0.95)',
  borderColor: CHART_COLORS.PRIMARY,
  borderWidth: 1,
  textStyle: {
    color: '#333333',
    fontSize: 12
  },
  formatter: formatter
})

// 获取通用的图例配置
export const getLegendConfig = (data = []) => ({
  data,
  top: 30,
  textStyle: {
    color: '#666666',
    fontSize: 12
  }
})

// 获取通用的网格配置
export const getGridConfig = (options = {}) => ({
  left: '3%',
  right: '4%',
  bottom: '15%',
  top: '15%',
  containLabel: true,
  ...options
})

// 获取X轴配置
export const getXAxisConfig = (data = [], options = {}) => ({
  type: 'category',
  data,
  axisLabel: {
    color: '#333333', // 从#666666改为更深的颜色
    fontSize: 11,
    fontWeight: 'bold', // 加粗横轴标签
    rotate: options.rotate || 0,
    interval: options.interval || 'auto'
  },
  axisLine: {
    lineStyle: {
      color: '#E0E0E0'
    }
  },
  axisTick: {
    show: false
  },
  ...options
})

// 获取Y轴配置
export const getYAxisConfig = (options = {}) => ({
  type: 'value',
  axisLabel: {
    color: '#666666',
    fontSize: 11,
    formatter: options.formatter || '{value}'
  },
  axisLine: {
    show: false
  },
  axisTick: {
    show: false
  },
  splitLine: {
    lineStyle: {
      color: '#F0F0F0',
      type: 'dashed'
    }
  },
  ...options
})

// 获取双Y轴配置
export const getDualYAxisConfig = (leftOptions = {}, rightOptions = {}) => [
  {
    ...getYAxisConfig(leftOptions),
    position: 'left'
  },
  {
    ...getYAxisConfig(rightOptions),
    position: 'right'
  }
]

// 线图系列配置
export const getLineSeriesConfig = (name, data, options = {}) => ({
  name,
  type: 'line',
  data,
  smooth: options.smooth !== false,
  symbol: options.symbol || 'circle',
  symbolSize: options.symbolSize || 6,
  lineStyle: {
    color: options.color || CHART_COLORS.PRIMARY,
    width: options.lineWidth || 3
  },
  itemStyle: {
    color: options.color || CHART_COLORS.PRIMARY
  },
  areaStyle: options.area ? {
    color: {
      type: 'linear',
      x: 0, y: 0, x2: 0, y2: 1,
      colorStops: [
        { offset: 0, color: options.color || CHART_COLORS.PRIMARY + '40' },
        { offset: 1, color: options.color || CHART_COLORS.PRIMARY + '10' }
      ]
    }
  } : undefined,
  yAxisIndex: options.yAxisIndex || 0,
  ...options.extra
})

// 柱状图系列配置
export const getBarSeriesConfig = (name, data, options = {}) => ({
  name,
  type: 'bar',
  data,
  itemStyle: {
    color: options.color || CHART_COLORS.PRIMARY,
    borderRadius: options.borderRadius || [4, 4, 0, 0]
  },
  barWidth: options.barWidth || '60%',
  yAxisIndex: options.yAxisIndex || 0,
  label: options.showLabel ? {
    show: true,
    position: 'top',
    formatter: options.labelFormatter || '{c}',
    color: '#666666',
    fontSize: 11
  } : undefined,
  ...options.extra
})

// 饼图系列配置
export const getPieSeriesConfig = (name, data, options = {}) => ({
  name,
  type: 'pie',
  data,
  radius: options.radius || ['40%', '70%'],
  center: options.center || ['50%', '50%'],
  itemStyle: {
    borderRadius: options.borderRadius || 8,
    borderColor: '#ffffff',
    borderWidth: 2
  },
  label: {
    show: options.showLabel !== false,
    formatter: options.labelFormatter || '{b}: {c} ({d}%)',
    fontSize: 11,
    color: '#666666'
  },
  labelLine: {
    show: options.showLabelLine !== false
  },
  emphasis: {
    itemStyle: {
      shadowBlur: 10,
      shadowOffsetX: 0,
      shadowColor: 'rgba(0, 0, 0, 0.5)'
    }
  },
  ...options.extra
})

// 获取sparkline配置（小型趋势图）
export const getSparklineOption = (data, color = CHART_COLORS.PRIMARY) => ({
  ...getBaseChartOption(),
  grid: {
    left: 0,
    right: 0,
    top: 0,
    bottom: 0
  },
  xAxis: {
    type: 'category',
    show: false,
    data: data.map((_, index) => index)
  },
  yAxis: {
    type: 'value',
    show: false
  },
  series: [{
    type: 'line',
    data,
    smooth: true,
    symbol: 'none',
    lineStyle: {
      color,
      width: 2
    },
    areaStyle: {
      color: {
        type: 'linear',
        x: 0, y: 0, x2: 0, y2: 1,
        colorStops: [
          { offset: 0, color: color + '40' },
          { offset: 1, color: color + '10' }
        ]
      }
    }
  }]
})

// 销售价格趋势图配置
export const getSalesPriceChartOption = (data) => ({
  ...getBaseChartOption(),
  title: {
    text: '销售量与平均单价趋势',
    left: 'center',
    textStyle: {
      color: CHART_COLORS.PRIMARY,
      fontSize: 16,
      fontWeight: 'bold'
    }
  },
  tooltip: getTooltipConfig((params) => {
    let result = `${params[0].axisValue}<br/>`
    params.forEach(param => {
      const unit = param.seriesName === '销量' ? 'T' : '元/T'
      result += `${param.marker}${param.seriesName}: ${formatNumber(param.value)}${unit}<br/>`
    })
    return result
  }),
  legend: getLegendConfig(['销量', '平均单价']),
  xAxis: getXAxisConfig(data.map(item => item.date), { rotate: 45 }),
  yAxis: getDualYAxisConfig(
    { name: '销量(T)', formatter: '{value}T' },
    { name: '单价(元/T)', formatter: '{value}元' }
  ),
  series: [
    getLineSeriesConfig('销量', data.map(item => item.volume), {
      color: CHART_COLORS.PRIMARY,
      yAxisIndex: 0
    }),
    getLineSeriesConfig('平均单价', data.map(item => item.price), {
      color: CHART_COLORS.SECONDARY,
      yAxisIndex: 1
    })
  ],
  grid: getGridConfig()
})

// 库存TOP15柱状图配置
export const getInventoryTopChartOption = (data) => ({
  ...getBaseChartOption(),
  title: {
    text: '库存量TOP15产品',
    left: 'center',
    textStyle: {
      color: CHART_COLORS.PRIMARY,
      fontSize: 16,
      fontWeight: 'bold'
    }
  },
  tooltip: getTooltipConfig((params) => {
    return `${params.name}<br/>库存量: ${formatNumber(params.value)}T`
  }),
  xAxis: getXAxisConfig(data.map(item => item.name), { rotate: 45 }),
  yAxis: getYAxisConfig({ name: '库存量(T)', formatter: '{value}T' }),
  series: [
    getBarSeriesConfig('库存量', data.map(item => item.value), {
      color: CHART_COLORS.PRIMARY,
      showLabel: true,
      labelFormatter: (params) => formatNumber(params.value) + 'T'
    })
  ],
  grid: getGridConfig({ bottom: '25%' })
})

// 产销率趋势图配置
export const getProductionRatioChartOption = (data) => ({
  ...getBaseChartOption(),
  title: {
    text: '产销率趋势分析',
    left: 'center',
    textStyle: {
      color: CHART_COLORS.PRIMARY,
      fontSize: 16,
      fontWeight: 'bold'
    }
  },
  tooltip: getTooltipConfig((params) => {
    return `${params[0].axisValue}<br/>产销率: ${formatPercentage(params[0].value)}`
  }),
  xAxis: getXAxisConfig(data.map(item => item.date), { rotate: 45 }),
  yAxis: getYAxisConfig({ 
    name: '产销率(%)', 
    formatter: '{value}%',
    min: 0,
    max: 150
  }),
  series: [
    getLineSeriesConfig('产销率', data.map(item => item.ratio), {
      color: CHART_COLORS.PRIMARY,
      area: true
    })
  ],
  // 添加100%基准线
  markLine: {
    data: [{
      yAxis: 100,
      lineStyle: {
        color: CHART_COLORS.ACCENT,
        type: 'dashed',
        width: 2
      },
      label: {
        formatter: '100%基准线',
        position: 'end'
      }
    }]
  },
  grid: getGridConfig()
})
