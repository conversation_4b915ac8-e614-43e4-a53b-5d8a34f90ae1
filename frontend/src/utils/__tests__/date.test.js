import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { calculateDateRange, getCurrentMonthRange, getMonthRange } from '../date'

describe('Date Utils', () => {
  beforeEach(() => {
    // Mock current date to ensure consistent test results
    vi.useFakeTimers()
    vi.setSystemTime(new Date('2025-07-26'))
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('calculateDateRange', () => {
    it('should calculate correct date range for 30 days', () => {
      const result = calculateDateRange(30)
      expect(result).toEqual({
        start: '2025-06-27',
        end: '2025-07-26'
      })
    })

    it('should calculate correct date range for 7 days', () => {
      const result = calculateDateRange(7)
      expect(result).toEqual({
        start: '2025-07-20',
        end: '2025-07-26'
      })
    })

    it('should calculate correct date range for 90 days', () => {
      const result = calculateDateRange(90)
      expect(result).toEqual({
        start: '2025-04-28',
        end: '2025-07-26'
      })
    })

    it('should calculate correct date range for 1 day', () => {
      const result = calculateDateRange(1)
      expect(result).toEqual({
        start: '2025-07-26',
        end: '2025-07-26'
      })
    })
  })

  describe('getCurrentMonthRange', () => {
    it('should return correct range for current month', () => {
      const result = getCurrentMonthRange()
      expect(result).toEqual({
        start: '2025-07-01',
        end: '2025-07-31'
      })
    })
  })

  describe('getMonthRange', () => {
    it('should return correct range for January', () => {
      const result = getMonthRange(2025, 1)
      expect(result).toEqual({
        start: '2025-01-01',
        end: '2025-01-31'
      })
    })

    it('should return correct range for February in leap year', () => {
      const result = getMonthRange(2024, 2)
      expect(result).toEqual({
        start: '2024-02-01',
        end: '2024-02-29'
      })
    })

    it('should return correct range for February in non-leap year', () => {
      const result = getMonthRange(2025, 2)
      expect(result).toEqual({
        start: '2025-02-01',
        end: '2025-02-28'
      })
    })

    it('should return correct range for December', () => {
      const result = getMonthRange(2025, 12)
      expect(result).toEqual({
        start: '2025-12-01',
        end: '2025-12-31'
      })
    })
  })
})