/**
 * 全局错误处理和日志系统
 */

class ErrorHandler {
  constructor() {
    this.errors = []
    this.maxErrors = 100 // 最多保存100个错误
    this.isProduction = import.meta.env.PROD
    this.setupGlobalHandlers()
  }

  /**
   * 设置全局错误处理器
   */
  setupGlobalHandlers() {
    // 捕获未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError({
        type: 'unhandledrejection',
        message: event.reason?.message || 'Unhandled Promise Rejection',
        stack: event.reason?.stack,
        timestamp: new Date().toISOString()
      })
    })

    // 捕获JavaScript错误
    window.addEventListener('error', (event) => {
      this.handleError({
        type: 'javascript',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack,
        timestamp: new Date().toISOString()
      })
    })

    // 捕获资源加载错误
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.handleError({
          type: 'resource',
          message: `Failed to load resource: ${event.target.src || event.target.href}`,
          element: event.target.tagName,
          timestamp: new Date().toISOString()
        })
      }
    }, true)
  }

  /**
   * 处理错误
   * @param {Object} error 错误对象
   */
  handleError(error) {
    // 添加到错误列表
    this.errors.unshift(error)
    
    // 保持错误列表大小
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(0, this.maxErrors)
    }

    // 控制台输出
    if (!this.isProduction) {
      console.error('🚨 Error captured:', error)
    }

    // 发送到监控服务（生产环境）
    if (this.isProduction) {
      this.reportError(error)
    }

    // 显示用户友好的错误提示
    this.showUserNotification(error)
  }

  /**
   * 手动记录错误
   * @param {Error|string} error 错误对象或消息
   * @param {Object} context 上下文信息
   */
  logError(error, context = {}) {
    const errorInfo = {
      type: 'manual',
      message: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : null,
      context,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent
    }

    this.handleError(errorInfo)
  }

  /**
   * 记录警告
   * @param {string} message 警告消息
   * @param {Object} context 上下文信息
   */
  logWarning(message, context = {}) {
    const warning = {
      type: 'warning',
      message,
      context,
      timestamp: new Date().toISOString(),
      url: window.location.href
    }

    if (!this.isProduction) {
      console.warn('⚠️ Warning:', warning)
    }
  }

  /**
   * 记录信息
   * @param {string} message 信息消息
   * @param {Object} context 上下文信息
   */
  logInfo(message, context = {}) {
    const info = {
      type: 'info',
      message,
      context,
      timestamp: new Date().toISOString()
    }

    if (!this.isProduction) {
      console.log('ℹ️ Info:', info)
    }
  }

  /**
   * 上报错误到监控服务
   * @param {Object} error 错误对象
   */
  async reportError(error) {
    try {
      // 这里可以集成第三方错误监控服务
      // 例如：Sentry, LogRocket, Bugsnag等
      
      // 模拟发送到监控服务
      const payload = {
        ...error,
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString()
      }

      // 实际项目中应该发送到真实的监控服务
      console.log('📡 Reporting error to monitoring service:', payload)
      
      // await fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(payload)
      // })
    } catch (reportError) {
      console.error('Failed to report error:', reportError)
    }
  }

  /**
   * 显示用户友好的错误提示
   * @param {Object} error 错误对象
   */
  showUserNotification(error) {
    // 避免显示过多的错误提示
    if (this.lastNotificationTime && Date.now() - this.lastNotificationTime < 5000) {
      return
    }

    this.lastNotificationTime = Date.now()

    // 根据错误类型显示不同的提示
    let message = '系统出现了一个问题，请稍后重试'
    
    switch (error.type) {
      case 'resource':
        message = '资源加载失败，请检查网络连接'
        break
      case 'unhandledrejection':
        message = '网络请求失败，请稍后重试'
        break
      case 'javascript':
        message = '页面功能异常，请刷新页面重试'
        break
    }

    // 创建错误提示元素
    const notification = document.createElement('div')
    notification.className = 'error-notification'
    notification.innerHTML = `
      <div class="error-notification-content">
        <span class="error-icon">⚠️</span>
        <span class="error-message">${message}</span>
        <button class="error-close" onclick="this.parentElement.parentElement.remove()">×</button>
      </div>
    `
    
    // 添加样式
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #fee;
      color: #c41e3a;
      padding: 12px 16px;
      border-radius: 8px;
      border: 1px solid #fcc;
      z-index: 9999;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      max-width: 400px;
      animation: slideIn 0.3s ease-out;
    `

    // 添加动画样式
    if (!document.getElementById('error-notification-styles')) {
      const styles = document.createElement('style')
      styles.id = 'error-notification-styles'
      styles.textContent = `
        @keyframes slideIn {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        .error-notification-content {
          display: flex;
          align-items: center;
          gap: 8px;
        }
        .error-close {
          background: none;
          border: none;
          font-size: 18px;
          cursor: pointer;
          color: #c41e3a;
          margin-left: auto;
        }
      `
      document.head.appendChild(styles)
    }

    document.body.appendChild(notification)

    // 5秒后自动移除
    setTimeout(() => {
      if (document.body.contains(notification)) {
        notification.style.animation = 'slideIn 0.3s ease-out reverse'
        setTimeout(() => {
          if (document.body.contains(notification)) {
            document.body.removeChild(notification)
          }
        }, 300)
      }
    }, 5000)
  }

  /**
   * 获取错误列表
   */
  getErrors() {
    return [...this.errors]
  }

  /**
   * 清除错误列表
   */
  clearErrors() {
    this.errors = []
  }

  /**
   * 获取错误统计
   */
  getErrorStats() {
    const stats = {
      total: this.errors.length,
      byType: {},
      recent: this.errors.slice(0, 10)
    }

    this.errors.forEach(error => {
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1
    })

    return stats
  }
}

// 创建全局实例
const errorHandler = new ErrorHandler()

export default errorHandler

// 便捷方法
export const logError = (error, context) => errorHandler.logError(error, context)
export const logWarning = (message, context) => errorHandler.logWarning(message, context)
export const logInfo = (message, context) => errorHandler.logInfo(message, context)
export const getErrors = () => errorHandler.getErrors()
export const getErrorStats = () => errorHandler.getErrorStats()

/**
 * Vue组合式函数：错误处理
 */
export function useErrorHandler() {
  const handleError = (error, context) => {
    errorHandler.logError(error, context)
  }

  const handleWarning = (message, context) => {
    errorHandler.logWarning(message, context)
  }

  const getErrorList = () => {
    return errorHandler.getErrors()
  }

  const getStats = () => {
    return errorHandler.getErrorStats()
  }

  return {
    handleError,
    handleWarning,
    getErrorList,
    getStats
  }
}
