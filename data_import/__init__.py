"""
数据导入包
重构版本的数据导入系统，将单体脚本分解为模块化组件

模块说明:
- config.py: 配置管理
- data_loader.py: 数据加载
- data_processors.py: 数据处理和清洗
- db_handler.py: 数据库操作
- main.py: 主程序协调器
"""

__version__ = "2.0.0"
__author__ = "AI Assistant"

# 导入主要组件
from .config import *
from .data_loader import load_all_data
from .data_processors import (
    process_inventory_data, 
    process_production_data, 
    process_sales_data,
    process_price_adjustments_data
)
from .db_handler import print_database_info

__all__ = [
    'load_all_data',
    'process_inventory_data',
    'process_production_data', 
    'process_sales_data',
    'process_price_adjustments_data',
    'print_database_info'
]