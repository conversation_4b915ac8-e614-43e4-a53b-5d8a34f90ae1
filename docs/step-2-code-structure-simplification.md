# 步骤二：简化并统一代码结构

## 目标和背景

### 目标
重构 `data_import/bulk_import_main.py` 文件，移除对废弃模块的依赖，建立清晰统一的数据导入入口点。

### 背景
当前的 `bulk_import_main.py` 文件存在对一个已废弃（或不存在）的 `daily_import_main.py` 的引用，这表明新旧代码逻辑耦合严重。这种情况导致：
- 代码理解困难，新开发者难以快速上手
- 维护成本高，修改一处可能影响多处
- 潜在的运行时错误，引用不存在的模块
- 技术债务累积，影响长期项目健康

### 现状分析
通过代码审查发现的主要问题：
1. **模块依赖混乱**：新旧导入逻辑混合存在
2. **入口点不明确**：多个导入方式并存，缺乏统一标准
3. **废弃代码残留**：`main_api_import` 函数依赖已不存在的逻辑
4. **文档缺失**：缺乏清晰的使用指南和架构说明

## 具体实施步骤

### 第一步：代码审计和依赖分析
1. **全面扫描导入依赖**
   ```bash
   # 检查所有Python文件中的import语句
   grep -r "import.*daily_import_main" data_import/
   grep -r "from.*daily_import_main" data_import/
   ```

2. **识别废弃函数调用**
   ```bash
   # 查找main_api_import函数的所有引用
   grep -r "main_api_import" data_import/
   ```

3. **分析函数依赖关系**
   - 绘制当前模块依赖图
   - 识别核心功能和辅助功能
   - 确定可以安全移除的部分

### 第二步：清理废弃代码
1. **移除导入语句**
   - 删除所有对 `daily_import_main` 的 import
   - 清理相关的 from 语句

2. **处理废弃函数**
   - 将 `main_api_import` 函数标记为 `@deprecated`
   - 添加明确的弃用警告
   - 设置移除时间表

3. **更新函数调用**
   - 替换所有对废弃函数的调用
   - 使用推荐的新接口

### 第三步：建立统一入口点
1. **重构主函数**
   - 简化 `bulk_import_main.py` 的主逻辑
   - 确保只有一个清晰的执行路径

2. **标准化接口**
   - 定义统一的函数签名
   - 建立一致的错误处理机制

3. **完善文档**
   - 添加详细的使用说明
   - 提供代码示例

## 代码示例

### 修改前的问题代码
```python
# bulk_import_main.py - 问题版本
import os
import sys
from daily_import_main import main_api_import  # 引用不存在的模块
from bulk_sql_import import generate_daily_metrics_sql

def main():
    """主函数 - 存在多个导入路径"""
    try:
        # 旧的API导入方式（已废弃）
        if len(sys.argv) > 1 and sys.argv[1] == '--api':
            main_api_import()  # 调用废弃函数
            return
        
        # 新的SQL文件导入方式
        bulk_sql_import()
        
    except ImportError as e:
        print(f"导入错误: {e}")
        # 错误处理不完善

def bulk_sql_import():
    """SQL文件导入逻辑"""
    # 实现逻辑...
    pass

if __name__ == "__main__":
    main()
```

### 修改后的清洁代码
```python
# bulk_import_main.py - 重构版本
"""
Spring Snow Food Analysis System - 数据导入主模块

这是数据导入的统一入口点，负责协调整个导入流程。
支持的导入方式：
1. SQL文件批量导入（推荐）
2. 直接数据库导入

使用方法：
    python bulk_import_main.py [--mode=sql|direct] [--config=config.json]

作者: Spring Snow Team
版本: 2.0.0
更新时间: 2025-01-01
"""

import os
import sys
import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any

# 只导入需要的模块
from bulk_sql_import import generate_daily_metrics_sql
from data_processors import process_inventory_data, process_production_data, process_sales_data
from config import get_config, validate_config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('data_import.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DataImportManager:
    """数据导入管理器 - 统一的导入接口"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化导入管理器
        
        Args:
            config_path: 配置文件路径，默认使用 config.py
        """
        self.config = get_config(config_path) if config_path else get_config()
        self.validate_environment()
    
    def validate_environment(self):
        """验证运行环境"""
        required_dirs = ['data_import', 'data']
        for dir_name in required_dirs:
            if not os.path.exists(dir_name):
                raise EnvironmentError(f"必需的目录不存在: {dir_name}")
        
        logger.info("✅ 环境验证通过")
    
    def import_via_sql_file(self) -> bool:
        """
        通过SQL文件导入数据（推荐方式）
        
        Returns:
            bool: 导入是否成功
        """
        try:
            logger.info("🚀 开始SQL文件导入流程")
            
            # 第一步：处理原始数据
            logger.info("📊 处理库存数据...")
            inventory_data = process_inventory_data(self.config['data_files']['inventory'])
            
            logger.info("🏭 处理生产数据...")
            production_data = process_production_data(self.config['data_files']['production'])
            
            logger.info("💰 处理销售数据...")
            sales_data = process_sales_data(self.config['data_files']['sales'])
            
            # 第二步：合并数据
            logger.info("🔄 合并日度指标数据...")
            daily_metrics = self._merge_daily_metrics(
                inventory_data, production_data, sales_data
            )
            
            # 第三步：生成SQL文件
            sql_file_path = f"output/daily_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql"
            logger.info(f"📝 生成SQL文件: {sql_file_path}")
            generate_daily_metrics_sql(daily_metrics, sql_file_path)
            
            # 第四步：验证生成的SQL
            if self._validate_sql_file(sql_file_path):
                logger.info("✅ SQL文件导入流程完成")
                logger.info(f"📁 SQL文件位置: {os.path.abspath(sql_file_path)}")
                logger.info("💡 请使用以下命令执行导入:")
                logger.info(f"   sqlite3 your_database.db < {sql_file_path}")
                return True
            else:
                logger.error("❌ SQL文件验证失败")
                return False
                
        except Exception as e:
            logger.error(f"❌ SQL文件导入失败: {str(e)}")
            return False
    
    def _merge_daily_metrics(self, inventory_data, production_data, sales_data) -> list:
        """合并各类数据为日度指标"""
        # 实现数据合并逻辑
        # 这里是简化示例，实际实现需要根据业务逻辑调整
        daily_metrics = []
        
        # 按日期和产品分组合并数据
        # ... 具体实现逻辑 ...
        
        logger.info(f"📈 生成日度指标记录数: {len(daily_metrics)}")
        return daily_metrics
    
    def _validate_sql_file(self, sql_file_path: str) -> bool:
        """验证生成的SQL文件"""
        try:
            with open(sql_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 基本验证
            if 'BEGIN TRANSACTION' not in content:
                logger.error("SQL文件缺少事务开始标记")
                return False
                
            if 'COMMIT' not in content:
                logger.error("SQL文件缺少事务提交标记")
                return False
                
            # 统计INSERT语句数量
            insert_count = content.count('INSERT INTO DailyMetrics')
            logger.info(f"📊 SQL文件包含 {insert_count} 条INSERT语句")
            
            return True
            
        except Exception as e:
            logger.error(f"SQL文件验证异常: {str(e)}")
            return False

# 废弃函数处理
@deprecated("main_api_import函数已废弃，请使用DataImportManager.import_via_sql_file()方法")
def main_api_import():
    """
    废弃的API导入函数
    
    此函数已被标记为废弃，将在下个版本中移除。
    请使用新的DataImportManager类进行数据导入。
    """
    logger.warning("⚠️  main_api_import函数已废弃，请更新您的代码")
    logger.info("💡 推荐使用: DataImportManager().import_via_sql_file()")
    raise DeprecationWarning("main_api_import函数已废弃，请使用新的导入接口")

def deprecated(message):
    """废弃装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            logger.warning(f"⚠️  废弃警告: {message}")
            return func(*args, **kwargs)
        return wrapper
    return decorator

def main():
    """
    主函数 - 统一的程序入口点
    
    支持的命令行参数：
    --mode: 导入模式 (sql|direct)，默认为sql
    --config: 配置文件路径，可选
    --help: 显示帮助信息
    """
    
    # 解析命令行参数
    import argparse
    parser = argparse.ArgumentParser(description='Spring Snow数据导入工具')
    parser.add_argument('--mode', choices=['sql', 'direct'], default='sql',
                       help='导入模式：sql(SQL文件) 或 direct(直接导入)')
    parser.add_argument('--config', help='配置文件路径')
    parser.add_argument('--version', action='version', version='2.0.0')
    
    args = parser.parse_args()
    
    try:
        # 初始化导入管理器
        import_manager = DataImportManager(args.config)
        
        # 根据模式执行导入
        if args.mode == 'sql':
            success = import_manager.import_via_sql_file()
        else:
            logger.error("❌ direct模式暂未实现，请使用sql模式")
            success = False
        
        # 输出结果
        if success:
            logger.info("🎉 数据导入流程成功完成")
            sys.exit(0)
        else:
            logger.error("💥 数据导入流程失败")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"💥 程序执行异常: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
```

### 配置文件示例
```python
# config.py - 更新后的配置
"""数据导入配置文件"""

import os

def get_config(config_path=None):
    """获取配置信息"""
    return {
        'data_files': {
            'inventory': 'data/inventory.xlsx',
            'production': 'data/production.xlsx', 
            'sales': 'data/sales.xlsx'
        },
        'output': {
            'sql_dir': 'output',
            'log_dir': 'logs'
        },
        'database': {
            'type': 'sqlite',
            'path': 'database.db'
        }
    }

def validate_config(config):
    """验证配置有效性"""
    required_keys = ['data_files', 'output', 'database']
    for key in required_keys:
        if key not in config:
            raise ValueError(f"配置缺少必需的键: {key}")
    return True
```

## 验证方法

### 功能验证
1. **导入流程测试**
   ```bash
   # 测试新的导入接口
   python bulk_import_main.py --mode=sql
   
   # 验证帮助信息
   python bulk_import_main.py --help
   
   # 测试版本信息
   python bulk_import_main.py --version
   ```

2. **废弃函数警告测试**
   ```python
   # 验证废弃警告是否正确显示
   try:
       main_api_import()
   except DeprecationWarning as e:
       print(f"✅ 废弃警告正常: {e}")
   ```

3. **错误处理测试**
   ```bash
   # 测试无效参数
   python bulk_import_main.py --mode=invalid
   
   # 测试缺失文件
   python bulk_import_main.py --config=nonexistent.json
   ```

### 代码质量验证
1. **静态代码分析**
   ```bash
   # 使用pylint检查代码质量
   pylint bulk_import_main.py
   
   # 使用flake8检查代码风格
   flake8 bulk_import_main.py
   ```

2. **导入依赖检查**
   ```bash
   # 验证所有导入都是有效的
   python -c "import bulk_import_main; print('✅ 导入成功')"
   ```

## 注意事项

### 向后兼容性
1. **渐进式迁移**：保留废弃函数一段时间，给用户迁移缓冲期
2. **清晰的迁移指南**：提供详细的代码迁移说明
3. **版本标记**：明确标记废弃时间和移除计划

### 错误处理
1. **友好的错误信息**：提供清晰的错误描述和解决建议
2. **日志记录**：详细记录操作过程，便于问题排查
3. **异常恢复**：在可能的情况下提供自动恢复机制

### 性能考虑
1. **延迟加载**：只在需要时导入重型模块
2. **内存管理**：及时释放不需要的数据结构
3. **进度反馈**：为长时间运行的操作提供进度指示

## 预期结果

### 直接效果
1. **代码清晰度提升**：移除混乱的依赖关系，建立清晰的模块结构
2. **维护成本降低**：统一的入口点减少维护复杂度
3. **错误率下降**：消除因废弃模块引用导致的运行时错误

### 间接效果
1. **开发效率提升**：新开发者更容易理解和使用代码
2. **系统稳定性增强**：减少因代码结构问题导致的系统异常
3. **技术债务减少**：清理历史遗留问题，为未来发展奠定基础

### 量化指标
- **代码复杂度**：预期降低30%以上
- **模块依赖数量**：减少至最小必需集合
- **文档覆盖率**：达到90%以上
- **单元测试覆盖率**：达到80%以上

## 后续优化建议

1. **自动化测试**：建立完整的单元测试和集成测试套件
2. **持续集成**：集成到CI/CD流程中，确保代码质量
3. **性能监控**：添加性能指标监控，及时发现性能问题
4. **用户反馈**：收集用户使用反馈，持续改进接口设计
