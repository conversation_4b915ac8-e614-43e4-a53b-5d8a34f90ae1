{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(python:*)", "Bash(npx wrangler d1 execute production-data --command \"SELECT COUNT(*) as total_records FROM production_sales_data;\" --local)", "Bash(ls:*)", "Bash(find:*)", "Bash(npx wrangler d1 execute:*)", "Bash(cd backend)", "Bash(npx wrangler d1 execute chunxue-prod-db --command \"SELECT name FROM sqlite_master WHERE type=''table'';\" --local)", "Bash(find /Volumes/Mac/项目文件/my-fullstack-project -name \"import_real_data.py\" -type f)", "Bash(timeout 60 python3 import_real_data.py)", "<PERSON><PERSON>(gtimeout:*)"], "deny": []}}