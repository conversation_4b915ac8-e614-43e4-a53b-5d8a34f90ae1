import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { fetchData } from '@/utils/api'

export const useInventoryStore = defineStore('inventory', () => {
  // 状态
  const inventoryData = ref([])
  const top15Data = ref([])
  const summaryData = ref(null)
  const isLoading = ref(false)
  const error = ref(null)
  const lastUpdated = ref(null)
  
  // 当前查询日期 - 将由组件传入或使用智能日期检测
  const currentDate = ref(null) // 初始为null，由API智能检测

  // 计算属性
  const totalInventory = computed(() => {
    // inventory/summary 端点已统一返回吨单位的 total_inventory
    return summaryData.value?.total_inventory || 0
  })

  const top15Total = computed(() => {
    // top15Data 中的 value 已经是吨
    return top15Data.value.reduce((sum, item) => sum + item.value, 0)
  })

  const top15Percentage = computed(() => {
    if (totalInventory.value === 0) {
      return 0
    }
    // 根据 top15Data 和总库存重新计算
    return (top15Total.value / totalInventory.value) * 100
  })
  const avgPrice = computed(() => summaryData.value?.avg_price || 0) // 这个值在新端点中不再提供
  
  const hasData = computed(() => inventoryData.value.length > 0)
  const hasTop15Data = computed(() => top15Data.value.length > 0)
  
  // 库存总量（格式化）
  const formattedTotalInventory = computed(() => {
    const value = totalInventory.value
    if (value >= 1000) {
      return (value / 1000).toFixed(1) + 'K'
    }
    return value.toFixed(1)
  })

  // 方法
  const fetchInventoryTop15 = async (date = currentDate.value, limit = 15) => {
    isLoading.value = true
    error.value = null

    try {
      console.log('🔄 Fetching inventory top15 data...')
      const response = await fetchData(`/api/inventory/top?date=${date}&limit=${limit}`)
      
      // Handle new response format with smart date detection
      let dataArray = response;
      if (response && response.data && Array.isArray(response.data)) {
        dataArray = response.data;
        
        // Update current date if backend found a different date with data
        if (response.actual_date && response.actual_date !== date) {
          console.log(`📅 Updated inventory date from ${date} to ${response.actual_date}`);
          currentDate.value = response.actual_date;
        }
      } else if (response && Array.isArray(response)) {
        // Backward compatibility: if response is directly an array
        dataArray = response;
      } else {
        dataArray = [];
      }
      
      if (Array.isArray(dataArray)) {
        // 后端API已经返回吨单位的数据，无需转换
        top15Data.value = dataArray.map(item => ({
          name: item.product_name,
          value: parseFloat(item.inventory_level) || 0, // 后端已转换为吨
          percentage: parseFloat(item.percentage) || 0
        }));
        
        console.log('✅ Inventory top15 data loaded:', top15Data.value.length, 'items');
        return top15Data.value;
      } else {
        // 如果返回的不是数组，则设置为空数组，避免 .map() 错误
        top15Data.value = [];
        console.warn('⚠️ Inventory top15 data is not an array, setting to empty.');
        return []; // 返回空数组而不是抛出错误
      }
    } catch (err) {
      error.value = err.message || '获取库存TOP15数据失败'
      console.error('❌ Failed to fetch inventory top15:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const fetchInventorySummary = async (date = currentDate.value) => { // 添加date参数
    try {
      console.log('🔄 Fetching total inventory summary data...')
      // 使用新的端点，添加必需的date参数
      const response = await fetchData(`/api/inventory/total-summary?date=${date}`)

      if (response) {
        summaryData.value = response
        
        // 如果返回了实际日期且与请求日期不同，更新当前日期
        if (response.actual_date && response.actual_date !== date) {
          console.log(`📅 更新库存日期从 ${date} 到 ${response.actual_date}`)
          currentDate.value = response.actual_date
        }
        
        console.log('✅ Total inventory summary data loaded:', response)
        return response
      } else {
        throw new Error('No total summary data received')
      }
    } catch (err) {
      console.error('❌ Failed to fetch total inventory summary:', err)
      throw err
    }
  }

  const fetchAllInventoryData = async (date = currentDate.value) => {
    isLoading.value = true
    error.value = null

    try {
      // 并行获取数据
      const [top15, summary] = await Promise.all([
        fetchInventoryTop15(date, 15),
        fetchInventorySummary(date) // 传递date参数
      ])

      lastUpdated.value = new Date().toISOString()
      return { top15, summary }
    } catch (err) {
      error.value = err.message || '获取库存数据失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const refreshData = async () => {
    return await fetchAllInventoryData(currentDate.value)
  }

  const updateDate = async (date) => {
    currentDate.value = date
    return await fetchAllInventoryData(date)
  }

  const clearData = () => {
    inventoryData.value = []
    top15Data.value = []
    summaryData.value = null
    error.value = null
    lastUpdated.value = null
  }

  const clearError = () => {
    error.value = null
  }

  // 获取特定产品的库存信息
  const getProductInventory = (productName) => {
    return inventoryData.value.find(item => item.product_name === productName)
  }

  // 获取库存排名
  const getInventoryRank = (productName) => {
    const index = top15Data.value.findIndex(item => item.name === productName)
    return index >= 0 ? index + 1 : null
  }

  return {
    // 状态
    inventoryData,
    top15Data,
    summaryData,
    isLoading,
    error,
    lastUpdated,
    currentDate,
    
    // 计算属性
    totalInventory,
    top15Total,
    top15Percentage,
    avgPrice,
    hasData,
    hasTop15Data,
    formattedTotalInventory,
    
    // 方法
    fetchInventoryTop15,
    fetchInventorySummary,
    fetchAllInventoryData,
    refreshData,
    updateDate,
    clearData,
    clearError,
    getProductInventory,
    getInventoryRank
  }
})
