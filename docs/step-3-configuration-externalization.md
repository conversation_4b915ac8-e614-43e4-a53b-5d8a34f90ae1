# 步骤三：外部化硬编码的配置

## 目标和背景

### 目标
修改 `data_processors.py` 和 `config.py` 文件，将硬编码的Excel列名和过滤条件外部化为可配置项，提高系统的灵活性和可维护性。

### 背景
当前数据处理逻辑中硬编码了大量的Excel列名（如 `物料名称`, `结存`）和过滤条件（如 `副产品`）。这种硬编码方式存在以下问题：

1. **脆弱性高**：Excel文件格式的微小变化就会导致脚本失败
2. **维护困难**：修改列名映射需要深入代码逻辑
3. **可扩展性差**：添加新的数据源或字段需要修改核心代码
4. **环境适应性弱**：不同环境或客户的Excel格式差异难以处理

### 风险分析
- **业务中断风险**：Excel格式变化导致数据导入失败
- **维护成本风险**：每次格式调整都需要技术人员介入
- **数据质量风险**：硬编码的过滤条件可能不适用于新数据

## 具体实施步骤

### 第一步：分析现有硬编码内容
1. **扫描硬编码字符串**
   ```bash
   # 查找所有硬编码的中文列名
   grep -r "物料名称\|结存\|主数量\|客户" data_import/
   
   # 查找硬编码的过滤条件
   grep -r "副产品\|鲜" data_import/
   ```

2. **分类硬编码内容**
   - Excel列名映射
   - 数据过滤条件
   - 业务规则常量
   - 文件路径和格式

3. **评估影响范围**
   - 识别所有使用硬编码的函数
   - 分析依赖关系
   - 确定重构优先级

### 第二步：设计配置结构
1. **创建配置模式**
   - 按业务领域分组配置
   - 支持多环境配置
   - 提供默认值和验证

2. **定义配置接口**
   - 统一的配置访问方法
   - 配置变更通知机制
   - 配置验证和错误处理

### 第三步：实施配置外部化
1. **更新config.py**
   - 添加列名映射配置
   - 定义过滤规则配置
   - 实现配置验证逻辑

2. **重构data_processors.py**
   - 使用配置替换硬编码
   - 添加配置依赖注入
   - 保持向后兼容性

3. **测试和验证**
   - 功能回归测试
   - 配置变更测试
   - 性能影响评估

## 代码示例

### 修改前的硬编码问题
```python
# data_processors.py - 问题版本
def process_inventory_data(file_path):
    """处理库存数据 - 硬编码版本"""
    df = pd.read_excel(file_path)
    
    # 硬编码的列名 - 脆弱！
    if '物料名称' not in df.columns:
        raise ValueError("缺少物料名称列")
    
    if '结存' not in df.columns:
        raise ValueError("缺少结存列")
    
    # 硬编码的过滤条件 - 不灵活！
    df = df[~df['物料名称'].str.contains('副产品', na=False)]
    df = df[~df['物料名称'].str.startswith('鲜', na=False)]
    
    # 硬编码的客户字段处理
    df = df[df['客户'].notna() & (df['客户'] != '')]
    
    return df

def process_sales_data(file_path):
    """处理销售数据 - 硬编码版本"""
    df = pd.read_excel(file_path)
    
    # 更多硬编码...
    df['average_price'] = (df['无税金额'] / df['主数量']) * 1.09
    
    return df
```

### 修改后的配置化版本

#### 1. 更新后的config.py
```python
# config.py - 配置化版本
"""
Spring Snow Food Analysis System - 配置管理模块

提供统一的配置管理接口，支持Excel列名映射、数据过滤规则等配置项的外部化管理。
"""

import json
import os
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class ColumnMapping:
    """Excel列名映射配置"""
    product_name: str = '物料名称'
    customer: str = '客户'
    balance: str = '结存'
    quantity: str = '主数量'
    tax_free_amount: str = '无税金额'
    tax_included_price: str = '含税单价'
    invoice_date: str = '发票日期'
    main_quantity: str = '主数量'
    currency_tax_free_amount: str = '本币无税金额'

@dataclass
class FilterRules:
    """数据过滤规则配置"""
    exclude_product_patterns: List[str] = None
    exclude_product_prefixes: List[str] = None
    exclude_categories: List[str] = None
    require_customer: bool = True
    
    def __post_init__(self):
        if self.exclude_product_patterns is None:
            self.exclude_product_patterns = ['副产品']
        if self.exclude_product_prefixes is None:
            self.exclude_product_prefixes = ['鲜']
        if self.exclude_categories is None:
            self.exclude_categories = ['副产品']

@dataclass
class BusinessRules:
    """业务规则配置"""
    tax_rate: float = 1.09
    price_calculation_method: str = 'tax_free_with_tax'  # 'tax_free_with_tax' 或 'tax_included'
    inventory_unit: str = 'T'  # 库存单位：T(吨) 或 K(千)
    date_format: str = '%Y-%m-%d'

@dataclass
class DataSourceConfig:
    """数据源配置"""
    inventory: ColumnMapping = None
    production: ColumnMapping = None
    sales: ColumnMapping = None
    
    def __post_init__(self):
        if self.inventory is None:
            self.inventory = ColumnMapping()
        if self.production is None:
            self.production = ColumnMapping()
        if self.sales is None:
            # 销售数据可能有不同的列名
            self.sales = ColumnMapping(
                customer='客户名称',  # 销售数据中客户列名可能不同
                quantity='主数量',
                tax_free_amount='本币无税金额'
            )

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or 'config/data_import_config.json'
        self._column_mappings = DataSourceConfig()
        self._filter_rules = FilterRules()
        self._business_rules = BusinessRules()
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 加载列名映射
                if 'column_mappings' in config_data:
                    self._load_column_mappings(config_data['column_mappings'])
                
                # 加载过滤规则
                if 'filter_rules' in config_data:
                    self._load_filter_rules(config_data['filter_rules'])
                
                # 加载业务规则
                if 'business_rules' in config_data:
                    self._load_business_rules(config_data['business_rules'])
                
                logger.info(f"✅ 配置文件加载成功: {self.config_file}")
                
            except Exception as e:
                logger.warning(f"⚠️  配置文件加载失败，使用默认配置: {e}")
        else:
            logger.info("📝 配置文件不存在，使用默认配置")
            self._create_default_config()
    
    def _load_column_mappings(self, config_data: Dict):
        """加载列名映射配置"""
        if 'inventory' in config_data:
            self._column_mappings.inventory = ColumnMapping(**config_data['inventory'])
        if 'production' in config_data:
            self._column_mappings.production = ColumnMapping(**config_data['production'])
        if 'sales' in config_data:
            self._column_mappings.sales = ColumnMapping(**config_data['sales'])
    
    def _load_filter_rules(self, config_data: Dict):
        """加载过滤规则配置"""
        self._filter_rules = FilterRules(**config_data)
    
    def _load_business_rules(self, config_data: Dict):
        """加载业务规则配置"""
        self._business_rules = BusinessRules(**config_data)
    
    def _create_default_config(self):
        """创建默认配置文件"""
        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
        
        default_config = {
            'column_mappings': {
                'inventory': asdict(self._column_mappings.inventory),
                'production': asdict(self._column_mappings.production),
                'sales': asdict(self._column_mappings.sales)
            },
            'filter_rules': asdict(self._filter_rules),
            'business_rules': asdict(self._business_rules)
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📝 默认配置文件已创建: {self.config_file}")
    
    def get_column_mapping(self, data_type: str) -> ColumnMapping:
        """获取指定数据类型的列名映射"""
        mapping_dict = {
            'inventory': self._column_mappings.inventory,
            'production': self._column_mappings.production,
            'sales': self._column_mappings.sales
        }
        
        if data_type not in mapping_dict:
            raise ValueError(f"不支持的数据类型: {data_type}")
        
        return mapping_dict[data_type]
    
    def get_filter_rules(self) -> FilterRules:
        """获取过滤规则"""
        return self._filter_rules
    
    def get_business_rules(self) -> BusinessRules:
        """获取业务规则"""
        return self._business_rules
    
    def validate_config(self) -> bool:
        """验证配置有效性"""
        try:
            # 验证列名映射
            for data_type in ['inventory', 'production', 'sales']:
                mapping = self.get_column_mapping(data_type)
                if not mapping.product_name:
                    raise ValueError(f"{data_type}数据缺少产品名称列映射")
            
            # 验证业务规则
            if self._business_rules.tax_rate <= 0:
                raise ValueError("税率必须大于0")
            
            logger.info("✅ 配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 配置验证失败: {e}")
            return False

# 全局配置管理器实例
_config_manager = None

def get_config_manager(config_file: Optional[str] = None) -> ConfigManager:
    """获取配置管理器实例（单例模式）"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager(config_file)
    return _config_manager

# 便捷函数
def get_column_mapping(data_type: str) -> ColumnMapping:
    """获取列名映射的便捷函数"""
    return get_config_manager().get_column_mapping(data_type)

def get_filter_rules() -> FilterRules:
    """获取过滤规则的便捷函数"""
    return get_config_manager().get_filter_rules()

def get_business_rules() -> BusinessRules:
    """获取业务规则的便捷函数"""
    return get_config_manager().get_business_rules()

# 向后兼容的配置字典（废弃，但保留以支持旧代码）
COLUMN_MAPPINGS = {
    'inventory': {
        'product_name': '物料名称',
        'customer': '客户',
        'balance': '结存'
    },
    'sales': {
        'product_name': '物料名称',
        'quantity': '主数量',
        'customer': '客户名称'
    }
}
```

#### 2. 重构后的data_processors.py
```python
# data_processors.py - 配置化版本
"""
Spring Snow Food Analysis System - 数据处理模块

使用外部化配置处理各类Excel数据，支持灵活的列名映射和过滤规则。
"""

import pandas as pd
import logging
from typing import Optional, List
from config import get_column_mapping, get_filter_rules, get_business_rules

logger = logging.getLogger(__name__)

class DataProcessor:
    """数据处理器基类"""
    
    def __init__(self, data_type: str):
        self.data_type = data_type
        self.column_mapping = get_column_mapping(data_type)
        self.filter_rules = get_filter_rules()
        self.business_rules = get_business_rules()
    
    def validate_columns(self, df: pd.DataFrame, required_columns: List[str]) -> bool:
        """验证DataFrame是否包含必需的列"""
        missing_columns = []
        
        for col_key in required_columns:
            # 从配置中获取实际的列名
            actual_col_name = getattr(self.column_mapping, col_key, None)
            if actual_col_name and actual_col_name not in df.columns:
                missing_columns.append(f"{col_key}({actual_col_name})")
        
        if missing_columns:
            raise ValueError(f"{self.data_type}数据缺少必需的列: {', '.join(missing_columns)}")
        
        return True
    
    def apply_product_filters(self, df: pd.DataFrame) -> pd.DataFrame:
        """应用产品过滤规则"""
        original_count = len(df)
        product_col = self.column_mapping.product_name
        
        # 过滤包含特定模式的产品
        for pattern in self.filter_rules.exclude_product_patterns:
            df = df[~df[product_col].str.contains(pattern, na=False)]
            logger.debug(f"过滤包含'{pattern}'的产品，剩余记录: {len(df)}")
        
        # 过滤特定前缀的产品
        for prefix in self.filter_rules.exclude_product_prefixes:
            df = df[~df[product_col].str.startswith(prefix, na=False)]
            logger.debug(f"过滤以'{prefix}'开头的产品，剩余记录: {len(df)}")
        
        # 过滤特定类别
        if hasattr(self.column_mapping, 'category') and self.column_mapping.category:
            category_col = self.column_mapping.category
            if category_col in df.columns:
                for category in self.filter_rules.exclude_categories:
                    df = df[df[category_col] != category]
                    logger.debug(f"过滤类别'{category}'，剩余记录: {len(df)}")
        
        filtered_count = original_count - len(df)
        if filtered_count > 0:
            logger.info(f"📊 {self.data_type}数据过滤: 原始{original_count}条 → 过滤后{len(df)}条 (过滤{filtered_count}条)")
        
        return df
    
    def apply_customer_filter(self, df: pd.DataFrame) -> pd.DataFrame:
        """应用客户过滤规则"""
        if not self.filter_rules.require_customer:
            return df
        
        customer_col = self.column_mapping.customer
        if customer_col and customer_col in df.columns:
            original_count = len(df)
            df = df[df[customer_col].notna() & (df[customer_col] != '')]
            filtered_count = original_count - len(df)
            
            if filtered_count > 0:
                logger.info(f"📊 过滤空客户记录: {filtered_count}条")
        
        return df

class InventoryProcessor(DataProcessor):
    """库存数据处理器"""
    
    def __init__(self):
        super().__init__('inventory')
    
    def process(self, file_path: str) -> pd.DataFrame:
        """处理库存数据"""
        logger.info(f"📊 开始处理库存数据: {file_path}")
        
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path)
            logger.info(f"📈 读取库存数据: {len(df)}条记录")
            
            # 验证必需的列
            required_columns = ['product_name', 'balance']
            if self.filter_rules.require_customer:
                required_columns.append('customer')
            
            self.validate_columns(df, required_columns)
            
            # 应用过滤规则
            df = self.apply_product_filters(df)
            df = self.apply_customer_filter(df)
            
            # 数据清理和转换
            balance_col = self.column_mapping.balance
            df[balance_col] = pd.to_numeric(df[balance_col], errors='coerce')
            df = df.dropna(subset=[balance_col])
            
            logger.info(f"✅ 库存数据处理完成: {len(df)}条有效记录")
            return df
            
        except Exception as e:
            logger.error(f"❌ 库存数据处理失败: {str(e)}")
            raise

class SalesProcessor(DataProcessor):
    """销售数据处理器"""
    
    def __init__(self):
        super().__init__('sales')
    
    def process(self, file_path: str) -> pd.DataFrame:
        """处理销售数据"""
        logger.info(f"💰 开始处理销售数据: {file_path}")
        
        try:
            df = pd.read_excel(file_path)
            logger.info(f"📈 读取销售数据: {len(df)}条记录")
            
            # 验证必需的列
            required_columns = ['product_name', 'quantity']
            if self.business_rules.price_calculation_method == 'tax_free_with_tax':
                required_columns.append('tax_free_amount')
            elif self.business_rules.price_calculation_method == 'tax_included':
                required_columns.append('tax_included_price')
            
            self.validate_columns(df, required_columns)
            
            # 应用过滤规则
            df = self.apply_product_filters(df)
            df = self.apply_customer_filter(df)
            
            # 计算平均价格
            df = self._calculate_average_price(df)
            
            logger.info(f"✅ 销售数据处理完成: {len(df)}条有效记录")
            return df
            
        except Exception as e:
            logger.error(f"❌ 销售数据处理失败: {str(e)}")
            raise
    
    def _calculate_average_price(self, df: pd.DataFrame) -> pd.DataFrame:
        """根据配置计算平均价格"""
        quantity_col = self.column_mapping.quantity
        
        if self.business_rules.price_calculation_method == 'tax_free_with_tax':
            # 使用无税金额计算含税价格
            tax_free_col = self.column_mapping.tax_free_amount
            df['average_price'] = (df[tax_free_col] / df[quantity_col]) * self.business_rules.tax_rate
            
        elif self.business_rules.price_calculation_method == 'tax_included':
            # 直接使用含税单价
            tax_included_col = self.column_mapping.tax_included_price
            df['average_price'] = df[tax_included_col]
        
        else:
            raise ValueError(f"不支持的价格计算方法: {self.business_rules.price_calculation_method}")
        
        return df

# 便捷函数（保持向后兼容）
def process_inventory_data(file_path: str) -> pd.DataFrame:
    """处理库存数据的便捷函数"""
    processor = InventoryProcessor()
    return processor.process(file_path)

def process_sales_data(file_path: str) -> pd.DataFrame:
    """处理销售数据的便捷函数"""
    processor = SalesProcessor()
    return processor.process(file_path)

def process_production_data(file_path: str) -> pd.DataFrame:
    """处理生产数据的便捷函数"""
    # 生产数据处理逻辑类似，这里简化
    processor = DataProcessor('production')
    df = pd.read_excel(file_path)
    df = processor.apply_product_filters(df)
    return df
```

#### 3. 默认配置文件示例
```json
{
  "column_mappings": {
    "inventory": {
      "product_name": "物料名称",
      "customer": "客户",
      "balance": "结存",
      "quantity": "主数量",
      "tax_free_amount": "无税金额",
      "tax_included_price": "含税单价"
    },
    "production": {
      "product_name": "物料名称",
      "quantity": "主数量",
      "date": "生产日期"
    },
    "sales": {
      "product_name": "物料名称",
      "customer": "客户名称",
      "quantity": "主数量",
      "tax_free_amount": "本币无税金额",
      "invoice_date": "发票日期"
    }
  },
  "filter_rules": {
    "exclude_product_patterns": ["副产品"],
    "exclude_product_prefixes": ["鲜"],
    "exclude_categories": ["副产品"],
    "require_customer": true
  },
  "business_rules": {
    "tax_rate": 1.09,
    "price_calculation_method": "tax_free_with_tax",
    "inventory_unit": "T",
    "date_format": "%Y-%m-%d"
  }
}
```

## 验证方法

### 配置验证测试
```python
# test_config.py
def test_config_loading():
    """测试配置加载功能"""
    from config import get_config_manager
    
    config_manager = get_config_manager()
    assert config_manager.validate_config()
    
    # 测试列名映射
    inventory_mapping = config_manager.get_column_mapping('inventory')
    assert inventory_mapping.product_name == '物料名称'
    
    # 测试过滤规则
    filter_rules = config_manager.get_filter_rules()
    assert '副产品' in filter_rules.exclude_product_patterns

def test_data_processing():
    """测试数据处理功能"""
    from data_processors import process_inventory_data
    
    # 使用测试数据验证处理结果
    df = process_inventory_data('test_data/inventory.xlsx')
    assert len(df) > 0
    assert '物料名称' in df.columns or df.columns[0] is not None
```

### 兼容性测试
```bash
# 测试不同Excel格式的兼容性
python -c "
import pandas as pd
from data_processors import process_inventory_data

# 测试标准格式
df1 = process_inventory_data('data/standard_inventory.xlsx')
print(f'标准格式处理结果: {len(df1)}条记录')

# 测试自定义格式（通过配置文件调整）
df2 = process_inventory_data('data/custom_inventory.xlsx')
print(f'自定义格式处理结果: {len(df2)}条记录')
"
```

## 注意事项

### 配置管理
1. **配置文件安全**：避免在配置文件中存储敏感信息
2. **版本控制**：配置文件变更需要版本控制和变更记录
3. **环境隔离**：不同环境使用不同的配置文件

### 向后兼容性
1. **渐进迁移**：保留旧的硬编码常量一段时间
2. **默认配置**：确保在没有配置文件时系统仍能正常工作
3. **错误处理**：配置加载失败时提供友好的错误信息

### 性能考虑
1. **配置缓存**：避免重复加载配置文件
2. **延迟验证**：只在使用时验证配置项
3. **内存优化**：大型配置文件的内存使用优化

## 预期结果

### 直接效果
1. **灵活性提升**：Excel格式变化时只需修改配置文件
2. **维护成本降低**：业务人员可以自行调整列名映射
3. **扩展性增强**：新增数据源时无需修改核心代码

### 间接效果
1. **系统稳定性**：减少因格式变化导致的系统故障
2. **部署效率**：不同环境可以使用不同配置
3. **用户体验**：减少因数据格式问题导致的使用困扰

### 量化指标
- **配置覆盖率**：90%以上的硬编码内容外部化
- **格式适应性**：支持至少3种不同的Excel格式
- **维护时间**：格式调整时间从小时级降低到分钟级

## 后续优化建议

1. **配置界面**：开发Web界面用于配置管理
2. **配置验证**：增强配置文件的语法和语义验证
3. **动态配置**：支持运行时配置更新
4. **配置模板**：提供常见场景的配置模板
