<template>
  <div class="price-alerts-container">
    <h1 class="view-title">价格预警管理</h1>
    <p class="view-description">在这里创建、查看和管理您的产品价格预警规则。</p>
    
    <BaseCard>
      <!-- 预警管理面板将放在这里 -->
      <PriceAlertPanel />
    </BaseCard>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import PriceAlertPanel from '@/components/priceMonitoring/PriceAlertPanel.vue'
import BaseCard from '@/components/common/BaseCard.vue'
import { api } from '@/utils/api'

// 后续将在这里添加获取和管理预警的逻辑
</script>

<style scoped>
.price-alerts-container {
  padding: 24px;
  background-color: #f4f6f8;
}

.view-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.view-description {
  font-size: 16px;
  color: #666;
  margin-bottom: 24px;
}
</style>