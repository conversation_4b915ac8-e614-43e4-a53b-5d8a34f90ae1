/**
 * 数据格式化工具函数
 */

// 格式化数字，添加千分位分隔符
export const formatNumber = (num, decimals = 1) => {
  if (num === null || num === undefined || isNaN(num)) return '--'
  
  const number = parseFloat(num)
  if (number === 0) return '0'
  
  // 处理大数值的单位转换
  if (Math.abs(number) >= 1000) {
    return (number / 1000).toFixed(decimals) + 'K'
  }
  
  return number.toLocaleString('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  })
}

// 格式化为整数
export const formatInteger = (num) => {
  if (num === null || num === undefined || isNaN(num)) return '--'
  return Math.round(parseFloat(num)).toLocaleString('zh-CN')
}

// 格式化货币
export const formatCurrency = (amount, currency = '¥', showUnit = true) => {
  if (amount === null || amount === undefined || isNaN(amount)) return '--'
  
  const number = parseFloat(amount)
  if (number === 0) return currency + '0'
  
  // 处理万元单位
  if (Math.abs(number) >= 10000) {
    const wanValue = (number / 10000).toFixed(1)
    return showUnit ? currency + wanValue + '万' : wanValue
  }
  
  const formatted = number.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
  
  return currency + formatted
}

// 格式化百分比
export const formatPercentage = (value, decimals = 1, showUnit = true) => {
  if (value === null || value === undefined || isNaN(value)) return '--'
  
  const number = parseFloat(value)
  const formatted = number.toFixed(decimals)
  return showUnit ? formatted + '%' : formatted
}

// 格式化日期
export const formatDate = (date, format = 'YYYY-MM-DD') => {
  if (!date) return '--'
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return '--'
  
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  
  switch (format) {
    case 'YYYY-MM-DD':
      return `${year}-${month}-${day}`
    case 'MM-DD':
      return `${month}-${day}`
    case 'YYYY年MM月DD日':
      return `${year}年${month}月${day}日`
    default:
      return `${year}-${month}-${day}`
  }
}

// 格式化时间范围
export const formatDateRange = (startDate, endDate) => {
  const start = formatDate(startDate, 'MM-DD')
  const end = formatDate(endDate, 'MM-DD')
  return `${start} ~ ${end}`
}

// 格式化产销率
export const formatRatio = (production, sales) => {
  if (!production || !sales || production === 0) return '--'
  
  const ratio = (sales / production) * 100
  return formatPercentage(ratio)
}

// 格式化变化率（环比）
export const formatChangeRate = (current, previous) => {
  if (current === null || previous === null || previous === 0) return '--'
  
  const rate = ((current - previous) / previous) * 100
  const formatted = formatPercentage(rate, 1, true)
  
  if (rate > 0) {
    return `+${formatted}`
  }
  
  return formatted
}

// 格式化库存单位（吨）
export const formatInventory = (value, unit = 'T', showUnit = true) => {
  if (value === null || value === undefined || isNaN(value)) return '--'
  
  const number = parseFloat(value)
  if (number === 0) return showUnit ? '0' + unit : '0'
  
  // 后端现在已统一返回吨单位的数据，无需转换
  const formatted = number.toLocaleString('zh-CN', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 1
  })
  return showUnit ? formatted + unit : formatted
}

// 格式化销量单位
export const formatSalesVolume = (value) => {
  if (value === null || value === undefined || isNaN(value)) return '--'
  
  const number = parseFloat(value)
  if (number === 0) return '0T'
  
  // 后端现在已统一返回吨单位的数据，无需转换
  const formatted = number.toLocaleString('zh-CN', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 1
  })
  
  return formatted + 'T'
}

// 格式化价格单位
export const formatPrice = (value, unit = '元/T', showUnit = true) => {
  if (value === null || value === undefined || isNaN(value)) return '--'
  
  const number = parseFloat(value)
  const formatted = number.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
  
  return showUnit ? formatted + unit : formatted
}

// 专门用于格式化金额的函数，智能处理万元单位
export const formatAmount = (amount, options = {}) => {
  const { 
    currency = '¥', 
    showCurrency = true, 
    showUnit = true, 
    decimals = 1 
  } = options
  
  if (amount === null || amount === undefined || isNaN(amount)) return '--'
  
  const number = parseFloat(amount)
  if (number === 0) return showCurrency ? currency + '0' : '0'
  
  // 处理万元单位
  if (Math.abs(number) >= 10000) {
    const wanValue = (number / 10000).toFixed(decimals)
    let result = wanValue
    if (showCurrency) result = currency + result
    if (showUnit) result += '万'
    return result
  }
  
  const formatted = number.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
  
  return showCurrency ? currency + formatted : formatted
}

// 获取变化趋势图标
export const getTrendIcon = (current, previous) => {
  if (!current || !previous) return '—'
  
  if (current > previous) return '↗'
  if (current < previous) return '↘'
  return '→'
}

// 获取变化趋势颜色类
export const getTrendColorClass = (current, previous) => {
  if (!current || !previous) return 'neutral'
  
  if (current > previous) return 'positive'
  if (current < previous) return 'negative'
  return 'neutral'
}

// 格式化大数字，自动选择合适的单位
export const formatLargeNumber = (num, decimals = 1) => {
  if (num === null || num === undefined || isNaN(num)) return '--'
  
  const number = parseFloat(num)
  if (number === 0) return '0'
  
  const abs = Math.abs(number)
  
  if (abs >= 1000000) {
    return (number / 1000000).toFixed(decimals) + 'M'
  } else if (abs >= 1000) {
    return (number / 1000).toFixed(decimals) + 'K'
  }
  
  return number.toLocaleString('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  })
}

// 格式化相对时间
export const formatRelativeTime = (dateTime) => {
  if (!dateTime) return '--'
  
  const date = new Date(dateTime)
  if (isNaN(date.getTime())) return '--'
  
  const now = new Date()
  const diffInSeconds = Math.floor((now - date) / 1000)
  
  if (diffInSeconds < 60) {
    return '刚刚'
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60)
    return `${minutes}分钟前`
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600)
    return `${hours}小时前`
  } else if (diffInSeconds < 2592000) { // 30天
    const days = Math.floor(diffInSeconds / 86400)
    return `${days}天前`
  } else {
    return formatDate(dateTime, 'MM-DD')
  }
}

// 格式化完整日期时间
export const formatDateTime = (dateTime, format = 'YYYY-MM-DD HH:mm:ss') => {
  if (!dateTime) return '--'
  
  const date = new Date(dateTime)
  if (isNaN(date.getTime())) return '--'
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  switch (format) {
    case 'YYYY-MM-DD HH:mm:ss':
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    case 'YYYY-MM-DD HH:mm':
      return `${year}-${month}-${day} ${hours}:${minutes}`
    case 'MM-DD HH:mm':
      return `${month}-${day} ${hours}:${minutes}`
    case 'HH:mm:ss':
      return `${hours}:${minutes}:${seconds}`
    default:
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }
}
