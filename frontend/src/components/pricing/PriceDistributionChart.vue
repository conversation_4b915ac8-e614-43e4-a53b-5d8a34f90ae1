<template>
  <BaseCard title="价格分布分析" :loading="loading" :error="error">
    <template #extra>
      <div class="chart-controls">
        <select v-model="chartType" @change="updateChartType" class="chart-type-selector">
          <option value="histogram">直方图</option>
          <option value="box">箱线图</option>
          <option value="scatter">散点图</option>
        </select>
        
        <select v-model="bucketCount" @change="updateBuckets" class="bucket-selector">
          <option value="5">5个区间</option>
          <option value="10">10个区间</option>
          <option value="15">15个区间</option>
        </select>
      </div>
    </template>

    <EChartsWrapper
      :options="chartOptions"
      :height="height"
      :loading="loading"
      @chart-ready="onChartReady"
      @chart-click="onChartClick"
    />

    <div class="distribution-summary">
      <div class="summary-item">
        <span class="label">价格区间：</span>
        <span class="value">{{ priceRange }}</span>
      </div>
      <div class="summary-item">
        <span class="label">标准差：</span>
        <span class="value">{{ formatPrice(standardDeviation) }}</span>
      </div>
      <div class="summary-item">
        <span class="label">变异系数：</span>
        <span class="value">{{ variationCoefficient }}</span>
      </div>
      <div class="summary-item">
        <span class="label">集中度：</span>
        <span class="value">{{ concentrationLevel }}</span>
      </div>
    </div>
  </BaseCard>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { usePricingStore } from '@/stores/pricing'
import { formatPrice } from '@/utils/formatters'
import { getBaseChartOption, getTooltipConfig, getXAxisConfig, getYAxisConfig, getBarSeriesConfig, getGridConfig } from '@/utils/charts'
import { CHART_COLORS } from '@/utils/constants'
import BaseCard from '@/components/common/BaseCard.vue'
import EChartsWrapper from '@/components/charts/EChartsWrapper.vue'

const props = defineProps({
  startDate: {
    type: String,
    default: '2025-06-01' // 备用默认值，实际值由父组件传入
  },
  endDate: {
    type: String,
    default: '2025-07-29' // 备用默认值，实际值由父组件传入
  },
  height: {
    type: String,
    default: '400px'
  }
})

const emit = defineEmits(['chart-ready', 'chart-click', 'data-updated'])

const pricingStore = usePricingStore()
const loading = ref(false)
const error = ref('')
const chartType = ref('histogram')
const bucketCount = ref(10)
const chartInstance = ref(null)

// 计算属性
const priceData = computed(() => {
  return pricingStore.priceData.map(item => item.price).filter(price => price > 0)
})

const priceRange = computed(() => {
  if (priceData.value.length === 0) return '--'
  
  const min = Math.min(...priceData.value)
  const max = Math.max(...priceData.value)
  
  return `${formatPrice(min)} - ${formatPrice(max)}`
})

const averagePrice = computed(() => {
  if (priceData.value.length === 0) return 0
  return priceData.value.reduce((sum, price) => sum + price, 0) / priceData.value.length
})

const standardDeviation = computed(() => {
  if (priceData.value.length === 0) return 0
  
  const avg = averagePrice.value
  const variance = priceData.value.reduce((sum, price) => {
    const diff = price - avg
    return sum + (diff * diff)
  }, 0) / priceData.value.length
  
  return Math.sqrt(variance)
})

const variationCoefficient = computed(() => {
  if (averagePrice.value === 0) return '--'
  return ((standardDeviation.value / averagePrice.value) * 100).toFixed(1) + '%'
})

const concentrationLevel = computed(() => {
  const cv = parseFloat(variationCoefficient.value)
  if (isNaN(cv)) return '--'
  
  if (cv < 15) return '高度集中'
  if (cv < 30) return '中度集中'
  return '分散'
})

// 价格分布数据
const distributionData = computed(() => {
  if (priceData.value.length === 0) return []
  
  const min = Math.min(...priceData.value)
  const max = Math.max(...priceData.value)
  const range = max - min
  const bucketSize = range / bucketCount.value
  
  const buckets = []
  for (let i = 0; i < bucketCount.value; i++) {
    const bucketMin = min + i * bucketSize
    const bucketMax = min + (i + 1) * bucketSize
    const count = priceData.value.filter(price => 
      price >= bucketMin && (i === bucketCount.value - 1 ? price <= bucketMax : price < bucketMax)
    ).length
    
    buckets.push({
      range: `${bucketMin.toFixed(0)}-${bucketMax.toFixed(0)}`,
      min: bucketMin,
      max: bucketMax,
      count: count,
      percentage: (count / priceData.value.length) * 100
    })
  }
  
  return buckets
})

// 图表配置
const chartOptions = computed(() => {
  if (priceData.value.length === 0) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#999',
          fontSize: 14
        }
      }
    }
  }

  const option = {
    ...getBaseChartOption(),
    title: {
      text: '价格分布分析',
      left: 'center',
      textStyle: {
        color: CHART_COLORS.PRIMARY,
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    grid: getGridConfig()
  }

  switch (chartType.value) {
    case 'histogram':
      option.tooltip = getTooltipConfig((params) => {
        const data = distributionData.value[params.dataIndex]
        return `价格区间: ${formatPrice(data.min)} - ${formatPrice(data.max)}<br/>
                数量: ${data.count}<br/>
                占比: ${data.percentage.toFixed(1)}%`
      })
      option.xAxis = getXAxisConfig(distributionData.value.map(item => item.range), { rotate: 45 })
      option.yAxis = getYAxisConfig({
        name: '数量',
        formatter: (value) => value.toString()
      })
      option.series = [
        getBarSeriesConfig('价格分布', distributionData.value.map(item => item.count), {
          color: CHART_COLORS.PRIMARY,
          showLabel: true,
          labelFormatter: (params) => params.value.toString()
        })
      ]
      break

    case 'box':
      // 箱线图配置
      const sortedPrices = [...priceData.value].sort((a, b) => a - b)
      const q1 = sortedPrices[Math.floor(sortedPrices.length * 0.25)]
      const median = sortedPrices[Math.floor(sortedPrices.length * 0.5)]
      const q3 = sortedPrices[Math.floor(sortedPrices.length * 0.75)]
      const min = sortedPrices[0]
      const max = sortedPrices[sortedPrices.length - 1]

      option.tooltip = {
        formatter: (params) => {
          return `最小值: ${formatPrice(min)}<br/>
                  Q1: ${formatPrice(q1)}<br/>
                  中位数: ${formatPrice(median)}<br/>
                  Q3: ${formatPrice(q3)}<br/>
                  最大值: ${formatPrice(max)}`
        }
      }
      option.xAxis = { type: 'category', data: ['价格分布'] }
      option.yAxis = getYAxisConfig({
        name: '价格(元/T)',
        formatter: (value) => formatPrice(value)
      })
      option.series = [{
        type: 'boxplot',
        data: [[min, q1, median, q3, max]],
        itemStyle: {
          color: CHART_COLORS.PRIMARY
        }
      }]
      break

    case 'scatter':
      // 散点图配置
      const scatterData = pricingStore.priceData.map((item, index) => [index, item.price])
      
      option.tooltip = getTooltipConfig((params) => {
        const index = params.value[0]
        const price = params.value[1]
        const date = pricingStore.priceData[index]?.date || ''
        return `日期: ${date}<br/>价格: ${formatPrice(price)}`
      })
      option.xAxis = getXAxisConfig([], { type: 'value', name: '时间序列' })
      option.yAxis = getYAxisConfig({
        name: '价格(元/T)',
        formatter: (value) => formatPrice(value)
      })
      option.series = [{
        type: 'scatter',
        data: scatterData,
        itemStyle: {
          color: CHART_COLORS.PRIMARY
        },
        symbolSize: 6
      }]
      break
  }

  return option
})

// 方法
const fetchData = async () => {
  loading.value = true
  error.value = ''
  
  try {
    await pricingStore.fetchPriceData(props.startDate, props.endDate)
    emit('data-updated', pricingStore.priceData)
  } catch (err) {
    error.value = '数据加载失败'
    console.error('Failed to load price distribution data:', err)
  } finally {
    loading.value = false
  }
}

const updateChartType = () => {
  console.log('Chart type updated to:', chartType.value)
}

const updateBuckets = () => {
  console.log('Bucket count updated to:', bucketCount.value)
}

const onChartReady = (chart) => {
  chartInstance.value = chart
  emit('chart-ready', chart)
}

const onChartClick = (params) => {
  emit('chart-click', params)
}

// 监听props变化
watch([() => props.startDate, () => props.endDate], () => {
  fetchData()
})

onMounted(() => {
  fetchData()
})

// 暴露方法
defineExpose({
  refresh: fetchData,
  getChart: () => chartInstance.value
})
</script>

<style scoped>
.chart-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.chart-type-selector,
.bucket-selector {
  padding: 6px 12px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.9em;
  background: white;
  cursor: pointer;
}

.chart-type-selector:focus,
.bucket-selector:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.distribution-summary {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  padding: 16px;
  background: var(--background-light);
  border-radius: 8px;
  border-top: 1px solid #f0f0f0;
}

.summary-item {
  text-align: center;
  flex: 1;
}

.label {
  display: block;
  color: var(--text-secondary);
  font-size: 0.85em;
  margin-bottom: 4px;
}

.value {
  display: block;
  color: var(--primary-blue);
  font-weight: 600;
  font-size: 1em;
}

@media (max-width: 768px) {
  .chart-controls {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .distribution-summary {
    flex-direction: column;
    gap: 12px;
  }
  
  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .label, .value {
    display: inline;
    margin: 0;
  }
}
</style>
