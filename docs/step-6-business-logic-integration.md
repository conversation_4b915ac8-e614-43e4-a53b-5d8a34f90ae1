# 步骤六：整合重复的业务逻辑

## 目标和背景

### 目标
重构 `ProductionRatioCalculator` 类及其调用方，将分散在多个API端点中的产销比计算逻辑整合到统一的业务服务中，消除代码重复和逻辑不一致。

### 背景
当前系统中多个API端点（如 `/api/trends/ratio`, `/api/production/ratio-stats`）都包含获取和计算产销比的逻辑，这种重复实现带来了以下问题：

1. **代码重复**：相同的计算逻辑在多处重复实现
2. **逻辑不一致**：不同端点的计算方法可能存在差异
3. **维护困难**：修改计算逻辑需要同时更新多个地方
4. **测试复杂**：需要为每个端点单独测试相同的业务逻辑
5. **数据不一致**：可能导致前端显示的数据不一致

### 业务逻辑分析
产销比相关的业务逻辑包括：
- **数据获取**：从数据库获取生产和销售数据
- **数据过滤**：应用产品过滤规则
- **比率计算**：计算产销比（销售量/生产量 * 100%）
- **趋势分析**：计算环比变化和趋势
- **统计汇总**：计算平均值、最大值、最小值等

## 具体实施步骤

### 第一步：分析现有重复逻辑
1. **识别重复的业务逻辑**
   ```bash
   # 查找产销比相关的代码
   grep -r "ProductionRatioCalculator\|production.*ratio\|sales.*ratio" backend/src/
   
   # 查找重复的计算逻辑
   grep -r "sales_volume.*production_volume\|production_volume.*sales_volume" backend/src/
   ```

2. **分析API端点的重复度**
   ```bash
   # 分析不同端点的相似代码
   diff <(grep -A 20 "/api/trends/ratio" backend/src/index.ts) \
        <(grep -A 20 "/api/production/ratio-stats" backend/src/index.ts)
   ```

3. **评估业务逻辑的复杂度**
   - 计算逻辑的复杂程度
   - 数据依赖关系
   - 性能要求

### 第二步：设计统一的业务服务架构
1. **服务层设计**
   - 创建专门的业务服务类
   - 定义清晰的服务接口
   - 建立数据传输对象(DTO)

2. **依赖注入设计**
   - 服务之间的依赖关系
   - 数据访问层的抽象
   - 配置和参数的注入

### 第三步：实现统一的业务服务
1. **创建ProductionService**
2. **重构现有的Calculator类**
3. **建立服务注册机制**

### 第四步：更新API路由
1. **重构相关的API端点**
2. **保持API接口兼容性**
3. **添加统一的错误处理**

## 代码示例

### 修改前的重复代码问题
```typescript
// 问题代码 - 多处重复的业务逻辑

// 在 /api/trends/ratio 端点中
app.get('/api/trends/ratio', async (c) => {
  // 重复逻辑1：数据获取和过滤
  const db = drizzle(c.env.DB);
  const data = await db.select().from(dailyMetrics)
    .where(and(
      gte(dailyMetrics.date, startDate),
      lte(dailyMetrics.date, endDate),
      // 硬编码的过滤条件
      sql`product_name NOT LIKE '%鲜%' OR product_name LIKE '%凤肠%'`
    ));
  
  // 重复逻辑2：产销比计算
  const ratioData = data.map(item => ({
    date: item.date,
    ratio: item.production_volume > 0 
      ? (item.sales_volume / item.production_volume) * 100 
      : 0
  }));
  
  return c.json(ratioData);
});

// 在 /api/production/ratio-stats 端点中
app.get('/api/production/ratio-stats', async (c) => {
  // 重复逻辑1：相同的数据获取（略有不同）
  const db = drizzle(c.env.DB);
  const data = await db.select().from(dailyMetrics)
    .where(and(
      eq(dailyMetrics.date, targetDate),
      // 可能不一致的过滤条件
      sql`product_name NOT LIKE '%副产品%'`
    ));
  
  // 重复逻辑2：相似但不完全相同的计算
  const totalSales = data.reduce((sum, item) => sum + item.sales_volume, 0);
  const totalProduction = data.reduce((sum, item) => sum + item.production_volume, 0);
  const avgRatio = totalProduction > 0 ? (totalSales / totalProduction) * 100 : 0;
  
  return c.json({ avgRatio, totalSales, totalProduction });
});

// 在其他地方还有类似的逻辑...
```

### 修改后的统一业务服务

#### 1. 业务服务接口定义
```typescript
// backend/src/types/business.ts
export interface ProductionRatioData {
  date: string;
  productName: string;
  productionVolume: number;
  salesVolume: number;
  ratio: number;
  ratioCategory: 'high' | 'normal' | 'low';
}

export interface ProductionRatioSummary {
  totalProduction: number;
  totalSales: number;
  averageRatio: number;
  productCount: number;
  highRatioCount: number;
  lowRatioCount: number;
  trendDirection: 'up' | 'down' | 'stable';
  periodComparison: number;
}

export interface ProductionRatioTrend {
  date: string;
  ratio: number;
  production: number;
  sales: number;
  change: number;
}

export interface ProductionServiceOptions {
  dateRange?: { start: string; end: string };
  productFilter?: string;
  excludeFresh?: boolean;
  excludeByproducts?: boolean;
  groupBy?: 'day' | 'week' | 'month';
}
```

#### 2. 统一的业务服务实现
```typescript
// backend/src/services/productionService.ts
import { drizzle } from 'drizzle-orm/d1';
import { eq, gte, lte, and, sum, avg, count, desc } from 'drizzle-orm';
import { dailyMetrics } from '../schema';
import { ProductFilter } from '../utils/filters';
import { 
  ProductionRatioData, 
  ProductionRatioSummary, 
  ProductionRatioTrend,
  ProductionServiceOptions 
} from '../types/business';

export class ProductionService {
  private db: any;
  
  constructor(database: D1Database) {
    this.db = drizzle(database);
  }
  
  /**
   * 获取产销比数据
   */
  async getProductionRatioData(options: ProductionServiceOptions): Promise<ProductionRatioData[]> {
    const query = this.buildBaseQuery(options);
    const rawData = await query.execute();
    
    return rawData.map(item => this.calculateRatioData(item));
  }
  
  /**
   * 获取产销比汇总统计
   */
  async getProductionRatioSummary(options: ProductionServiceOptions): Promise<ProductionRatioSummary> {
    const ratioData = await this.getProductionRatioData(options);
    
    const totalProduction = ratioData.reduce((sum, item) => sum + item.productionVolume, 0);
    const totalSales = ratioData.reduce((sum, item) => sum + item.salesVolume, 0);
    const averageRatio = totalProduction > 0 ? (totalSales / totalProduction) * 100 : 0;
    
    const highRatioCount = ratioData.filter(item => item.ratioCategory === 'high').length;
    const lowRatioCount = ratioData.filter(item => item.ratioCategory === 'low').length;
    
    // 计算趋势方向
    const trendDirection = await this.calculateTrendDirection(options);
    
    // 计算环比变化
    const periodComparison = await this.calculatePeriodComparison(options);
    
    return {
      totalProduction,
      totalSales,
      averageRatio,
      productCount: ratioData.length,
      highRatioCount,
      lowRatioCount,
      trendDirection,
      periodComparison
    };
  }
  
  /**
   * 获取产销比趋势数据
   */
  async getProductionRatioTrends(options: ProductionServiceOptions): Promise<ProductionRatioTrend[]> {
    const { dateRange, groupBy = 'day' } = options;
    
    if (!dateRange) {
      throw new Error('获取趋势数据需要指定日期范围');
    }
    
    // 根据分组方式构建不同的查询
    const query = this.buildTrendQuery(options);
    const trendData = await query.execute();
    
    // 计算变化率
    return this.calculateTrendChanges(trendData);
  }
  
  /**
   * 获取产品级别的产销比分析
   */
  async getProductLevelRatioAnalysis(options: ProductionServiceOptions & { 
    limit?: number;
    sortBy?: 'ratio' | 'production' | 'sales';
  }): Promise<ProductionRatioData[]> {
    const { limit = 50, sortBy = 'ratio' } = options;
    
    let query = this.buildBaseQuery(options);
    
    // 按产品分组并计算汇总
    query = this.db
      .select({
        product_name: dailyMetrics.product_name,
        totalProduction: sum(dailyMetrics.production_volume),
        totalSales: sum(dailyMetrics.sales_volume),
        avgProduction: avg(dailyMetrics.production_volume),
        avgSales: avg(dailyMetrics.sales_volume),
        dataPoints: count(dailyMetrics.date)
      })
      .from(dailyMetrics)
      .where(this.buildWhereConditions(options))
      .groupBy(dailyMetrics.product_name)
      .limit(limit);
    
    // 根据排序字段排序
    const sortField = {
      'ratio': 'totalSales / totalProduction',
      'production': 'totalProduction',
      'sales': 'totalSales'
    }[sortBy];
    
    if (sortField) {
      query = query.orderBy(desc(sortField));
    }
    
    const results = await query.execute();
    
    return results.map(item => ({
      date: options.dateRange?.end || new Date().toISOString().split('T')[0],
      productName: item.product_name,
      productionVolume: item.totalProduction || 0,
      salesVolume: item.totalSales || 0,
      ratio: item.totalProduction > 0 ? (item.totalSales / item.totalProduction) * 100 : 0,
      ratioCategory: this.categorizeRatio(
        item.totalProduction > 0 ? (item.totalSales / item.totalProduction) * 100 : 0
      )
    }));
  }
  
  /**
   * 构建基础查询
   */
  private buildBaseQuery(options: ProductionServiceOptions) {
    let query = this.db
      .select({
        date: dailyMetrics.date,
        product_name: dailyMetrics.product_name,
        production_volume: dailyMetrics.production_volume,
        sales_volume: dailyMetrics.sales_volume
      })
      .from(dailyMetrics);
    
    const whereConditions = this.buildWhereConditions(options);
    if (whereConditions) {
      query = query.where(whereConditions);
    }
    
    return query.orderBy(dailyMetrics.date, dailyMetrics.product_name);
  }
  
  /**
   * 构建WHERE条件
   */
  private buildWhereConditions(options: ProductionServiceOptions) {
    const conditions = [];
    
    // 日期范围条件
    if (options.dateRange) {
      conditions.push(
        and(
          gte(dailyMetrics.date, options.dateRange.start),
          lte(dailyMetrics.date, options.dateRange.end)
        )
      );
    }
    
    // 产品过滤条件
    if (options.excludeFresh) {
      conditions.push(ProductFilter.getInventoryFilter());
    }
    
    if (options.excludeByproducts) {
      conditions.push(ProductFilter.getProductionFilter());
    }
    
    // 产品名称过滤
    if (options.productFilter) {
      conditions.push(eq(dailyMetrics.product_name, options.productFilter));
    }
    
    return conditions.length > 0 ? and(...conditions) : undefined;
  }
  
  /**
   * 构建趋势查询
   */
  private buildTrendQuery(options: ProductionServiceOptions) {
    const { dateRange, groupBy } = options;
    
    let query = this.db
      .select({
        date: dailyMetrics.date,
        totalProduction: sum(dailyMetrics.production_volume),
        totalSales: sum(dailyMetrics.sales_volume)
      })
      .from(dailyMetrics)
      .where(this.buildWhereConditions(options))
      .groupBy(dailyMetrics.date)
      .orderBy(dailyMetrics.date);
    
    return query;
  }
  
  /**
   * 计算单条记录的产销比数据
   */
  private calculateRatioData(item: any): ProductionRatioData {
    const ratio = item.production_volume > 0 
      ? (item.sales_volume / item.production_volume) * 100 
      : 0;
    
    return {
      date: item.date,
      productName: item.product_name,
      productionVolume: item.production_volume || 0,
      salesVolume: item.sales_volume || 0,
      ratio: Math.round(ratio * 100) / 100, // 保留两位小数
      ratioCategory: this.categorizeRatio(ratio)
    };
  }
  
  /**
   * 对产销比进行分类
   */
  private categorizeRatio(ratio: number): 'high' | 'normal' | 'low' {
    if (ratio >= 120) return 'high';
    if (ratio >= 80) return 'normal';
    return 'low';
  }
  
  /**
   * 计算趋势方向
   */
  private async calculateTrendDirection(options: ProductionServiceOptions): Promise<'up' | 'down' | 'stable'> {
    // 获取最近的趋势数据
    const recentTrends = await this.getProductionRatioTrends({
      ...options,
      dateRange: {
        start: this.getDateBefore(7), // 最近7天
        end: new Date().toISOString().split('T')[0]
      }
    });
    
    if (recentTrends.length < 2) return 'stable';
    
    const firstRatio = recentTrends[0].ratio;
    const lastRatio = recentTrends[recentTrends.length - 1].ratio;
    const change = lastRatio - firstRatio;
    
    if (change > 5) return 'up';
    if (change < -5) return 'down';
    return 'stable';
  }
  
  /**
   * 计算环比变化
   */
  private async calculatePeriodComparison(options: ProductionServiceOptions): Promise<number> {
    // 实现环比计算逻辑
    // 这里简化处理，实际应该根据具体需求实现
    return 0;
  }
  
  /**
   * 计算趋势变化
   */
  private calculateTrendChanges(trendData: any[]): ProductionRatioTrend[] {
    return trendData.map((item, index) => {
      const ratio = item.totalProduction > 0 
        ? (item.totalSales / item.totalProduction) * 100 
        : 0;
      
      const change = index > 0 
        ? ratio - (trendData[index - 1].totalProduction > 0 
            ? (trendData[index - 1].totalSales / trendData[index - 1].totalProduction) * 100 
            : 0)
        : 0;
      
      return {
        date: item.date,
        ratio: Math.round(ratio * 100) / 100,
        production: item.totalProduction || 0,
        sales: item.totalSales || 0,
        change: Math.round(change * 100) / 100
      };
    });
  }
  
  /**
   * 获取指定天数前的日期
   */
  private getDateBefore(days: number): string {
    const date = new Date();
    date.setDate(date.getDate() - days);
    return date.toISOString().split('T')[0];
  }
}
```

#### 3. 重构后的API路由
```typescript
// backend/src/routes/production.ts - 重构版本
import { Hono } from 'hono';
import { ProductionService } from '../services/productionService';
import { validateQuery } from '../middleware/validation';

const production = new Hono();

// 依赖注入
production.use('*', async (c, next) => {
  c.set('productionService', new ProductionService(c.env.DB));
  await next();
});

/**
 * 获取产销比趋势数据
 * GET /api/production/ratio-trends
 */
production.get('/ratio-trends', 
  validateQuery(['start_date', 'end_date']),
  async (c) => {
    try {
      const productionService = c.get('productionService') as ProductionService;
      const { start_date, end_date, group_by, product_filter } = c.req.query();
      
      const trends = await productionService.getProductionRatioTrends({
        dateRange: { start: start_date, end: end_date },
        groupBy: group_by as 'day' | 'week' | 'month' || 'day',
        productFilter: product_filter,
        excludeFresh: true,
        excludeByproducts: true
      });
      
      return c.json({
        success: true,
        data: trends,
        message: '产销比趋势获取成功'
      });
      
    } catch (error) {
      console.error('产销比趋势获取失败:', error);
      return c.json({
        success: false,
        error: '产销比趋势获取失败'
      }, 500);
    }
  }
);

/**
 * 获取产销比统计汇总
 * GET /api/production/ratio-stats
 */
production.get('/ratio-stats', async (c) => {
  try {
    const productionService = c.get('productionService') as ProductionService;
    const { date, period } = c.req.query();
    
    const endDate = date || new Date().toISOString().split('T')[0];
    const startDate = period ? getDateBefore(parseInt(period)) : getDateBefore(30);
    
    const summary = await productionService.getProductionRatioSummary({
      dateRange: { start: startDate, end: endDate },
      excludeFresh: true,
      excludeByproducts: true
    });
    
    return c.json({
      success: true,
      data: summary,
      message: '产销比统计获取成功'
    });
    
  } catch (error) {
    console.error('产销比统计获取失败:', error);
    return c.json({
      success: false,
      error: '产销比统计获取失败'
    }, 500);
  }
});

/**
 * 获取产品级别的产销比分析
 * GET /api/production/product-ratio-analysis
 */
production.get('/product-ratio-analysis', async (c) => {
  try {
    const productionService = c.get('productionService') as ProductionService;
    const { date, limit, sort_by } = c.req.query();
    
    const analysis = await productionService.getProductLevelRatioAnalysis({
      dateRange: {
        start: date || new Date().toISOString().split('T')[0],
        end: date || new Date().toISOString().split('T')[0]
      },
      limit: limit ? parseInt(limit) : 15,
      sortBy: sort_by as 'ratio' | 'production' | 'sales' || 'ratio',
      excludeFresh: true,
      excludeByproducts: true
    });
    
    return c.json({
      success: true,
      data: analysis,
      message: '产品产销比分析获取成功'
    });
    
  } catch (error) {
    console.error('产品产销比分析获取失败:', error);
    return c.json({
      success: false,
      error: '产品产销比分析获取失败'
    }, 500);
  }
});

function getDateBefore(days: number): string {
  const date = new Date();
  date.setDate(date.getDate() - days);
  return date.toISOString().split('T')[0];
}

export default production;
```

#### 4. 服务注册和依赖注入
```typescript
// backend/src/services/serviceContainer.ts
export class ServiceContainer {
  private services = new Map<string, any>();
  
  register<T>(name: string, factory: () => T): void {
    this.services.set(name, factory);
  }
  
  get<T>(name: string): T {
    const factory = this.services.get(name);
    if (!factory) {
      throw new Error(`Service ${name} not found`);
    }
    return factory();
  }
}

// 全局服务容器
export const serviceContainer = new ServiceContainer();

// 注册服务
serviceContainer.register('productionService', () => new ProductionService(/* DB */));
```

## 验证方法

### 业务逻辑一致性测试
```typescript
// tests/services/productionService.test.ts
describe('ProductionService', () => {
  test('should calculate consistent ratios across different methods', async () => {
    const service = new ProductionService(mockDB);
    
    // 测试不同方法计算的产销比是否一致
    const ratioData = await service.getProductionRatioData(testOptions);
    const summary = await service.getProductionRatioSummary(testOptions);
    
    const manualAvgRatio = ratioData.reduce((sum, item) => sum + item.ratio, 0) / ratioData.length;
    
    expect(Math.abs(summary.averageRatio - manualAvgRatio)).toBeLessThan(0.01);
  });
  
  test('should handle edge cases correctly', async () => {
    const service = new ProductionService(mockDB);
    
    // 测试生产量为0的情况
    const zeroProductionData = await service.getProductionRatioData({
      productFilter: 'zero-production-product'
    });
    
    expect(zeroProductionData[0].ratio).toBe(0);
    expect(zeroProductionData[0].ratioCategory).toBe('low');
  });
});
```

### API一致性测试
```bash
# 测试不同API端点返回数据的一致性
curl "http://localhost:8787/api/production/ratio-trends?start_date=2025-01-01&end_date=2025-01-07" > trends.json
curl "http://localhost:8787/api/production/ratio-stats?period=7" > stats.json

# 验证数据一致性
node scripts/verify-data-consistency.js trends.json stats.json
```

### 性能测试
```bash
# 对比重构前后的性能
npm run benchmark:production-ratio
```

## 注意事项

### 业务逻辑一致性
1. **计算公式统一**：确保所有地方使用相同的计算公式
2. **数据过滤一致**：统一的产品过滤规则
3. **边界条件处理**：统一处理除零、空值等边界情况

### 服务设计原则
1. **单一职责**：每个服务只负责特定的业务领域
2. **依赖注入**：通过构造函数注入依赖
3. **接口抽象**：定义清晰的服务接口

### 性能优化
1. **缓存策略**：为频繁计算的结果添加缓存
2. **批量处理**：优化数据库查询，减少往返次数
3. **懒加载**：只在需要时计算复杂的统计数据

## 预期结果

### 代码质量提升
1. **重复代码消除**：产销比相关代码重复度降低90%以上
2. **逻辑一致性**：所有API端点使用统一的计算逻辑
3. **可维护性增强**：修改业务逻辑只需更新一个地方

### 系统稳定性改善
1. **数据一致性**：前端显示的数据完全一致
2. **错误处理统一**：统一的异常处理和错误响应
3. **测试覆盖完整**：业务逻辑有完整的单元测试覆盖

### 量化指标
- **代码重复度**：从40%降低到5%以下
- **API响应一致性**：达到100%
- **单元测试覆盖率**：业务逻辑达到95%以上
- **维护时间**：业务逻辑修改时间减少70%

## 后续优化建议

1. **缓存层集成**：为计算结果添加Redis缓存
2. **实时计算**：考虑实时数据流处理
3. **业务规则引擎**：将复杂的业务规则外部化
4. **监控和告警**：添加业务指标监控和异常告警
