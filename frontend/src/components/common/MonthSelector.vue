<template>
  <div class="month-selector">
    <div class="selector-group">
      <select v-model="selectedYear" @change="updateRange" class="year-select">
        <option v-for="year in availableYears" :key="year" :value="year">
          {{ year }}年
        </option>
      </select>
      <select v-model="selectedMonth" @change="updateRange" class="month-select">
        <option v-for="month in availableMonths" :key="month.value" :value="month.value">
          {{ month.label }}
        </option>
      </select>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { getMonthRange } from '@/utils/date'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      start: '',
      end: ''
    })
  },
  minYear: {
    type: Number,
    default: 2023
  },
  maxYear: {
    type: Number,
    default: new Date().getFullYear()
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const selectedYear = ref(new Date().getFullYear())
const selectedMonth = ref(new Date().getMonth() + 1)

const availableYears = computed(() => {
  const years = []
  for (let year = props.maxYear; year >= props.minYear; year--) {
    years.push(year)
  }
  return years
})

const availableMonths = computed(() => [
  { value: 1, label: '1月' },
  { value: 2, label: '2月' },
  { value: 3, label: '3月' },
  { value: 4, label: '4月' },
  { value: 5, label: '5月' },
  { value: 6, label: '6月' },
  { value: 7, label: '7月' },
  { value: 8, label: '8月' },
  { value: 9, label: '9月' },
  { value: 10, label: '10月' },
  { value: 11, label: '11月' },
  { value: 12, label: '12月' }
])

const updateRange = () => {
  const range = getMonthRange(selectedYear.value, selectedMonth.value)
  emit('update:modelValue', range)
  emit('change', range)
}

// Initialize with current month if no modelValue provided
onMounted(() => {
  if (!props.modelValue.start || !props.modelValue.end) {
    updateRange()
  }
})

// Expose methods for parent components
defineExpose({
  setMonth: (year, month) => {
    selectedYear.value = year
    selectedMonth.value = month
    updateRange()
  },
  getCurrentSelection: () => ({
    year: selectedYear.value,
    month: selectedMonth.value
  })
})
</script>

<style scoped>
.month-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.selector-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.year-select,
.month-select {
  padding: 6px 12px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.9em;
  background: white;
  cursor: pointer;
  min-width: 80px;
}

.year-select:focus,
.month-select:focus {
  outline: none;
  border-color: var(--primary-blue);
}

@media (max-width: 768px) {
  .selector-group {
    flex-direction: column;
    width: 100%;
  }
  
  .year-select,
  .month-select {
    width: 100%;
    min-width: auto;
  }
}
</style>