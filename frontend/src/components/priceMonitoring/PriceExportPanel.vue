<template>
  <div class="price-export-panel">
    <div class="panel-header">
      <h3>数据导出</h3>
      <button @click="showExportDialog = true" class="export-btn">
        <span class="icon">📥</span>
        导出数据
      </button>
    </div>

    <!-- 导出对话框 -->
    <div v-if="showExportDialog" class="export-dialog-overlay" @click.self="closeDialog">
      <div class="export-dialog">
        <div class="dialog-header">
          <h4>导出价格数据</h4>
          <button @click="closeDialog" class="close-btn">✕</button>
        </div>

        <div class="dialog-body">
          <!-- 产品选择 -->
          <div class="form-group">
            <label>选择产品</label>
            <div class="product-selection">
              <div class="selection-header">
                <label>
                  <input 
                    type="checkbox" 
                    v-model="selectAll"
                    @change="toggleSelectAll"
                    :indeterminate="selectedProducts.length > 0 && selectedProducts.length < availableProducts.length"
                  >
                  全选 ({{ selectedProducts.length }}/{{ availableProducts.length }})
                </label>
                <input 
                  type="text" 
                  v-model="productFilter"
                  placeholder="搜索产品..."
                  class="filter-input"
                >
              </div>
              <div class="product-list">
                <label 
                  v-for="product in filteredProducts" 
                  :key="product.product_name"
                  class="product-item"
                >
                  <input 
                    type="checkbox" 
                    :value="product.product_name"
                    v-model="selectedProducts"
                  >
                  <span>{{ product.product_name }}</span>
                  <span class="product-info">{{ product.adjustment_days }}天</span>
                </label>
              </div>
            </div>
          </div>

          <!-- 日期范围 -->
          <div class="form-group">
            <label>日期范围</label>
            <div class="date-range">
              <input 
                type="date" 
                v-model="exportParams.startDate"
                :max="exportParams.endDate || today"
                class="date-input"
              >
              <span>至</span>
              <input 
                type="date" 
                v-model="exportParams.endDate"
                :min="exportParams.startDate"
                :max="today"
                class="date-input"
              >
            </div>
          </div>

          <!-- 导出格式 -->
          <div class="form-group">
            <label>导出格式</label>
            <div class="format-options">
              <div 
                v-for="format in exportFormats" 
                :key="format.key"
                class="format-option"
                :class="{ active: exportParams.format === format.key }"
                @click="exportParams.format = format.key"
              >
                <div class="format-icon">{{ format.icon }}</div>
                <div class="format-info">
                  <div class="format-name">{{ format.name }}</div>
                  <div class="format-desc">{{ format.description }}</div>
                </div>
                <div class="format-features">
                  <span v-for="feature in format.features" :key="feature" class="feature-tag">
                    {{ feature }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 附加选项 -->
          <div class="form-group">
            <label>附加选项</label>
            <div class="extra-options">
              <label>
                <input type="checkbox" v-model="exportParams.includeStats">
                包含统计摘要
              </label>
            </div>
          </div>

          <!-- 错误提示 -->
          <div v-if="validationErrors.length > 0" class="error-messages">
            <p v-for="error in validationErrors" :key="error" class="error-item">
              ⚠️ {{ error }}
            </p>
          </div>
        </div>

        <div class="dialog-footer">
          <button @click="closeDialog" class="btn-cancel">取消</button>
          <button @click="previewData" class="btn-preview" :disabled="!canExport">
            预览数据
          </button>
          <button @click="startExport" class="btn-export" :disabled="!canExport || exporting">
            <span v-if="!exporting">导出</span>
            <span v-else>导出中...</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 预览对话框 -->
    <div v-if="showPreviewDialog" class="preview-dialog-overlay" @click.self="closePreview">
      <div class="preview-dialog">
        <div class="dialog-header">
          <h4>数据预览</h4>
          <button @click="closePreview" class="close-btn">✕</button>
        </div>
        <div class="preview-content">
          <div v-if="previewLoading" class="preview-loading">
            <div class="loader"></div>
            <p>加载预览数据...</p>
          </div>
          <div v-else-if="previewData" class="preview-table-wrapper">
            <table class="preview-table">
              <thead>
                <tr>
                  <th>产品名称</th>
                  <th>规格</th>
                  <th>调价日期</th>
                  <th>当前价格</th>
                  <th>价格差异</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in previewData.data.slice(0, 10)" :key="index">
                  <td>{{ item.product_name }}</td>
                  <td>{{ item.specification || '-' }}</td>
                  <td>{{ item.adjustment_date }}</td>
                  <td>{{ item.current_price.toFixed(2) }}</td>
                  <td :class="{ 'price-up': item.price_difference > 0, 'price-down': item.price_difference < 0 }">
                    {{ item.price_difference > 0 ? '+' : '' }}{{ item.price_difference.toFixed(2) }}
                  </td>
                </tr>
              </tbody>
            </table>
            <p class="preview-note">仅显示前10条记录，完整数据将在导出文件中</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 导出进度 -->
    <div v-if="exportProgress.visible" class="export-progress">
      <div class="progress-header">
        <span>{{ exportProgress.status }}</span>
        <button @click="cancelExport" class="cancel-btn" v-if="exportProgress.cancellable">
          取消
        </button>
      </div>
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: exportProgress.progress + '%' }"></div>
      </div>
      <div v-if="exportProgress.error" class="progress-error">
        {{ exportProgress.error }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { getKeyProducts } from '@/utils/priceDataManager';
import exportManager from '@/utils/priceExportManager';

const emit = defineEmits(['export-completed']);

// 状态
const showExportDialog = ref(false);
const showPreviewDialog = ref(false);
const exporting = ref(false);
const previewLoading = ref(false);
const previewData = ref(null);

// 产品相关
const availableProducts = ref([]);
const selectedProducts = ref([]);
const productFilter = ref('');
const selectAll = ref(false);

// 导出参数
const exportParams = ref({
  format: 'csv',
  startDate: '',
  endDate: '',
  includeStats: false
});

// 导出格式配置
const exportFormats = ref([
  {
    key: 'csv',
    name: 'CSV',
    icon: '📊',
    description: 'CSV格式，可用Excel打开',
    features: ['兼容Excel', '文件较小']
  },
  {
    key: 'json',
    name: 'JSON',
    icon: '📄',
    description: 'JSON格式，适合程序处理',
    features: ['结构化数据', '包含元数据']
  },
  {
    key: 'xlsx',
    name: 'Excel',
    icon: '📑',
    description: 'Excel格式，功能丰富',
    features: ['多工作表', '格式化']
  }
]);

// 导出进度
const exportProgress = ref({
  visible: false,
  status: '',
  progress: 0,
  error: null,
  cancellable: true
});

// 验证错误
const validationErrors = ref([]);

// 计算属性
const today = computed(() => new Date().toISOString().split('T')[0]);

const filteredProducts = computed(() => {
  if (!productFilter.value) return availableProducts.value;
  
  const filter = productFilter.value.toLowerCase();
  return availableProducts.value.filter(p => 
    p.product_name.toLowerCase().includes(filter)
  );
});

const canExport = computed(() => {
  return selectedProducts.value.length > 0 && validationErrors.value.length === 0;
});

// 方法
async function loadProducts() {
  try {
    const response = await getKeyProducts({ days: 90, limit: 50 });
    if (response.success && response.data) {
      availableProducts.value = response.data;
    }
  } catch (error) {
    console.error('Failed to load products:', error);
  }
}

function toggleSelectAll() {
  if (selectAll.value) {
    selectedProducts.value = availableProducts.value.map(p => p.product_name);
  } else {
    selectedProducts.value = [];
  }
}

function validateParams() {
  const params = {
    products: selectedProducts.value,
    format: exportParams.value.format,
    startDate: exportParams.value.startDate,
    endDate: exportParams.value.endDate
  };
  
  const validation = exportManager.validateExportParams(params);
  validationErrors.value = validation.errors;
  return validation.isValid;
}

async function previewData() {
  if (!validateParams()) return;
  
  showPreviewDialog.value = true;
  previewLoading.value = true;
  
  try {
    const data = await exportManager.previewExportData({
      products: selectedProducts.value,
      startDate: exportParams.value.startDate,
      endDate: exportParams.value.endDate
    });
    
    previewData.value = data;
  } catch (error) {
    console.error('Failed to preview data:', error);
    alert('预览失败: ' + error.message);
    closePreview();
  } finally {
    previewLoading.value = false;
  }
}

async function startExport() {
  if (!validateParams()) return;
  
  exporting.value = true;
  showExportDialog.value = false;
  
  exportProgress.value = {
    visible: true,
    status: '准备导出...',
    progress: 0,
    error: null,
    cancellable: true
  };
  
  try {
    await exportManager.exportPriceData({
      products: selectedProducts.value,
      format: exportParams.value.format,
      startDate: exportParams.value.startDate,
      endDate: exportParams.value.endDate,
      includeStats: exportParams.value.includeStats,
      onProgress: (progress) => {
        exportProgress.value.status = getProgressStatus(progress.status);
        exportProgress.value.progress = progress.progress;
        
        if (progress.error) {
          exportProgress.value.error = progress.error;
        }
      }
    });
    
    // 导出成功
    setTimeout(() => {
      exportProgress.value.visible = false;
      emit('export-completed', {
        format: exportParams.value.format,
        products: selectedProducts.value.length
      });
    }, 1500);
    
  } catch (error) {
    exportProgress.value.error = error.message;
    exportProgress.value.cancellable = false;
  } finally {
    exporting.value = false;
  }
}

function getProgressStatus(status) {
  const statusMap = {
    preparing: '准备数据...',
    fetching: '获取数据...',
    processing: '处理数据...',
    completed: '导出完成！',
    error: '导出失败'
  };
  return statusMap[status] || status;
}

function cancelExport() {
  exportManager.cancelExport();
  exportProgress.value.visible = false;
  exporting.value = false;
}

function closeDialog() {
  showExportDialog.value = false;
  validationErrors.value = [];
}

function closePreview() {
  showPreviewDialog.value = false;
  previewData.value = null;
}

onMounted(() => {
  loadProducts();
});
</script>

<style scoped>
.price-export-panel {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.panel-header h3 {
  margin: 0;
  color: #333;
}

.export-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #005BAC;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.export-btn:hover {
  background-color: #004590;
}

.export-btn .icon {
  font-size: 1.1rem;
}

/* 导出对话框 */
.export-dialog-overlay,
.preview-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.export-dialog,
.preview-dialog {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 700px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #eee;
}

.dialog-header h4 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
}

.dialog-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

/* 产品选择 */
.product-selection {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.filter-input {
  padding: 0.4rem 0.8rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.product-list {
  max-height: 200px;
  overflow-y: auto;
  padding: 0.5rem;
}

.product-item {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  cursor: pointer;
  border-radius: 4px;
}

.product-item:hover {
  background-color: #f5f5f5;
}

.product-item input[type="checkbox"] {
  margin-right: 0.5rem;
}

.product-info {
  margin-left: auto;
  font-size: 0.85rem;
  color: #666;
}

/* 日期范围 */
.date-range {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.date-input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

/* 导出格式 */
.format-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.format-option {
  display: flex;
  align-items: center;
  padding: 1rem;
  border: 2px solid #e0e0e0;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.format-option:hover {
  border-color: #005BAC;
  background-color: #f8f9fa;
}

.format-option.active {
  border-color: #005BAC;
  background-color: #e8f4ff;
}

.format-icon {
  font-size: 2rem;
  margin-right: 1rem;
}

.format-info {
  flex: 1;
}

.format-name {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.format-desc {
  font-size: 0.85rem;
  color: #666;
}

.format-features {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.feature-tag {
  font-size: 0.75rem;
  padding: 0.2rem 0.5rem;
  background-color: #e0e0e0;
  border-radius: 3px;
  color: #666;
}

/* 附加选项 */
.extra-options label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.extra-options input[type="checkbox"] {
  margin-right: 0.5rem;
}

/* 错误提示 */
.error-messages {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #fbeaea;
  border-radius: 4px;
}

.error-item {
  margin: 0 0 0.5rem 0;
  color: #D92E2E;
  font-size: 0.9rem;
}

.error-item:last-child {
  margin-bottom: 0;
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #eee;
}

.btn-cancel,
.btn-preview,
.btn-export {
  padding: 0.5rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-cancel {
  background-color: #f5f5f5;
  color: #666;
}

.btn-cancel:hover {
  background-color: #e0e0e0;
}

.btn-preview {
  background-color: #49A9E8;
  color: white;
}

.btn-preview:hover:not(:disabled) {
  background-color: #3a8bc7;
}

.btn-export {
  background-color: #005BAC;
  color: white;
}

.btn-export:hover:not(:disabled) {
  background-color: #004590;
}

.btn-preview:disabled,
.btn-export:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 预览对话框 */
.preview-content {
  padding: 1.5rem;
  min-height: 300px;
}

.preview-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.preview-table-wrapper {
  overflow-x: auto;
}

.preview-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.preview-table th,
.preview-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.preview-table th {
  background-color: #f5f5f5;
  font-weight: 500;
  color: #333;
}

.price-up {
  color: #D92E2E;
}

.price-down {
  color: #00A854;
}

.preview-note {
  margin-top: 1rem;
  text-align: center;
  color: #666;
  font-size: 0.85rem;
}

/* 导出进度 */
.export-progress {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 300px;
  z-index: 1000;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.cancel-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  font-size: 0.85rem;
}

.cancel-btn:hover {
  color: #D92E2E;
}

.progress-bar {
  height: 6px;
  background-color: #e0e0e0;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #005BAC;
  transition: width 0.3s ease;
}

.progress-error {
  margin-top: 1rem;
  color: #D92E2E;
  font-size: 0.85rem;
}

/* 加载动画 */
.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #005BAC;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式 */
@media (max-width: 768px) {
  .export-dialog,
  .preview-dialog {
    width: 95%;
    max-height: 95vh;
  }
  
  .format-option {
    flex-direction: column;
    text-align: center;
  }
  
  .format-icon {
    margin-right: 0;
    margin-bottom: 0.5rem;
  }
  
  .export-progress {
    right: 1rem;
    bottom: 1rem;
    left: 1rem;
    min-width: auto;
  }
}
</style>