# Spring Snow Food Analysis System - Frontend

> 春雪食品分析系统前端应用 - 基于Vue.js 3的现代化单页应用

## 🚀 项目简介

Spring Snow Food Analysis System 是一个专业的食品行业数据分析平台，提供销售分析、库存管理、产销率监控、价格趋势等核心功能。本项目是系统的前端部分，采用Vue.js 3 + Vite构建的现代化单页应用。

## ✨ 主要特性

- 🎯 **专业的数据可视化**: 基于Apache ECharts的丰富图表展示
- 📊 **实时数据监控**: 支持实时数据更新和动态展示
- 📱 **响应式设计**: 完美适配桌面、平板、移动设备
- 🎨 **专业UI设计**: 财务报表级别的专业视觉设计
- ⚡ **高性能**: Vite构建工具，热重载，代码分割
- 🔒 **安全可靠**: 完善的认证系统和数据验证
- 🧩 **组件化架构**: 50+可复用组件，易于维护和扩展

## 🛠️ 技术栈

- **核心框架**: [Vue.js 3](https://vuejs.org/) (使用 Composition API)
- **构建工具**: [Vite 5.x](https://vitejs.dev/)
- **状态管理**: [Pinia](https://pinia.vuejs.org/)
- **路由管理**: [Vue Router 4](https://router.vuejs.org/)
- **单元测试**: [Vitest](https://vitest.dev/)
- **图表库**: [Apache ECharts 5](https://echarts.apache.org/)
- **HTTP客户端**: [Axios](https://axios-http.com/)
- **日期处理**: [Day.js](https://day.js.org/)
- **代码规范**: [ESLint](https://eslint.org/) + [Prettier](https://prettier.io/)
- **样式方案**: 原生 CSS3 + CSS Variables

## 📦 项目设置与运行

### 环境要求

- **Node.js**: `^18.0.0` 或 `^20.0.0` (推荐)
- **npm**: `^9.0.0` 或更高版本

### 安装依赖

克隆项目后，在 `frontend` 目录下运行以下命令安装所有依赖：

```bash
npm install
```

### 常用脚本

- **启动开发服务器**:
  ```bash
  npm run dev
  ```
  应用将在 `http://localhost:5173` (或其他可用端口) 上运行，并支持热模块重载 (HMR)。

- **生产构建**:
  ```bash
  npm run build
  ```
  此命令会将应用打包到 `dist` 目录，并进行代码压缩和优化。

- **预览生产构建**:
  ```bash
  npm run preview
  ```
  在本地启动一个静态服务器，用于预览 `dist` 目录的内容。

- **运行测试**:
  ```bash
  npm run test
  ```
  执行所有单元测试和集成测试。

- **代码格式化与检查**:
  ```bash
  # 自动修复ESLint问题
  npm run lint

  # 使用Prettier格式化所有代码
  npm run format
  ```

## 📁 项目结构

```
frontend/
├── public/              # 静态资源，直接复制到构建输出目录
├── src/
│   ├── assets/          # 静态资源 (由Vite处理)
│   ├── components/      # 全局Vue组件
│   │   ├── auth/        # 认证相关组件
│   │   ├── charts/      # ECharts封装及图表组件
│   │   ├── common/      # 基础通用组件 (如按钮, 卡片)
│   │   ├── dashboard/   # 仪表板专用组件
│   │   ├── inventory/   # 库存模块组件
│   │   ├── layout/      # 应用布局组件 (页头, 导航)
│   │   ├── pricing/     # 价格模块组件
│   │   ├── production/  # 生产模块组件
│   │   └── sales/       # 销售模块组件
│   ├── composables/     # Vue组合式函数 (如 useDateRange)
│   ├── router/          # 路由配置 (Vue Router)
│   ├── stores/          # 全局状态管理 (Pinia)
│   ├── styles/          # 全局样式
│   ├── utils/           # 通用工具函数 (API, 格式化, 性能监控等)
│   ├── views/           # 页面级组件
│   ├── App.vue          # 根组件
│   └── main.js          # 应用入口文件
├── .env.development     # 开发环境变量
├── .env.production      # 生产环境变量
├── index.html           # HTML入口文件
├── package.json         # 项目依赖和脚本
└── vite.config.js       # Vite配置文件
```

## 🎯 核心功能

本项目提供了一个功能丰富的数据分析平台，主要模块包括：

### 📊 仪表板 (`/dashboard`)
- **功能**: 提供整个系统的业务总览，集中展示关键绩效指标（KPIs），如总销售额、库存水平、核心产品产销率等。
- **技术亮点**: 组件化设计，支持用户自定义布局和指标卡片。

### 📈 销售分析 (`/sales`)
- **功能**: 深入分析销售数据，包括销售趋势、产品类别分布、区域销售表现等。支持多维度数据筛选和图表钻取。
- **图表**: 销售趋势折线图、销售额条形图、产品分类饼图。

### 📦 库存管理 (`/inventory`)
- **功能**: 监控库存水平，分析库存周转率，预警高库存风险。提供库存明细查询和历史趋势分析。
- **特色**: 可视化展示TOP15库存产品，帮助快速定位关键库存问题。

### 🏭 产销率分析 (`/production`)
- **功能**: 核心模块之一，用于监控生产与销售的平衡关系。通过对比产销率与100%基准线，评估产品是否供过于求或供不应求。
- **图表**: 产销率趋势图、生产与销售对比图。

### 💰 价格监控 (`/price-monitoring`)
- **功能**: 跟踪产品价格波动，提供价格预警配置，并与其他市场数据进行对比分析。
- **组件**: 价格趋势图、价格排行榜、预警配置面板。

### 📋 详细数据 (`/details`)
- **功能**: 提供原始数据的表格查询、筛选、排序和导出功能，满足深度分析和报告制作的需求。

## 🔧 开发指南

### 组件开发

```vue
<template>
  <BaseCard title="示例组件" :loading="loading">
    <div class="example-content">
      <!-- 组件内容 -->
    </div>
  </BaseCard>
</template>

<script setup>
import { ref } from 'vue'
import BaseCard from '@/components/common/BaseCard.vue'

const loading = ref(false)
</script>
```

### 状态管理

```javascript
// stores/example.js
import { defineStore } from 'pinia'

export const useExampleStore = defineStore('example', {
  state: () => ({
    data: [],
    loading: false
  }),
  
  actions: {
    async fetchData() {
      this.loading = true
      try {
        // API调用
      } finally {
        this.loading = false
      }
    }
  }
})
```

### 图表组件

```vue
<template>
  <EChartsWrapper
    :options="chartOptions"
    height="400px"
    @chart-ready="onChartReady"
  />
</template>

<script setup>
import { computed } from 'vue'
import EChartsWrapper from '@/components/charts/EChartsWrapper.vue'

const chartOptions = computed(() => ({
  // ECharts配置
}))
</script>
```

## ✅ 测试

项目使用 [Vitest](https://vitest.dev/) 进行单元测试和集成测试，以保证代码质量和功能稳定性。

### 运行测试

```bash
# 运行所有测试
npm run test

# 启动UI模式，方便调试
npm run test:ui

# 生成测试覆盖率报告
npm run test:coverage
```

### 测试文件结构

测试文件位于其关联源文件的同级 `__tests__` 目录下，或在顶层 `test/` 目录中，遵循 `*.spec.js` 或 `*.test.js` 的命名约定。

```
src/
└── utils/
    ├── __tests__/
    │   └── date.test.js  # `date.js` 的单元测试
    └── date.js
```

## 🚀 部署

### 环境配置

项目通过 `.env` 文件管理环境变量：

- **`.env.development`**: 开发环境配置，例如本地API代理。
- **`.env.production`**: 生产环境配置，例如生产API端点。
- **`.env.staging`**: (可选) 预发布环境配置。

### 部署流程

项目可以轻松部署到任何支持静态网站托管的平台。

1.  **构建应用**:
    ```bash
    npm run build
    ```
2.  **部署 `dist` 目录**:
    将生成的 `dist` 目录的内容上传到你的静态托管服务，例如 Netlify, Vercel, Cloudflare Pages, 或自己的服务器。

### 自动化部署

项目中包含一个示例部署脚本 `scripts/deploy.sh`，可以根据实际需求进行修改，用于自动化部署流程。通过 `npm run deploy:prod` 命令可以触发此脚本。

## 📊 性能优化

项目从多个维度进行了性能优化，以确保流畅的用户体验。

### 关键优化措施

- **代码分割 (Code Splitting)**: 利用Vite的内置功能，所有页面级组件（Views）都通过动态 `import()` 实现懒加载，显著减小了初始包体积。
- **组件懒加载**: 对于非首屏或用户交互后才显示的复杂组件（如模态框、复杂图表），同样采用异步加载。
- **Tree Shaking**: 借助Vite和Rollup，自动移除生产构建中未使用的代码。
- **静态资源优化**:
  - **图片压缩**: 集成构建工具对图片资源进行自动化压缩。
  - **缓存策略**: 通过HTTP头为静态资源配置了长效缓存。
- **数据预加载**: 在应用初始化阶段（`main.js`），异步预加载核心业务数据（如日期范围），避免用户首次操作时的等待。
- **虚拟滚动**: 在渲染大量数据表格时，采用虚拟滚动技术，只渲染视口内的列表项，保证页面滚动性能。

### 自定义性能监控

项目内置了一个强大的性能监控工具 (`src/utils/performance.js`)，可在开发环境下使用：

- **自定义测量**: 使用 `startMeasure(name)` 和 `endMeasure(name)` 精确测量任意代码块的执行时间。
- **长任务监控**: 自动监控并警告超过50ms的JavaScript长任务。
- **内存监控**: 跟踪JavaScript堆内存使用情况。
- **综合报告**: 生成包含页面加载、资源耗时、内存使用和自定义测量的完整性能报告。

## 🔍 调试工具

为了提升开发效率和问题定位速度，项目内置了丰富的调试工具。

### 全局调试对象

在开发模式下，可以在浏览器控制台通过 `window.__DEBUG__` 访问以下工具：

- **`window.__DEBUG__.performanceMonitor`**: 访问性能监控器实例，可手动调用其API，如 `generateReport()`。
- **`window.__DEBUG__.errorHandler`**: 访问全局错误处理器实例，可查看已捕获的错误列表 (`getErrors()`) 或统计信息 (`getErrorStats()`)。
- **`window.__DEBUG__.app`**: 访问Vue应用实例，方便检查全局属性或组件状态。

### 全局错误处理

项目实现了统一的错误处理机制 (`src/utils/errorHandler.js`)：

- **自动捕获**: 自动捕获未处理的Promise拒绝、JavaScript运行时错误和资源加载失败。
- **分类处理**: 根据错误类型（如API请求失败、代码异常）向用户显示不同的友好提示。
- **开发环境日志**: 在开发环境下，所有错误都会在控制台详细打印，并附带上下文信息。
- **生产环境上报**: 在生产环境中，错误信息被格式化并准备好发送到第三方监控服务（如Sentry）。

## 📝 代码规范

- 使用ESLint进行代码质量检查
- 使用Prettier进行代码格式化
- 遵循Vue.js官方风格指南
- 组件命名使用PascalCase
- 文件命名使用kebab-case

## 🤝 贡献指南

我们欢迎任何形式的贡献！请遵循以下步骤：

1.  **Fork** 本项目仓库。
2.  **克隆** 你自己的副本到本地 (`git clone https://github.com/YourUsername/my-fullstack-project.git`)。
3.  **创建新分支** (`git checkout -b feature/your-amazing-feature`)。
4.  **进行修改** 并编写必要的测试。
5.  **确保代码规范** (`npm run lint` 和 `npm run format`)。
6.  **提交你的更改** (`git commit -m 'feat: Add some amazing feature'`)。我们推荐使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范。
7.  **推送到你的分支** (`git push origin feature/your-amazing-feature`)。
8.  **创建 Pull Request** 指向原始仓库的 `main` 分支。

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🆘 支持

如果您遇到问题或有建议，请：

1. 查看[文档](./docs/)
2. 搜索[Issues](../../issues)
3. 创建新的Issue

## 📞 联系方式

- 项目维护者: Spring Snow Team
- 邮箱: <EMAIL>
- 官网: https://springsnow.com

---

**Made with ❤️ by Spring Snow Team**
