<template>
  <div class="sales-page">
    <!-- 销售概览卡片 -->
    <div class="sales-overview">
      <div class="overview-cards">
        <MetricCard
          title="总销量"
          :value="salesStore.totalSalesVolume"
          unit="T"
          icon="📦"
          variant="primary"
          :loading="salesStore.isLoading"
          :format="formatVolumeValue"
        />
        <MetricCard
          title="总销售额"
          :value="salesStore.totalSalesAmount"
          unit="元"
          icon="💰"
          variant="secondary"
          :loading="salesStore.isLoading"
          :format="formatSalesAmount"
        />
        <MetricCard
          title="平均单价"
          :value="salesStore.averagePrice"
          unit=""
          icon="📊"
          variant="tertiary"
          :loading="salesStore.isLoading"
          :format="formatPrice"
        />
        <MetricCard
          title="价格变化"
          :value="salesStore.priceChangeRate"
          unit="%"
          icon="📈"
          variant="accent"
          :loading="salesStore.isLoading"
          :format="formatChangeRate"
          :class="getTrendColorClass(salesStore.latestPrice, salesStore.firstPrice)"
        />
      </div>
    </div>

    <!-- 控制面板 -->
    <div class="sales-controls">
      <!-- 快捷日期选择按钮 -->
      <div class="quick-date-buttons">
        <BaseButton
          v-for="option in quickDateOptions"
          :key="option.value"
          @click="selectQuickDate(option.value)"
          :variant="selectedQuickDate === option.value ? 'primary' : 'ghost'"
          size="small"
        >
          {{ option.label }}
        </BaseButton>
      </div>

      <div class="date-selectors">
        <div class="date-range-selector">
          <label>数据范围：</label>
          <input
            v-model="dateRange.start"
            type="date"
            @change="updateDateRange"
          />
          <span>至</span>
          <input
            v-model="dateRange.end"
            type="date"
            @change="updateDateRange"
          />
        </div>

        <div class="month-selector">
          <label>按月选择：</label>
          <MonthSelector
            v-model="monthRange"
            @change="handleMonthChange"
          />
        </div>
      </div>

      <div class="control-buttons">
        <BaseButton
          @click="refreshData"
          :loading="salesStore.isLoading"
          variant="primary"
          size="small"
          icon="🔄"
        >
          刷新数据
        </BaseButton>

        <BaseButton
          @click="exportData"
          variant="secondary"
          size="small"
          icon="📊"
        >
          导出数据
        </BaseButton>
      </div>
    </div>

    <!-- 销售趋势图表 -->
    <div class="sales-charts">
      <div class="chart-section">
        <SalesTrendChart
          :start-date="dateRange.start"
          :end-date="dateRange.end"
          @data-updated="handleSalesDataUpdate"
          @date-range-changed="handleDateRangeChanged"
        />
      </div>

      <div class="chart-section">
        <SalesVolumeChart
          :start-date="dateRange.start"
          :end-date="dateRange.end"
        />
      </div>
    </div>

    <!-- 销售明细表格 -->
    <div class="sales-details">
      <SalesDetailTable
        :start-date="dateRange.start"
        :end-date="dateRange.end"
        @export-data="handleExportData"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useSalesStore } from '@/stores/sales'
import { formatSalesVolume, formatPrice, formatChangeRate, getTrendColorClass, formatAmount, formatInventory } from '@/utils/formatters'
import { useDateRange } from '@/composables/useDateRange'
import MetricCard from '@/components/dashboard/MetricCard.vue'
import SalesTrendChart from '@/components/sales/SalesTrendChart.vue'
import SalesVolumeChart from '@/components/sales/SalesVolumeChart.vue'
import SalesDetailTable from '@/components/sales/SalesDetailTable.vue'
import BaseButton from '@/components/common/BaseButton.vue'
import MonthSelector from '@/components/common/MonthSelector.vue'

const salesStore = useSalesStore()
const { dateRange: dynamicDateRange, ensureDateRangeLoaded } = useDateRange()

// 本地状态 - 使用动态计算的默认日期范围
const dateRange = ref({
  start: dynamicDateRange.value.start,
  end: dynamicDateRange.value.end
})
const monthRange = ref({ start: '', end: '' })

// 快捷日期选择选项
const quickDateOptions = ref([
  { label: '近7天', value: 7 },
  { label: '近30天', value: 30 },
  { label: '近90天', value: 90 }
])
const selectedQuickDate = ref(null)

// 格式化函数
const formatSalesAmount = (value) => {
  return formatAmount(value, { showCurrency: false, showUnit: true })
}

const formatVolumeValue = (value) => {
  return formatInventory(value, 'T', false) // 不显示单位，避免重复
}

// 方法
const updateDateRange = async () => {
  try {
    await salesStore.updateDateRange(dateRange.value.start, dateRange.value.end)
  } catch (error) {
    console.error('Failed to update date range:', error)
  }
}

const refreshData = async () => {
  try {
    await salesStore.refreshData()
  } catch (error) {
    console.error('Failed to refresh sales data:', error)
  }
}

const exportData = () => {
  // TODO: 实现数据导出功能
  console.log('Export sales data')
}

const handleSalesDataUpdate = (data) => {
  console.log('Sales data updated:', data.length, 'records')
}

const handleExportData = (data) => {
  console.log('Export data:', data.length, 'records')
}

const handleDateRangeChanged = (newDateRange) => {
  // 更新日期范围
  dateRange.value = newDateRange
  // 刷新数据
  updateDateRange()
}

const handleMonthChange = (range) => {
  // 使用月份选择器的日期范围
  dateRange.value = range
  
  // 清除快捷日期选择
  selectedQuickDate.value = null
  
  // 触发数据更新
  updateDateRange()
}

const selectQuickDate = (days) => {
  // 设置选中状态
  selectedQuickDate.value = days
  
  // 计算日期范围
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(endDate.getDate() - days + 1)
  
  // 格式化日期为 YYYY-MM-DD
  const formatDate = (date) => {
    const y = date.getFullYear()
    const m = String(date.getMonth() + 1).padStart(2, '0')
    const d = String(date.getDate()).padStart(2, '0')
    return `${y}-${m}-${d}`
  }
  
  // 更新日期范围
  dateRange.value = {
    start: formatDate(startDate),
    end: formatDate(endDate)
  }
  
  // 触发数据更新
  updateDateRange()
}

// 生命周期
onMounted(async () => {
  try {
    // 确保日期范围已加载
    await ensureDateRangeLoaded()
    
    // 更新本地日期状态
    dateRange.value = {
      start: dynamicDateRange.value.start,
      end: dynamicDateRange.value.end
    }
    
    if (!salesStore.hasData) {
      await salesStore.fetchAllSalesData()
    }
  } catch (error) {
    console.error('Failed to load sales data:', error)
  }
})
</script>

<style scoped>
.sales-page {
  padding: 0;
}

.sales-overview {
  margin-bottom: 32px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.sales-charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.chart-section {
  min-height: 400px;
}

.sales-details {
  margin-bottom: 32px;
}

.sales-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
  background: var(--background-light);
  border-radius: 12px;
}

.quick-date-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.date-selectors {
  display: flex;
  gap: 24px;
  align-items: center;
  flex-wrap: wrap;
}

.date-range-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.9em;
}

.date-range-selector label {
  color: var(--text-secondary);
  font-weight: 500;
}

.date-range-selector input {
  padding: 6px 10px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.9em;
}

.date-range-selector input:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.month-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.9em;
}

.month-selector label {
  color: var(--text-secondary);
  font-weight: 500;
}

.year-select,
.month-select {
  padding: 6px 10px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.9em;
  background: white;
  cursor: pointer;
}

.year-select:focus,
.month-select:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.control-buttons {
  display: flex;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .sales-charts {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .overview-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .sales-controls {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .date-range-selector {
    justify-content: center;
    flex-wrap: wrap;
  }

  .control-buttons {
    justify-content: center;
  }
}
</style>
