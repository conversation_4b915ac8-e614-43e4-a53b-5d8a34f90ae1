<template>
  <div class="price-stats-summary">
    <div class="stats-grid">
      <div class="stat-card" :class="{ 'loading': loading }">
        <div class="stat-icon">
          <i class="fas fa-chart-bar"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">
            {{ loading ? '--' : stats.total_products || 0 }}
          </div>
          <div class="stat-label">监控产品</div>
        </div>
      </div>

      <div class="stat-card" :class="{ 'loading': loading }">
        <div class="stat-icon">
          <i class="fas fa-exchange-alt"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">
            {{ loading ? '--' : (stats.price_stats?.total_adjustments || 0) }}
          </div>
          <div class="stat-label">价格调整</div>
        </div>
      </div>

      <div class="stat-card" :class="{ 'loading': loading }">
        <div class="stat-icon">
          <i class="fas fa-calculator"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">
            {{ loading ? '--' : formatCurrency(stats.price_stats?.avg_price || 0, '元/T', true) }}
          </div>
          <div class="stat-label">平均价格 (元/T)</div>
        </div>
      </div>

      <div class="stat-card" :class="{ 'loading': loading }">
        <div class="stat-icon">
          <i class="fas fa-arrows-alt-v"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">
            <span class="price-range">
              {{ loading ? '--' : formatCurrency(stats.price_stats?.min_price || 0, '', false) }}
              <span class="separator">~</span>
              {{ loading ? '--' : formatCurrency(stats.price_stats?.max_price || 0, '', false) }}
            </span>
          </div>
          <div class="stat-label">价格区间 (元/T)</div>
        </div>
      </div>

      <div class="stat-card date-range" :class="{ 'loading': loading }">
        <div class="stat-icon">
          <i class="fas fa-calendar-alt"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">
            <span class="date-text">
              {{ formatDateRange(stats.date_range) }}
            </span>
          </div>
          <div class="stat-label">监控时间</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { formatCurrency, formatDate } from '@/utils/formatters'

// Props
const props = defineProps({
  stats: {
    type: Object,
    default: () => ({})
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// 方法
const formatDateRange = (dateRange) => {
  if (!dateRange || !dateRange.start_date || !dateRange.end_date) {
    return '无数据'
  }
  
  const start = formatDate(dateRange.start_date, 'MM-DD')
  const end = formatDate(dateRange.end_date, 'MM-DD')
  
  if (start === end) {
    return start
  }
  
  return `${start} 至 ${end}`
}
</script>

<style scoped>
.price-stats-summary {
  width: 100%;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border-left: 4px solid #005BAC;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-card.loading {
  opacity: 0.6;
}

.stat-card.loading .stat-value,
.stat-card.loading .stat-label {
  color: #999;
}

.stat-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #005BAC 0%, #49A9E8 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.2;
}

.stat-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.price-range {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 16px;
}

.separator {
  font-size: 14px;
  color: #999;
}

.date-range .stat-value {
  font-size: 16px;
}

.date-text {
  color: #005BAC;
  font-weight: 600;
}

/* 不同类型的卡片颜色 */
.stat-card:nth-child(1) {
  border-left-color: #005BAC;
}

.stat-card:nth-child(1) .stat-icon {
  background: linear-gradient(135deg, #005BAC 0%, #49A9E8 100%);
}

.stat-card:nth-child(2) {
  border-left-color: #52c41a;
}

.stat-card:nth-child(2) .stat-icon {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
}

.stat-card:nth-child(3) {
  border-left-color: #ff9500;
}

.stat-card:nth-child(3) .stat-icon {
  background: linear-gradient(135deg, #ff9500 0%, #ffa940 100%);
}

.stat-card:nth-child(4) {
  border-left-color: #722ed1;
}

.stat-card:nth-child(4) .stat-icon {
  background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
}

.stat-card:nth-child(5) {
  border-left-color: #eb2f96;
}

.stat-card:nth-child(5) .stat-icon {
  background: linear-gradient(135deg, #eb2f96 0%, #f759ab 100%);
}

@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }
  
  .stat-card {
    padding: 16px;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
  
  .stat-value {
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }
  
  .stat-card {
    padding: 12px;
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
  
  .stat-icon {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }
  
  .stat-value {
    font-size: 18px;
  }
  
  .stat-label {
    font-size: 12px;
  }
  
  .price-range {
    flex-direction: column;
    gap: 2px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    flex-direction: row;
    text-align: left;
  }
}
</style>
