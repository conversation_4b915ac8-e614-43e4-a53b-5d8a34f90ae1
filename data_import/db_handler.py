"""
数据库处理模块
封装所有数据库交互功能，支持SQLite和Cloudflare D1数据库
"""
import json
import subprocess
import sqlite3
import time
import os
from .config import (
    USE_D1_DATABASE, USE_REMOTE_D1, D1_DB_NAME, DB_NAME, SCRIPT_DIR,
    D1_BATCH_SIZE, D1_MAX_RETRIES, D1_BATCH_DELAY, D1_TIMEOUT,
    TABLE_BATCH_CONFIGS, ENABLE_PERFORMANCE_MONITORING, SHOW_BATCH_SIZE_ESTIMATES
)


def execute_d1_command(command, is_query=False, max_retries=None):
    """Execute a D1 database command using wrangler CLI with retry mechanism"""
    if max_retries is None:
        max_retries = D1_MAX_RETRIES
        
    for attempt in range(max_retries):
        try:
            # 根据配置选择本地或远程D1数据库
            if USE_REMOTE_D1:
                cmd = ['npx', 'wrangler', 'd1', 'execute', D1_DB_NAME, '--remote', '--command', command]
                db_location = "远程D1数据库（生产环境）"
            else:
                cmd = ['npx', 'wrangler', 'd1', 'execute', D1_DB_NAME, '--local', '--command', command]
                db_location = "本地D1数据库（开发环境）"
            
            if attempt == 0:  # 只在第一次尝试时显示
                print(f"执行D1命令到: {db_location}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, 
                                  cwd=os.path.join(SCRIPT_DIR, 'backend'), timeout=D1_TIMEOUT)

            if result.returncode != 0:
                if attempt < max_retries - 1:
                    print(f"D1 command failed (attempt {attempt + 1}/{max_retries}), retrying: {result.stderr[:200]}")
                    time.sleep(2 ** attempt)  # Exponential backoff
                    continue
                else:
                    print(f"D1 command failed after {max_retries} attempts: {result.stderr}")
                    return None

            if is_query and result.stdout:
                # Parse JSON output for queries
                try:
                    # Find the start of the JSON output, ignoring wrangler's logs
                    output = result.stdout.strip()
                    json_start_index = output.find('[')
                    
                    if json_start_index != -1:
                        json_string = output[json_start_index:]
                        # Ensure the parsed content is a list of results
                        parsed_json = json.loads(json_string)
                        if isinstance(parsed_json, list) and len(parsed_json) > 0 and "results" in parsed_json[0]:
                            return parsed_json[0]["results"]
                        return parsed_json # Fallback for other structures
                    else:
                        print(f"No JSON found in D1 query result: {output}")
                        return None
                except json.JSONDecodeError as e:
                    print(f"Failed to parse D1 query result: {e}")
                    print(f"Raw output: {result.stdout}")
                    return None

            return result.stdout
        except subprocess.TimeoutExpired:
            if attempt < max_retries - 1:
                print(f"D1 command timeout (attempt {attempt + 1}/{max_retries}), retrying...")
                time.sleep(2 ** attempt)
                continue
            else:
                print(f"D1 command timeout after {max_retries} attempts")
                return None
        except Exception as e:
            if attempt < max_retries - 1:
                print(f"Error executing D1 command (attempt {attempt + 1}/{max_retries}): {e}")
                time.sleep(2 ** attempt)
                continue
            else:
                print(f"Error executing D1 command after {max_retries} attempts: {e}")
                return None
        finally:
            # Add progressive delay based on attempt
            time.sleep(0.5 + (attempt * 0.1))


def execute_d1_batch_insert(table_name, columns, data_batch):
    """Execute batch insert into D1 database using multi-statement SQL command"""
    if not data_batch:
        return True

    statements = []
    for row_data in data_batch:
        # Convert None values to NULL and format the command
        formatted_values = []
        for value in row_data:
            if value is None:
                formatted_values.append('NULL')
            elif isinstance(value, str):
                # Escape single quotes in strings
                escaped_value = value.replace("'", "''")
                formatted_values.append(f"'{escaped_value}'")
            else:
                formatted_values.append(str(value))
        
        # Note: Ensure each statement ends with a semicolon
        statement = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({', '.join(formatted_values)});"
        statements.append(statement)

    # Join all statements into a single command string
    full_command = " ".join(statements)
    
    # Execute the entire batch command at once
    result = execute_d1_command(full_command)

    if result is not None:
        print(f"Successfully executed batch insert for {len(data_batch)} rows into {table_name}")
        return True
    else:
        print(f"Failed to execute batch insert for {table_name}")
        return False


def get_optimal_batch_config(table_name, data_sample=None):
    """获取表的最优批处理配置"""
    # 使用表特定配置，如果存在的话
    if table_name in TABLE_BATCH_CONFIGS:
        config = TABLE_BATCH_CONFIGS[table_name].copy()
        if ENABLE_PERFORMANCE_MONITORING:
            print(f"使用 {table_name} 表的优化配置: {config['batch_size']} 条/批次, {config['delay']}s 延迟")
        return config
    
    # 默认配置
    return {
        'batch_size': D1_BATCH_SIZE,
        'delay': D1_BATCH_DELAY,
        'target_size_kb': 95
    }


def estimate_batch_size_kb(statements):
    """估算批次的实际大小（KB）"""
    if not statements:
        return 0
    
    # 计算所有语句的总字节数
    total_command = " ".join(statements)
    size_bytes = len(total_command.encode('utf-8'))
    return size_bytes / 1024


def execute_d1_batch_upsert_optimized(table_name, columns, data_batch):
    """
    高性能批量更新插入，使用动态批次大小优化
    """
    if not data_batch:
        return True

    # 获取表的最优配置
    batch_config = get_optimal_batch_config(table_name)
    sub_batch_size = batch_config['batch_size']
    batch_delay = batch_config['delay']
    
    print(f"Starting optimized batch upsert for {len(data_batch)} records...")
    print(f"使用优化配置: {sub_batch_size} 条/批次, {batch_delay}s 延迟")
    
    successful_batches = 0
    total_batches = (len(data_batch) + sub_batch_size - 1) // sub_batch_size
    total_start_time = time.time()
    
    for batch_idx in range(0, len(data_batch), sub_batch_size):
        batch_start_time = time.time()
        sub_batch = data_batch[batch_idx:batch_idx + sub_batch_size]
        batch_num = batch_idx // sub_batch_size + 1
        
        print(f"Processing sub-batch {batch_num}/{total_batches} ({len(sub_batch)} records)...")
        
        # Build INSERT OR REPLACE statements
        statements = []
        for row_data in sub_batch:
            formatted_values = []
            for value in row_data:
                if value is None:
                    formatted_values.append('NULL')
                elif isinstance(value, str):
                    escaped_value = value.replace("'", "''")
                    formatted_values.append(f"'{escaped_value}'")
                else:
                    formatted_values.append(str(value))
            
            # Use INSERT OR REPLACE to handle upsert logic
            statement = f"INSERT OR REPLACE INTO {table_name} ({', '.join(columns)}) VALUES ({', '.join(formatted_values)});"
            statements.append(statement)
        
        # Join all statements for batch execution
        batch_command = " ".join(statements)
        
        # 计算实际批次大小
        if SHOW_BATCH_SIZE_ESTIMATES:
            sql_size_kb = estimate_batch_size_kb(statements)
            print(f"SQL batch size: {sql_size_kb:.1f} KB ({len(statements)} statements)")
            
            # 如果超过目标大小，给出警告
            if sql_size_kb > batch_config.get('target_size_kb', 95):
                print(f"⚠️ 警告: 批次大小 {sql_size_kb:.1f} KB 超过目标 {batch_config.get('target_size_kb', 95)} KB")
        
        # Execute the batch with retry
        result = execute_d1_command(batch_command, max_retries=D1_MAX_RETRIES)
        
        batch_end_time = time.time()
        batch_duration = batch_end_time - batch_start_time
        
        if result is not None:
            successful_batches += 1
            records_per_sec = len(sub_batch) / batch_duration if batch_duration > 0 else 0
            print(f"✓ Sub-batch {batch_num}/{total_batches} 完成 ({batch_duration:.2f}s, {records_per_sec:.1f} records/s)")
        else:
            print(f"✗ Sub-batch {batch_num}/{total_batches} failed")
        
        # 使用配置的延迟
        if batch_num < total_batches:
            time.sleep(batch_delay)
    
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    success_rate = successful_batches / total_batches
    
    # 性能统计
    total_records = len(data_batch)
    avg_records_per_sec = total_records / total_duration if total_duration > 0 else 0
    
    print(f"Batch upsert completed: {successful_batches}/{total_batches} sub-batches successful ({success_rate:.1%})")
    
    if ENABLE_PERFORMANCE_MONITORING:
        print(f"性能统计:")
        print(f"  总耗时: {total_duration:.2f}s")
        print(f"  平均速度: {avg_records_per_sec:.1f} records/s")
        print(f"  总记录数: {total_records}")
        print(f"  成功率: {success_rate:.1%}")
    
    # Consider it successful if at least 80% of batches succeeded
    return success_rate >= 0.8


def get_sqlite_connection():
    """Get SQLite database connection"""
    if not os.path.exists(DB_NAME):
        raise FileNotFoundError(f"SQLite database not found: {DB_NAME}")
    return sqlite3.connect(DB_NAME)


def execute_query(query, use_d1=None):
    """Execute a query on the configured database"""
    if use_d1 is None:
        use_d1 = USE_D1_DATABASE
    
    if use_d1:
        return execute_d1_command(query, is_query=True)
    else:
        conn = get_sqlite_connection()
        try:
            cursor = conn.cursor()
            cursor.execute(query)
            if query.strip().upper().startswith('SELECT'):
                return cursor.fetchall()
            else:
                conn.commit()
                return cursor.rowcount
        finally:
            conn.close()


def verify_table_data(table_name, expected_min_records=None):
    """Verify data in a specific table"""
    print(f"\n--- Verifying {table_name} table ---")
    
    try:
        # Get total count
        count_query = f"SELECT COUNT(*) as count FROM {table_name}"
        count_result = execute_query(count_query)
        
        if count_result:
            if USE_D1_DATABASE:
                total_count = count_result[0]['count']
            else:
                total_count = count_result[0][0]
            print(f"Total records in {table_name}: {total_count}")
            
            if expected_min_records and total_count < expected_min_records:
                print(f"⚠️ Warning: Expected at least {expected_min_records} records, but found {total_count}")
                return False
                
            return True
        else:
            print(f"❌ Failed to query {table_name} table")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying {table_name} table: {e}")
        return False


def verify_products_table():
    """Verify Products table data"""
    return verify_table_data('Products')


def verify_daily_metrics_table():
    """Verify DailyMetrics table data"""
    print(f"\n--- Verifying DailyMetrics table ---")
    
    try:
        # Get total count and date range
        metrics_query = """
        SELECT 
            COUNT(*) as total_count,
            MIN(date) as min_date,
            MAX(date) as max_date,
            COUNT(DISTINCT date) as unique_dates
        FROM DailyMetrics
        """
        
        result = execute_query(metrics_query)
        if result:
            if USE_D1_DATABASE:
                data = result[0]
                print(f"Total DailyMetrics records: {data['total_count']}")
                print(f"Date range: {data['min_date']} to {data['max_date']}")
                print(f"Unique dates: {data['unique_dates']}")
            else:
                data = result[0]
                print(f"Total DailyMetrics records: {data[0]}")
                print(f"Date range: {data[1]} to {data[2]}")
                print(f"Unique dates: {data[3]}")
            return True
        else:
            print("❌ Failed to query DailyMetrics table")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying DailyMetrics table: {e}")
        return False


def verify_price_adjustments_table():
    """Verify PriceAdjustments table data"""
    print(f"\n--- Verifying PriceAdjustments table ---")
    
    try:
        # Get total count, unique products, and date range
        price_query = """
        SELECT 
            COUNT(*) as total_count,
            COUNT(DISTINCT product_id) as unique_products,
            MIN(adjustment_date) as min_date,
            MAX(adjustment_date) as max_date
        FROM PriceAdjustments
        """
        
        result = execute_query(price_query)
        if result:
            if USE_D1_DATABASE:
                data = result[0]
                print(f"Total PriceAdjustments records: {data['total_count']}")
                print(f"Unique products: {data['unique_products']}")
                print(f"Date range: {data['min_date']} to {data['max_date']}")
            else:
                data = result[0]
                print(f"Total PriceAdjustments records: {data[0]}")
                print(f"Unique products: {data[1]}")
                print(f"Date range: {data[2]} to {data[3]}")
            return True
        else:
            print("❌ Failed to query PriceAdjustments table")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying PriceAdjustments table: {e}")
        return False


def create_products_if_not_exist(product_names):
    """Create products in Products table if they don't exist"""
    if not product_names:
        return {}
    
    print(f"Checking and creating products for {len(product_names)} unique product names...")
    
    # Get existing products
    existing_query = "SELECT product_id, product_name FROM Products"
    existing_result = execute_query(existing_query)
    
    existing_products = {}
    if existing_result:
        if USE_D1_DATABASE:
            existing_products = {row['product_name']: row['product_id'] for row in existing_result}
        else:
            existing_products = {row[1]: row[0] for row in existing_result}
    
    # Find products that need to be created
    new_products = [name for name in product_names if name not in existing_products]
    
    if new_products:
        print(f"Creating {len(new_products)} new products...")
        
        # Prepare batch data for new products
        new_product_data = [[name] for name in new_products]
        
        # Insert new products
        success = execute_d1_batch_insert('Products', ['product_name'], new_product_data)
        
        if success:
            # Get updated product mapping
            updated_result = execute_query(existing_query)
            if updated_result:
                if USE_D1_DATABASE:
                    return {row['product_name']: row['product_id'] for row in updated_result}
                else:
                    return {row[1]: row[0] for row in updated_result}
        
        return existing_products
    else:
        print("All products already exist in the database")
        return existing_products


def print_database_info():
    """Print current database configuration"""
    if USE_D1_DATABASE:
        mode = f"Cloudflare D1 ({'远程/生产环境' if USE_REMOTE_D1 else '本地/开发环境'})"
    else:
        mode = "Local SQLite"
    print(f"Database mode: {mode}")