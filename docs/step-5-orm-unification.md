# 步骤五：统一使用ORM以消除SQL注入风险

## 目标和背景

### 目标
在整个后端项目中统一使用 Drizzle ORM，重写所有字符串拼接的SQL查询，消除SQL注入漏洞，提高代码的类型安全性。

### 背景
当前代码大量使用字符串拼接来构建SQL查询，这是导致SQL注入漏洞的常见原因。虽然项目已经引入了Drizzle ORM，但并未充分利用其安全特性。主要问题包括：

1. **安全风险**：字符串拼接容易产生SQL注入漏洞
2. **类型安全缺失**：动态SQL缺乏编译时类型检查
3. **维护困难**：复杂的SQL字符串难以理解和修改
4. **调试复杂**：动态生成的SQL难以调试和优化
5. **代码不一致**：部分使用ORM，部分使用原生SQL

### 风险分析
- **安全漏洞**：恶意输入可能导致数据泄露或破坏
- **数据完整性**：不当的SQL操作可能破坏数据一致性
- **性能问题**：未优化的动态SQL可能影响性能
- **合规风险**：安全漏洞可能导致合规问题

## 具体实施步骤

### 第一步：审计现有SQL代码
1. **识别所有SQL字符串拼接**
   ```bash
   # 查找字符串拼接的SQL
   grep -r "SELECT.*+" backend/src/
   grep -r "INSERT.*+" backend/src/
   grep -r "UPDATE.*+" backend/src/
   grep -r "DELETE.*+" backend/src/
   
   # 查找模板字符串SQL
   grep -r "\`SELECT" backend/src/
   grep -r "\`INSERT" backend/src/
   ```

2. **分析SQL注入风险点**
   ```bash
   # 查找用户输入直接拼接到SQL的情况
   grep -r "req\.query\|req\.body" backend/src/ | grep -E "(SELECT|INSERT|UPDATE|DELETE)"
   ```

3. **评估现有Drizzle使用情况**
   ```bash
   # 检查已使用Drizzle的地方
   grep -r "drizzle\|eq\|gte\|lte" backend/src/
   ```

### 第二步：完善Drizzle Schema定义
1. **更新数据库Schema**
2. **定义完整的类型系统**
3. **建立查询构建器模式**

### 第三步：重写SQL查询
1. **重构ProductFilter类**
2. **替换所有动态SQL**
3. **实现复杂查询的ORM版本**

### 第四步：建立安全查询模式
1. **创建查询服务层**
2. **实现参数验证**
3. **添加查询日志和监控**

## 代码示例

### 修改前的不安全代码
```typescript
// 问题代码 - SQL注入风险
class ProductFilter {
  static getInventoryFilter(productName?: string, category?: string) {
    let sql = "SELECT * FROM DailyMetrics WHERE 1=1";
    
    // 危险！直接拼接用户输入
    if (productName) {
      sql += ` AND product_name LIKE '%${productName}%'`;
    }
    
    if (category) {
      sql += ` AND category = '${category}'`;
    }
    
    // 硬编码的过滤条件
    sql += " AND (product_name NOT LIKE '%鲜%' OR product_name LIKE '%凤肠%')";
    
    return sql;
  }
}

// 在路由中直接执行SQL
app.get('/api/inventory/search', async (c) => {
  const { product_name, category } = c.req.query();
  
  // 危险！用户输入直接用于SQL构建
  const sql = ProductFilter.getInventoryFilter(product_name, category);
  const result = await c.env.DB.prepare(sql).all();
  
  return c.json(result);
});
```

### 修改后的安全ORM代码

#### 1. 完善的Schema定义
```typescript
// backend/src/schema/index.ts
import { sqliteTable, text, real, integer } from 'drizzle-orm/sqlite-core';
import { relations } from 'drizzle-orm';

// 日度指标表
export const dailyMetrics = sqliteTable('DailyMetrics', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  date: text('date').notNull(),
  product_name: text('product_name').notNull(),
  inventory_level: real('inventory_level').default(0),
  production_volume: real('production_volume').default(0),
  sales_volume: real('sales_volume').default(0),
  sales_amount: real('sales_amount').default(0),
  average_price: real('average_price').default(0),
  created_at: text('created_at').default('CURRENT_TIMESTAMP'),
  updated_at: text('updated_at').default('CURRENT_TIMESTAMP')
});

// 产品信息表（如果需要）
export const products = sqliteTable('Products', {
  id: integer('id').primaryKey({ autoIncrement: true }),
  name: text('name').notNull().unique(),
  category: text('category'),
  unit: text('unit').default('T'),
  is_fresh: integer('is_fresh', { mode: 'boolean' }).default(false),
  is_byproduct: integer('is_byproduct', { mode: 'boolean' }).default(false),
  created_at: text('created_at').default('CURRENT_TIMESTAMP')
});

// 定义关系
export const dailyMetricsRelations = relations(dailyMetrics, ({ one }) => ({
  product: one(products, {
    fields: [dailyMetrics.product_name],
    references: [products.name]
  })
}));

// 导出类型
export type DailyMetric = typeof dailyMetrics.$inferSelect;
export type NewDailyMetric = typeof dailyMetrics.$inferInsert;
export type Product = typeof products.$inferSelect;
export type NewProduct = typeof products.$inferInsert;
```

#### 2. 安全的查询构建器
```typescript
// backend/src/utils/queryBuilder.ts
import { drizzle } from 'drizzle-orm/d1';
import { 
  eq, ne, gt, gte, lt, lte, like, notLike, 
  and, or, not, isNull, isNotNull,
  desc, asc, sum, count, avg, max, min
} from 'drizzle-orm';
import { dailyMetrics, products } from '../schema';

export class SafeQueryBuilder {
  private db: any;
  
  constructor(database: D1Database) {
    this.db = drizzle(database);
  }
  
  /**
   * 安全的产品过滤查询
   */
  getInventoryWithFilters(filters: {
    productName?: string;
    category?: string;
    dateRange?: { start: string; end: string };
    excludeFresh?: boolean;
    excludeByproducts?: boolean;
    minInventory?: number;
  }) {
    let query = this.db.select().from(dailyMetrics);
    
    const conditions = [];
    
    // 安全的产品名称过滤
    if (filters.productName) {
      conditions.push(like(dailyMetrics.product_name, `%${filters.productName}%`));
    }
    
    // 日期范围过滤
    if (filters.dateRange) {
      conditions.push(
        and(
          gte(dailyMetrics.date, filters.dateRange.start),
          lte(dailyMetrics.date, filters.dateRange.end)
        )
      );
    }
    
    // 排除鲜品（使用安全的模式匹配）
    if (filters.excludeFresh) {
      conditions.push(
        or(
          not(like(dailyMetrics.product_name, '鲜%')),
          like(dailyMetrics.product_name, '%凤肠%')
        )
      );
    }
    
    // 排除副产品
    if (filters.excludeByproducts) {
      conditions.push(not(like(dailyMetrics.product_name, '%副产品%')));
    }
    
    // 最小库存过滤
    if (filters.minInventory !== undefined) {
      conditions.push(gte(dailyMetrics.inventory_level, filters.minInventory));
    }
    
    // 应用所有条件
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }
    
    return query;
  }
  
  /**
   * 安全的聚合查询
   */
  getInventorySummary(filters: {
    date?: string;
    excludeFresh?: boolean;
  }) {
    const conditions = [];
    
    if (filters.date) {
      conditions.push(eq(dailyMetrics.date, filters.date));
    }
    
    if (filters.excludeFresh) {
      conditions.push(
        or(
          not(like(dailyMetrics.product_name, '鲜%')),
          like(dailyMetrics.product_name, '%凤肠%')
        )
      );
    }
    
    let query = this.db
      .select({
        totalInventory: sum(dailyMetrics.inventory_level),
        productCount: count(dailyMetrics.product_name),
        avgInventory: avg(dailyMetrics.inventory_level),
        maxInventory: max(dailyMetrics.inventory_level),
        minInventory: min(dailyMetrics.inventory_level)
      })
      .from(dailyMetrics);
    
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }
    
    return query;
  }
  
  /**
   * 安全的TOP N查询
   */
  getTopProducts(options: {
    limit: number;
    date?: string;
    orderBy: 'inventory' | 'sales' | 'production';
    excludeFresh?: boolean;
  }) {
    const { limit, date, orderBy, excludeFresh } = options;
    
    const conditions = [];
    
    if (date) {
      conditions.push(eq(dailyMetrics.date, date));
    }
    
    if (excludeFresh) {
      conditions.push(
        or(
          not(like(dailyMetrics.product_name, '鲜%')),
          like(dailyMetrics.product_name, '%凤肠%')
        )
      );
    }
    
    // 确定排序字段
    const orderField = {
      'inventory': dailyMetrics.inventory_level,
      'sales': dailyMetrics.sales_volume,
      'production': dailyMetrics.production_volume
    }[orderBy];
    
    let query = this.db
      .select({
        productName: dailyMetrics.product_name,
        inventory: dailyMetrics.inventory_level,
        sales: dailyMetrics.sales_volume,
        production: dailyMetrics.production_volume,
        date: dailyMetrics.date
      })
      .from(dailyMetrics)
      .orderBy(desc(orderField))
      .limit(Math.min(limit, 50)); // 限制最大返回数量
    
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }
    
    return query;
  }
  
  /**
   * 安全的趋势分析查询
   */
  getTrendData(options: {
    productName?: string;
    startDate: string;
    endDate: string;
    groupBy: 'day' | 'week' | 'month';
  }) {
    const { productName, startDate, endDate, groupBy } = options;
    
    const conditions = [
      gte(dailyMetrics.date, startDate),
      lte(dailyMetrics.date, endDate)
    ];
    
    if (productName) {
      conditions.push(eq(dailyMetrics.product_name, productName));
    }
    
    // 根据分组方式选择不同的查询
    let query = this.db
      .select({
        date: dailyMetrics.date,
        totalInventory: sum(dailyMetrics.inventory_level),
        totalSales: sum(dailyMetrics.sales_volume),
        totalProduction: sum(dailyMetrics.production_volume),
        avgPrice: avg(dailyMetrics.average_price)
      })
      .from(dailyMetrics)
      .where(and(...conditions))
      .groupBy(dailyMetrics.date)
      .orderBy(dailyMetrics.date);
    
    return query;
  }
}
```

#### 3. 重构后的ProductFilter类
```typescript
// backend/src/utils/filters.ts
import { SQL, sql } from 'drizzle-orm';
import { like, not, or, and } from 'drizzle-orm';
import { dailyMetrics } from '../schema';

export class ProductFilter {
  /**
   * 获取库存产品过滤条件（ORM版本）
   */
  static getInventoryFilter(): SQL {
    return or(
      not(like(dailyMetrics.product_name, '鲜%')),
      like(dailyMetrics.product_name, '%凤肠%')
    );
  }
  
  /**
   * 获取销售产品过滤条件
   */
  static getSalesFilter(): SQL {
    return and(
      not(like(dailyMetrics.product_name, '%副产品%')),
      or(
        not(like(dailyMetrics.product_name, '鲜%')),
        like(dailyMetrics.product_name, '%凤肠%')
      )
    );
  }
  
  /**
   * 获取生产产品过滤条件
   */
  static getProductionFilter(): SQL {
    return not(like(dailyMetrics.product_name, '%副产品%'));
  }
  
  /**
   * 客户过滤条件（排除空客户）
   */
  static getCustomerFilter(customerColumn: any): SQL {
    return and(
      sql`${customerColumn} IS NOT NULL`,
      sql`${customerColumn} != ''`
    );
  }
  
  /**
   * 动态构建复合过滤条件
   */
  static buildCompositeFilter(options: {
    includeInventory?: boolean;
    includeSales?: boolean;
    includeProduction?: boolean;
    excludeFresh?: boolean;
    excludeByproducts?: boolean;
    requireCustomer?: boolean;
  }): SQL | undefined {
    const conditions: SQL[] = [];
    
    if (options.excludeFresh) {
      conditions.push(
        or(
          not(like(dailyMetrics.product_name, '鲜%')),
          like(dailyMetrics.product_name, '%凤肠%')
        )
      );
    }
    
    if (options.excludeByproducts) {
      conditions.push(not(like(dailyMetrics.product_name, '%副产品%')));
    }
    
    return conditions.length > 0 ? and(...conditions) : undefined;
  }
}
```

#### 4. 安全的路由实现
```typescript
// backend/src/routes/inventory.ts - 安全版本
import { Hono } from 'hono';
import { SafeQueryBuilder } from '../utils/queryBuilder';
import { validateQuery, sanitizeInput } from '../middleware/validation';

const inventory = new Hono();

/**
 * 安全的库存搜索API
 */
inventory.get('/search', 
  validateQuery(['product_name', 'category']),
  sanitizeInput(['product_name', 'category']),
  async (c) => {
    try {
      const queryBuilder = new SafeQueryBuilder(c.env.DB);
      
      // 从验证和清理后的查询参数中获取值
      const filters = {
        productName: c.get('sanitized_product_name'),
        category: c.get('sanitized_category'),
        excludeFresh: true,
        excludeByproducts: true
      };
      
      // 使用安全的查询构建器
      const query = queryBuilder.getInventoryWithFilters(filters);
      const results = await query.execute();
      
      return c.json({
        success: true,
        data: results,
        count: results.length,
        message: '库存搜索成功'
      });
      
    } catch (error) {
      console.error('库存搜索失败:', error);
      return c.json({
        success: false,
        error: '库存搜索失败',
        details: error instanceof Error ? error.message : '未知错误'
      }, 500);
    }
  }
);

/**
 * 安全的库存汇总API
 */
inventory.get('/summary', async (c) => {
  try {
    const queryBuilder = new SafeQueryBuilder(c.env.DB);
    const { date } = c.req.query();
    
    // 参数验证
    if (date && !/^\d{4}-\d{2}-\d{2}$/.test(date)) {
      return c.json({
        success: false,
        error: '日期格式无效，请使用YYYY-MM-DD格式'
      }, 400);
    }
    
    const query = queryBuilder.getInventorySummary({
      date: date || new Date().toISOString().split('T')[0],
      excludeFresh: true
    });
    
    const summary = await query.execute();
    
    return c.json({
      success: true,
      data: summary[0] || {},
      message: '库存汇总获取成功'
    });
    
  } catch (error) {
    console.error('库存汇总获取失败:', error);
    return c.json({
      success: false,
      error: '库存汇总获取失败'
    }, 500);
  }
});

export default inventory;
```

#### 5. 输入验证和清理中间件
```typescript
// backend/src/middleware/validation.ts
import { Context, Next } from 'hono';

/**
 * 查询参数验证中间件
 */
export const validateQuery = (allowedFields: string[]) => {
  return async (c: Context, next: Next) => {
    const query = c.req.query();
    
    // 检查是否包含不允许的字段
    const queryKeys = Object.keys(query);
    const invalidFields = queryKeys.filter(key => !allowedFields.includes(key));
    
    if (invalidFields.length > 0) {
      return c.json({
        success: false,
        error: '包含不允许的查询参数',
        details: `不允许的参数: ${invalidFields.join(', ')}`
      }, 400);
    }
    
    await next();
  };
};

/**
 * 输入清理中间件
 */
export const sanitizeInput = (fields: string[]) => {
  return async (c: Context, next: Next) => {
    const query = c.req.query();
    
    for (const field of fields) {
      if (query[field]) {
        // 清理SQL注入相关字符
        const sanitized = query[field]
          .replace(/['"`;\\]/g, '') // 移除危险字符
          .replace(/\s+/g, ' ')     // 规范化空格
          .trim()                   // 去除首尾空格
          .substring(0, 100);       // 限制长度
        
        // 将清理后的值存储在context中
        c.set(`sanitized_${field}`, sanitized);
      }
    }
    
    await next();
  };
};

/**
 * SQL注入检测中间件
 */
export const detectSQLInjection = () => {
  return async (c: Context, next: Next) => {
    const query = c.req.query();
    const body = await c.req.json().catch(() => ({}));
    
    const allInputs = { ...query, ...body };
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
      /(--|\/\*|\*\/)/,
      /(\bOR\b.*=.*\bOR\b)/i,
      /(\bAND\b.*=.*\bAND\b)/i,
      /(;|\||&)/
    ];
    
    for (const [key, value] of Object.entries(allInputs)) {
      if (typeof value === 'string') {
        for (const pattern of sqlPatterns) {
          if (pattern.test(value)) {
            console.warn(`检测到可能的SQL注入尝试: ${key}=${value}`);
            return c.json({
              success: false,
              error: '输入包含不安全的内容'
            }, 400);
          }
        }
      }
    }
    
    await next();
  };
};
```

## 验证方法

### 安全性测试
1. **SQL注入测试**
   ```bash
   # 测试常见的SQL注入攻击
   curl "http://localhost:8787/api/inventory/search?product_name='; DROP TABLE DailyMetrics; --"
   curl "http://localhost:8787/api/inventory/search?product_name=1' OR '1'='1"
   curl "http://localhost:8787/api/inventory/search?category=test' UNION SELECT * FROM sqlite_master --"
   ```

2. **参数验证测试**
   ```bash
   # 测试无效参数
   curl "http://localhost:8787/api/inventory/summary?date=invalid-date"
   curl "http://localhost:8787/api/inventory/search?invalid_param=test"
   ```

3. **输入清理测试**
   ```bash
   # 测试特殊字符处理
   curl "http://localhost:8787/api/inventory/search?product_name=test'\";\\"
   ```

### 功能验证
1. **ORM查询测试**
   ```typescript
   // tests/utils/queryBuilder.test.ts
   import { SafeQueryBuilder } from '../src/utils/queryBuilder';
   
   describe('SafeQueryBuilder', () => {
     test('should build safe inventory filter', async () => {
       const builder = new SafeQueryBuilder(mockDB);
       const query = builder.getInventoryWithFilters({
         productName: 'test',
         excludeFresh: true
       });
       
       // 验证生成的SQL不包含直接拼接的用户输入
       expect(query.toSQL()).not.toContain("'test'");
     });
   });
   ```

2. **类型安全验证**
   ```bash
   # TypeScript编译检查
   npx tsc --noEmit --strict
   ```

### 性能测试
1. **查询性能对比**
   ```bash
   # 对比ORM和原生SQL的性能
   npm run benchmark:queries
   ```

2. **内存使用监控**
   ```bash
   # 监控ORM的内存使用
   node --inspect backend/src/index.ts
   ```

## 注意事项

### 迁移策略
1. **渐进式迁移**：逐个API端点迁移到ORM
2. **向后兼容**：保持API接口不变
3. **充分测试**：每个迁移的查询都要进行安全测试

### 性能考虑
1. **查询优化**：确保ORM生成的SQL是优化的
2. **连接池**：合理配置数据库连接池
3. **缓存策略**：为频繁查询添加缓存

### 安全最佳实践
1. **最小权限**：数据库用户只授予必需的权限
2. **输入验证**：所有用户输入都要验证和清理
3. **日志记录**：记录所有数据库操作用于审计

## 预期结果

### 安全性提升
1. **SQL注入风险**：完全消除SQL注入漏洞
2. **类型安全**：编译时检查所有数据库操作
3. **输入验证**：所有用户输入都经过验证和清理

### 代码质量改善
1. **可维护性**：ORM查询更易理解和修改
2. **可测试性**：每个查询都可以独立测试
3. **一致性**：统一的数据库访问模式

### 量化指标
- **SQL注入漏洞**：从潜在风险降低到0
- **类型安全覆盖率**：达到100%
- **代码复杂度**：降低30%以上
- **查询性能**：保持或提升现有性能

## 后续优化建议

1. **查询优化**：使用数据库查询分析工具优化慢查询
2. **缓存层**：为频繁查询添加Redis缓存
3. **监控告警**：建立数据库性能监控和异常告警
4. **安全审计**：定期进行安全代码审计和渗透测试
