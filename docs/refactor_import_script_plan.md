# Refactoring Plan for `import_real_data.py`

**Version:** 1.0
**Date:** 2025-07-26
**Author:** AI Assistant

## 1. Introduction & Motivation

The current `import_real_data.py` script is a monolithic file containing over 1300 lines of code. It is responsible for loading data from multiple Excel files, performing complex data cleaning and transformation, interacting with two different types of databases (SQLite and Cloudflare D1), and verifying the imported data.

While functional, this monolithic structure presents several challenges:

*   **Low Cohesion & High Coupling:** Business logic, database logic, and configuration are tightly interwoven, making the code difficult to understand and modify.
*   **Poor Readability & Maintainability:** A developer needs to navigate a very large file to make even minor changes, increasing the risk of introducing bugs.
*   **Difficult to Test:** It is nearly impossible to unit test specific functions (e.g., a data processing function) without running the entire script and having a live database connection.
*   **Limited Reusability:** Critical functions, such as the Cloudflare D1 command executor, cannot be easily reused by other parts of the application.

This refactoring aims to address these issues by decomposing the script into smaller, well-defined modules that adhere to the **Single Responsibility Principle (SRP)**.

## 2. Proposed Architecture

We propose splitting the script into five distinct Python modules, each with a clear and specific purpose. The main script will be refactored to act as an orchestrator, coordinating the work between these modules.

### 2.1. Module Dependency Diagram

The following diagram illustrates the proposed modular structure and their dependencies:

```mermaid
graph TD
    subgraph "Data Source"
        A[Excel Files]
    end

    subgraph "Code Modules"
        B(main.py) -- "Imports configuration" --> C(config.py)
        B -- "Loads data using" --> D(data_loader.py)
        B -- "Processes data using" --> E(data_processors.py)
        B -- "Writes & Verifies data using" --> F(db_handler.py)
        D -- "Reads from" --> A
    end

    subgraph "Database"
        G[SQLite / Cloudflare D1]
    end

    F -- "Reads/Writes to" --> G

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style G fill:#ccf,stroke:#333,stroke-width:2px
```

## 3. Module Breakdown & Responsibilities

The new modules will be organized within a new directory, `data_import/`, to keep them grouped together.

### 3.1. `data_import/config.py`

*   **Responsibility:** Centralized configuration management.
*   **Contents:**
    *   File paths (`EXCEL_FOLDER`, `inv_summary_path`, etc.).
    *   Database connection details (`DB_NAME`, `D1_DB_NAME`).
    *   Feature flags and environment switches (`USE_D1_DATABASE`, `USE_REMOTE_D1`).

### 3.2. `data_import/db_handler.py`

*   **Responsibility:** Encapsulate all database interactions. It will provide a consistent API regardless of whether SQLite or D1 is being used.
*   **Contents:**
    *   `execute_d1_command()`
    *   `execute_d1_batch_insert()`
    *   `execute_d1_batch_upsert_optimized()`
    *   Functions for local SQLite connection and operations.
    *   Data verification functions that query the database.

### 3.3. `data_import/data_loader.py`

*   **Responsibility:** Handle the loading of raw data from source files (Excel).
*   **Contents:**
    *   A function `load_all_data(config)` that takes the configuration module as input and returns a dictionary of Pandas DataFrames (e.g., `{'inventory': df_inv, 'sales': df_sales}`).

### 3.4. `data_import/data_processors.py`

*   **Responsibility:** Contain all the business logic for cleaning, filtering, and transforming the raw data.
*   **Contents:**
    *   `filter_products()`
    *   `filter_products_for_inventory()`
    *   `process_inventory_data()`
    *   `process_production_data()`
    *   `process_sales_data()`
    *   `process_price_adjustments_data()`
    *   `extract_date_info()`

### 3.5. `data_import/main.py` (The new orchestrator)

*   **Responsibility:** Define the high-level workflow of the data import process.
*   **Contents:**
    *   Import necessary modules (`config`, `data_loader`, `data_processors`, `db_handler`).
    *   Define the main execution flow:
        1.  Load data using `data_loader`.
        2.  Process each DataFrame using functions from `data_processors`.
        3.  Combine and prepare data for database insertion.
        4.  Use `db_handler` to insert the processed data into the target database.
        5.  Use `db_handler` to run verification queries.
    *   Handle high-level logging and error reporting.

## 4. Refactoring Steps

The refactoring process will be executed in the following sequence to minimize disruption:

1.  **Create Directory Structure:** Create a new directory named `data_import` in the project root.
2.  **Create `config.py`:** Move all configuration variables from the top of `import_real_data.py` into `data_import/config.py`.
3.  **Create `db_handler.py`:** Move all database-related functions (D1 and SQLite commands, verification queries) into `data_import/db_handler.py`.
4.  **Create `data_loader.py`:** Move the Excel file loading logic into `data_import/data_loader.py`.
5.  **Create `data_processors.py`:** Move all Pandas DataFrame processing and cleaning functions into `data_import/data_processors.py`.
6.  **Create `main.py`:** Create a new `data_import/main.py` file. Rewrite the main execution block to import and call functions from the other modules in the correct order.
7.  **Testing:** Execute `data_import/main.py` and verify that the outcome is identical to running the original `import_real_data.py` script. Check the database to ensure data is inserted correctly.
8.  **Cleanup:** Once the new structure is verified, the original `import_real_data.py` can be safely deleted.

## 5. Expected Benefits

Upon completion, this refactoring will yield significant benefits:

*   **Improved Maintainability:** Changes to one area of functionality (e.g., database logic) will be isolated to a single file, making updates faster and safer.
*   **Enhanced Readability:** The `main.py` script will provide a clear, high-level overview of the import process, while implementation details are hidden in their respective modules.
*   **Increased Testability:** Each data processing function can be tested in isolation by providing a sample DataFrame and asserting the output, without any database dependency.
*   **Code Reusability:** The `db_handler.py` module can be imported and used by other scripts or application components that need to interact with the database.
*   **Better Onboarding:** New developers can understand the data import pipeline more quickly by examining the modular structure.

---
**文档版本**: v1.0
**最后更新**: 2025-07-30
**负责人**: AI Assistant