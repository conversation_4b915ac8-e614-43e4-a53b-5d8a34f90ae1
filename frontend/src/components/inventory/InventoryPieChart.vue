<template>
  <BaseCard title="库存占比分析" :loading="loading" :error="error">
    <template #extra>
      <div class="chart-controls">
        <select v-model="displayLimit" @change="updateLimit" class="limit-selector">
          <option value="8">TOP8</option>
          <option value="10">TOP10</option>
          <option value="12">TOP12</option>
        </select>
        
        <BaseButton
          @click="toggleChartType"
          variant="ghost"
          size="small"
        >
          {{ chartType === 'pie' ? '环形图' : '饼图' }}
        </BaseButton>
      </div>
    </template>

    <PieChart
      :data="chartData"
      name-field="name"
      value-field="value"
      :loading="loading"
      :height="height"
      :radius="chartRadius"
      :colors="chartColors"
      :label-formatter="labelFormatter"
      :tooltip-formatter="tooltipFormatter"
      @chart-ready="onChartReady"
      @chart-click="onChartClick"
    />

    <div class="pie-summary">
      <div class="summary-item">
        <span class="label">显示产品：</span>
        <span class="value">{{ chartData.length }}种</span>
      </div>
      <div class="summary-item">
        <span class="label">总库存量：</span>
        <span class="value">{{ formatInventory(totalDisplayed) }}</span>
      </div>
      <div class="summary-item">
        <span class="label">占比总和：</span>
        <span class="value">{{ formatPercentage(totalPercentage) }}</span>
      </div>
      <div class="summary-item">
        <span class="label">最大占比：</span>
        <span class="value">{{ formatPercentage(maxPercentage) }}</span>
      </div>
    </div>
  </BaseCard>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useInventoryStore } from '@/stores/inventory'
import { formatInventory, formatPercentage } from '@/utils/formatters'
import { CHART_COLORS } from '@/utils/constants'
import BaseCard from '@/components/common/BaseCard.vue'
import BaseButton from '@/components/common/BaseButton.vue'
import PieChart from '@/components/charts/PieChart.vue'

const props = defineProps({
  date: {
    type: String,
    default: '2025-07-26'
  },
  height: {
    type: String,
    default: '400px'
  }
})

const emit = defineEmits(['chart-ready', 'chart-click', 'data-updated'])

const inventoryStore = useInventoryStore()
const loading = ref(false)
const error = ref('')
const displayLimit = ref(10)
const chartType = ref('pie') // 'pie' or 'doughnut'

// 计算属性
const chartData = computed(() => {
  let data = [...inventoryStore.top15Data]
  
  // 排序并限制显示数量
  data.sort((a, b) => b.value - a.value)
  data = data.slice(0, displayLimit.value)
  
  // 计算百分比
  const total = inventoryStore.totalInventory
  
  return data.map(item => ({
    name: item.name,
    value: item.value,
    percentage: total > 0 ? (item.value / total) * 100 : 0
  }))
})

const chartRadius = computed(() => {
  return chartType.value === 'pie' ? ['0%', '70%'] : ['40%', '70%']
})

const chartColors = computed(() => [
  CHART_COLORS.PRIMARY,
  CHART_COLORS.SECONDARY,
  CHART_COLORS.ACCENT,
  CHART_COLORS.SUCCESS,
  CHART_COLORS.WARNING,
  '#8B5CF6',
  '#06B6D4',
  '#84CC16',
  '#F59E0B',
  '#EF4444',
  '#8B5A2B',
  '#6B7280'
])

const totalDisplayed = computed(() => {
  return chartData.value.reduce((sum, item) => sum + item.value, 0)
})

const totalPercentage = computed(() => {
  return chartData.value.reduce((sum, item) => sum + item.percentage, 0)
})

const maxPercentage = computed(() => {
  if (chartData.value.length === 0) return 0
  return Math.max(...chartData.value.map(item => item.percentage))
})

// 格式化函数
const labelFormatter = (params) => {
  return `${params.name}: ${formatInventory(params.value)} (${params.percent}%)`
}

const tooltipFormatter = (params) => {
  return `${params.name}<br/>库存量: ${formatInventory(params.value)}<br/>占比: ${params.percent}%`
}

// 方法
const fetchData = async () => {
  loading.value = true
  error.value = ''
  
  try {
    await inventoryStore.fetchInventoryTop15(props.date, 15)
    emit('data-updated', inventoryStore.top15Data)
  } catch (err) {
    error.value = '数据加载失败'
    console.error('Failed to load inventory pie data:', err)
  } finally {
    loading.value = false
  }
}

const updateLimit = () => {
  console.log('Display limit updated to:', displayLimit.value)
}

const toggleChartType = () => {
  chartType.value = chartType.value === 'pie' ? 'doughnut' : 'pie'
  console.log('Chart type toggled to:', chartType.value)
}

const onChartReady = (chart) => {
  emit('chart-ready', chart)
}

const onChartClick = (params) => {
  emit('chart-click', params)
}

// 监听日期变化
watch(() => props.date, () => {
  fetchData()
})

onMounted(() => {
  fetchData()
})

// 暴露方法
defineExpose({
  refresh: fetchData
})
</script>

<style scoped>
.chart-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.limit-selector {
  padding: 6px 12px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.9em;
  background: white;
  cursor: pointer;
}

.limit-selector:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.pie-summary {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  padding: 16px;
  background: var(--background-light);
  border-radius: 8px;
  border-top: 1px solid #f0f0f0;
}

.summary-item {
  text-align: center;
  flex: 1;
}

.label {
  display: block;
  color: var(--text-secondary);
  font-size: 0.85em;
  margin-bottom: 4px;
}

.value {
  display: block;
  color: var(--primary-blue);
  font-weight: 600;
  font-size: 1em;
}

@media (max-width: 768px) {
  .chart-controls {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .pie-summary {
    flex-direction: column;
    gap: 12px;
  }
  
  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .label, .value {
    display: inline;
    margin: 0;
  }
}
</style>
