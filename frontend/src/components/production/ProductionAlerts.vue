<template>
  <div v-if="hasAlerts" class="production-alerts">
    <div v-if="consistencyWarning" class="alert alert-warning">
      <div class="alert-icon">⚠️</div>
      <div class="alert-content">
        <div class="alert-title">{{ consistencyWarning.message }}</div>
        <div class="alert-details">{{ consistencyWarning.details }}</div>
        <div class="alert-timestamp">检测时间: {{ formatTimestamp(consistencyWarning.timestamp) }}</div>
        <div class="alert-actions">
          <BaseButton @click="dismissWarning" variant="ghost" size="small">忽略</BaseButton>
          <BaseButton @click="refreshData" variant="primary" size="small">刷新数据</BaseButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useProductionStore } from '@/stores/production'
import BaseButton from '@/components/common/BaseButton.vue'

const productionStore = useProductionStore()

// 获取一致性警告
const consistencyWarning = computed(() => productionStore.consistencyWarning)

// 是否有任何警告需要显示
const hasAlerts = computed(() => consistencyWarning.value !== null)

// 格式化时间戳
const formatTimestamp = (timestamp) => {
  if (!timestamp) return '';
  
  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

// 忽略一致性警告
const dismissWarning = () => {
  if (productionStore.consistencyWarning) {
    console.log('用户忽略了数据一致性警告:', productionStore.consistencyWarning);
    productionStore.consistencyWarning = null;
  }
}

// 刷新数据
const refreshData = async () => {
  try {
    await productionStore.refreshData();
    console.log('数据已刷新');
  } catch (err) {
    console.error('刷新数据失败:', err);
  }
}
</script>

<style scoped>
.production-alerts {
  margin-bottom: 20px;
}

.alert {
  display: flex;
  padding: 16px;
  border-radius: 8px;
  align-items: flex-start;
}

.alert-warning {
  background-color: rgba(255, 204, 0, 0.1);
  border: 1px solid rgba(255, 204, 0, 0.5);
}

.alert-icon {
  font-size: 24px;
  margin-right: 16px;
  flex-shrink: 0;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 600;
  color: #d97706;
  margin-bottom: 4px;
}

.alert-details {
  color: #78350f;
  margin-bottom: 8px;
}

.alert-timestamp {
  font-size: 0.85em;
  color: #92400e;
  margin-bottom: 12px;
}

.alert-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .alert {
    flex-direction: column;
  }
  
  .alert-icon {
    margin-bottom: 8px;
  }
  
  .alert-actions {
    flex-direction: column;
    width: 100%;
  }
}
</style>