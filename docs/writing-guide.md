# 文档写作指南

本指南旨在为项目的所有 Markdown 文档提供一套统一的格式化标准，以确保一致性、可读性和可维护性。

## 1. 标题层级

为了保持文档结构的清晰和一致，请遵循以下标题层级规范：

-   `#` **主标题**：每个文档应有且仅有一个一级标题，用于表示文档的中心主题。
-   `##` **章节标题**：用于划分文档的主要部分。
-   `###` **子章节标题**：用于进一步细分章节内容。
-   `####` **及更低层级标题**：根据需要使用，但建议保持结构扁平化，避免过深的嵌套。

## 2. 代码块

所有代码块都必须明确指定语言标识，以确保正确的语法高亮。

**正确示例：**

```typescript
// TypeScript 示例
function greet(name: string): string {
  return `Hello, ${name}!`;
}
```

```python
# Python 示例
def add(a, b):
  return a + b
```

```bash
# Bash 示例
npm install
```

**错误示例：**

````
# 缺少语言标识
echo "这是一个没有语言标识的代码块"
````

## 3. 列表

### 无序列表

使用 `-` 或 `*` 创建无序列表，并保持缩进一致。

-   项目一
-   项目二
    -   子项目 A
    -   子项目 B

### 有序列表

使用 `1.`、`2.` 等创建有序列表。

1.  第一步
2.  第二步
3.  第三步

## 4. 表格

表格应使用标准的 Markdown 语法创建，并确保表头和对齐方式清晰。

| 表头 1 | 表头 2 | 居中对齐 | 右对齐 |
| :--- | :--- | :---: | ---: |
| 内容 A | 内容 B |   C   |    D |
| 内容 E | 内容 F |   G   |    H |

## 5. 术语和风格

-   **一致性**：在所有文档中，请使用统一的术语。例如，统一使用“后端”而非“后台”，“用户界面”而非“UI”。
-   **简洁性**：句子应简明扼要，避免冗长和复杂的表达。
-   **链接**：内部链接应使用相对路径。


---
**文档版本**: v1.0
**最后更新**: 2025-07-30
**负责人**: AI Assistant
