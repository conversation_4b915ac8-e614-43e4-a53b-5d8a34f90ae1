/**
 * 性能监控工具
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = new Map()
    this.observers = []
    this.isSupported = typeof performance !== 'undefined'
  }

  /**
   * 开始性能测量
   * @param {string} name 测量名称
   */
  start(name) {
    if (!this.isSupported) return

    const startTime = performance.now()
    this.metrics.set(name, { startTime, endTime: null, duration: null })
    
    console.log(`🚀 Performance: Started measuring "${name}"`)
  }

  /**
   * 结束性能测量
   * @param {string} name 测量名称
   */
  end(name) {
    if (!this.isSupported) return

    const metric = this.metrics.get(name)
    if (!metric) {
      console.warn(`⚠️ Performance: No measurement found for "${name}"`)
      return
    }

    const endTime = performance.now()
    const duration = endTime - metric.startTime

    metric.endTime = endTime
    metric.duration = duration

    console.log(`✅ Performance: "${name}" took ${duration.toFixed(2)}ms`)
    
    return duration
  }

  /**
   * 获取测量结果
   * @param {string} name 测量名称
   */
  getMetric(name) {
    return this.metrics.get(name)
  }

  /**
   * 获取所有测量结果
   */
  getAllMetrics() {
    const results = {}
    for (const [name, metric] of this.metrics) {
      results[name] = {
        duration: metric.duration,
        startTime: metric.startTime,
        endTime: metric.endTime
      }
    }
    return results
  }

  /**
   * 清除所有测量结果
   */
  clear() {
    this.metrics.clear()
    console.log('🧹 Performance: Cleared all metrics')
  }

  /**
   * 监控页面加载性能
   */
  measurePageLoad() {
    if (!this.isSupported || !performance.timing) return null

    const timing = performance.timing
    const metrics = {
      // DNS查询时间
      dnsLookup: timing.domainLookupEnd - timing.domainLookupStart,
      // TCP连接时间
      tcpConnect: timing.connectEnd - timing.connectStart,
      // 请求响应时间
      request: timing.responseEnd - timing.requestStart,
      // DOM解析时间
      domParse: timing.domContentLoadedEventEnd - timing.domLoading,
      // 页面完全加载时间
      pageLoad: timing.loadEventEnd - timing.navigationStart,
      // 首次渲染时间
      firstPaint: this.getFirstPaint(),
      // 首次内容渲染时间
      firstContentfulPaint: this.getFirstContentfulPaint()
    }

    console.log('📊 Page Load Performance:', metrics)
    return metrics
  }

  /**
   * 获取首次渲染时间
   */
  getFirstPaint() {
    if (!performance.getEntriesByType) return null

    const paintEntries = performance.getEntriesByType('paint')
    const firstPaint = paintEntries.find(entry => entry.name === 'first-paint')
    return firstPaint ? firstPaint.startTime : null
  }

  /**
   * 获取首次内容渲染时间
   */
  getFirstContentfulPaint() {
    if (!performance.getEntriesByType) return null

    const paintEntries = performance.getEntriesByType('paint')
    const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint')
    return fcp ? fcp.startTime : null
  }

  /**
   * 监控内存使用情况
   */
  measureMemoryUsage() {
    if (!performance.memory) return null

    const memory = {
      used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024), // MB
      total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024), // MB
      limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024) // MB
    }

    console.log('💾 Memory Usage:', memory)
    return memory
  }

  /**
   * 监控长任务
   */
  observeLongTasks() {
    if (!window.PerformanceObserver) return

    try {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          console.warn(`⚠️ Long Task detected: ${entry.duration.toFixed(2)}ms`, entry)
        }
      })

      observer.observe({ entryTypes: ['longtask'] })
      this.observers.push(observer)
    } catch (error) {
      console.warn('Long task observation not supported:', error)
    }
  }

  /**
   * 监控资源加载性能
   */
  measureResourcePerformance() {
    if (!performance.getEntriesByType) return []

    const resources = performance.getEntriesByType('resource')
    const resourceMetrics = resources.map(resource => ({
      name: resource.name,
      type: this.getResourceType(resource.name),
      duration: resource.duration,
      size: resource.transferSize || 0,
      startTime: resource.startTime
    }))

    console.log('📦 Resource Performance:', resourceMetrics)
    return resourceMetrics
  }

  /**
   * 获取资源类型
   */
  getResourceType(url) {
    if (url.includes('.js')) return 'script'
    if (url.includes('.css')) return 'stylesheet'
    if (url.includes('.png') || url.includes('.jpg') || url.includes('.svg')) return 'image'
    if (url.includes('.woff') || url.includes('.ttf')) return 'font'
    return 'other'
  }

  /**
   * 生成性能报告
   */
  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      pageLoad: this.measurePageLoad(),
      memory: this.measureMemoryUsage(),
      resources: this.measureResourcePerformance(),
      customMetrics: this.getAllMetrics(),
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      }
    }

    console.log('📋 Performance Report:', report)
    return report
  }

  /**
   * 断开所有观察器
   */
  disconnect() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
  }
}

// 创建全局实例
const performanceMonitor = new PerformanceMonitor()

// 自动监控长任务
if (typeof window !== 'undefined') {
  performanceMonitor.observeLongTasks()
}

export default performanceMonitor

// 便捷方法
export const startMeasure = (name) => performanceMonitor.start(name)
export const endMeasure = (name) => performanceMonitor.end(name)
export const measurePageLoad = () => performanceMonitor.measurePageLoad()
export const measureMemory = () => performanceMonitor.measureMemoryUsage()
export const generatePerformanceReport = () => performanceMonitor.generateReport()

/**
 * Vue组合式函数：性能监控
 */
export function usePerformance() {
  const startMeasurement = (name) => {
    performanceMonitor.start(name)
  }

  const endMeasurement = (name) => {
    return performanceMonitor.end(name)
  }

  const getMetrics = () => {
    return performanceMonitor.getAllMetrics()
  }

  const generateReport = () => {
    return performanceMonitor.generateReport()
  }

  return {
    startMeasurement,
    endMeasurement,
    getMetrics,
    generateReport
  }
}
