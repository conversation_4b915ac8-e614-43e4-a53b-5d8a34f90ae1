# 春雪食品产销分析系统架构设计文档

## 1. 系统概述

春雪食品产销分析系统是一个基于 Cloudflare 无服务器平台构建的现代化全栈数据分析应用。系统将传统的 Excel-Python 静态报表分析转换为实时、交互式的 Web 数据分析平台，为春雪食品提供生产、销售、库存和价格的多维度分析能力。

### 1.1. 业务背景

- **数据周期**: 2025-06-01 至 2025-06-26（26天运营数据）
- **产品覆盖**: 300+ 产品总数，254个非鲜品用于价格监控
- **核心指标**: 产量、销量、库存水平、平均价格、产销率
- **数据来源**: 生产记录、销售发票、库存汇总、调价表等Excel文件

### 1.2. 架构目标

- **高性能与可伸缩性**: 利用 Cloudflare Workers 边缘计算提供低延迟 API 服务，D1 数据库实现全球数据访问
- **数据一致性**: 统一的产品过滤逻辑，确保前后端数据计算的一致性
- **可维护性**: 前后端分离设计，TypeScript 统一技术栈，模块化代码结构
- **自动化运维**: 基于 Cloudflare Pages 和 Wrangler CLI 的全流程自动化部署

## 2. 系统架构

系统采用现代化的无服务器全栈架构，完全构建在 Cloudflare 生态系统之上，结合 Python 数据导入系统实现完整的数据处理流程。

```mermaid
graph TD
    subgraph 用户层
        A[浏览器客户端]
    end

    subgraph Cloudflare 边缘网络
        B[Cloudflare Pages<br/>Vue.js 3 SPA]
        C[Cloudflare Workers<br/>Hono API 服务]
        D[Cloudflare D1<br/>SQLite 数据库]
    end

    subgraph 数据导入系统
        E[Python 导入脚本]
        F[Excel 文件源]
        G[数据处理模块]
    end

    subgraph 核心业务模块
        H[产销率计算器]
        I[产品过滤器]
        J[价格监控引擎]
    end

    A -- HTTPS --> B
    B -- API 调用 --> C
    C -- SQL 查询 --> D
    F -- 批量导入 --> E
    E -- 数据处理 --> G
    G -- 写入数据 --> D
    C -- 业务逻辑 --> H
    C -- 数据过滤 --> I
    C -- 价格分析 --> J
```

### 2.1. 技术栈详解

#### 前端层 (Vue.js 3 SPA)
- **核心框架**: Vue.js ^3.4.0 (Composition API)
- **构建工具**: Vite ^5.0.0 (热重载、代码分割、TypeScript 支持)
- **状态管理**: Pinia ^2.1.7 (6个模块化 stores)
- **路由管理**: Vue Router ^4.2.5 (懒加载、路由守卫)
- **数据可视化**: ECharts ^5.6.0 (图表组件)
- **HTTP 客户端**: Axios ^1.6.2 (请求拦截器、错误处理)
- **日期处理**: Day.js ^1.11.10 (轻量级日期库)
- **样式方案**: CSS Grid + Flexbox (响应式设计)
- **包管理器**: npm (使用 `npm ci` 确保一致安装)

#### 开发工具链 (前端)
- **TypeScript**: ^5.8.3 (类型安全)
- **测试框架**: Vitest ^1.0.0 (单元测试、覆盖率)
- **代码检查**: ESLint ^8.57.0 (Vue 插件支持)
- **代码格式化**: Prettier ^3.1.0 (统一代码风格)
- **测试工具**: @vue/test-utils ^2.4.0, jsdom ^26.1.0

#### 后端层 (Cloudflare Workers)
- **运行时**: Cloudflare Workers (V8 隔离环境)
- **Web 框架**: Hono ^4.8.3 (TypeScript 优先、边缘优化)
- **数据库**: Cloudflare D1 (SQLite 兼容、无服务器)
- **身份认证**: @tsndr/cloudflare-worker-jwt ^3.2.0 (完整 JWT 实现)
- **密码加密**: bcryptjs ^3.0.2 (密码哈希)
- **文件处理**: xlsx ^0.18.5 (Excel 解析)
- **CORS 处理**: cors ^2.8.5 (跨域请求支持)
- **ORM 支持**: Drizzle ORM ^0.44.2 (类型安全的数据库操作)

#### 后端开发工具
- **TypeScript**: ^5.5.2 (类型定义)
- **测试框架**: Vitest ~3.2.0 (Workers 环境测试)
- **部署工具**: Wrangler ^4.21.0 (开发和部署)
- **类型生成**: @cloudflare/workers-types ^4.20250711.0

#### 数据导入系统 (Python)
- **核心语言**: Python 3.x
- **数据处理**: pandas (Excel 读取、数据清洗)
- **数据库交互**: sqlite3 + subprocess (D1 CLI 调用)
- **配置管理**: 模块化配置系统
- **批处理优化**: 动态批次大小、重试机制
- **数据验证**: 多层过滤逻辑、业务规则验证

#### 数据库层 (Cloudflare D1)
- **类型**: Cloudflare D1 (SQLite 兼容、无服务器)
- **Schema**: 规范化设计，Products 和 DailyMetrics 主表
- **索引策略**: 针对日期和 product_id 查询优化
- **迁移管理**: 通过 `schema.sql` 和 Wrangler CLI

## 3. 核心业务流程

### 3.1. 数据导入流程 (Python 系统)
1. **Excel 文件准备**: 将生产、销售、库存、调价等 Excel 文件放入 `Excel文件夹/`
2. **数据处理**: Python 脚本读取 Excel 文件，应用业务过滤规则
   - 库存数据: 排除鲜品（保留凤肠），排除副产品和空白分类
   - 销售数据: 包含鲜品，仅排除副产品和空白分类
3. **数据验证**: 产品名称一致性检查，数据范围验证
4. **批量写入**: 使用优化的批处理策略写入 D1 数据库
5. **结果验证**: 数据完整性检查和统计报告

### 3.2. 用户认证流程
1. **注册**: 用户输入用户名、密码和邀请码 `SPRING2025`
2. **验证**: 后端 Worker 验证邀请码，存储用户信息到 `Users` 表
3. **登录**: 用户名密码验证，返回模拟会话令牌
4. **会话管理**: 前端本地存储令牌，API 请求携带身份验证

### 3.3. 数据查询与展示流程
1. **用户交互**: 前端日期选择器、筛选控件触发查询
2. **API 请求**: Pinia store 通过 Axios 发送请求到 Worker API
3. **业务逻辑处理**: 
   - ProductionRatioCalculator: 统一的产销率计算
   - ProductFilter: 一致的产品过滤逻辑
   - 复杂聚合查询在 SQL 层面执行
4. **数据响应**: JSON 格式返回处理结果
5. **前端渲染**: Pinia 状态更新，Vue 组件响应式渲染，ECharts 图表更新

### 3.4. 产销率计算流程 (核心业务逻辑)
1. **数据获取**: 分别获取销售数据（包含鲜品）和生产数据（排除鲜品）
2. **聚合计算**: 使用 ProductionRatioCalculator 统一计算逻辑
3. **比率计算**: (总销量 / 总产量) × 100，应用 500% 上限裁剪
4. **一致性验证**: 聚合方法与日均方法的结果对比验证
5. **结果返回**: 包含详细计算方法和可追溯信息

## 4. 数据库设计

### 4.1. 核心表结构

#### Products (产品主表)
```sql
CREATE TABLE Products (
    product_id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_name TEXT NOT NULL UNIQUE,
    sku TEXT UNIQUE,
    category TEXT
);
```
- **用途**: 存储所有产品的静态信息
- **关键字段**: product_name 必须唯一，用于关联其他表

#### DailyMetrics (每日指标表)
```sql
CREATE TABLE DailyMetrics (
    record_id INTEGER PRIMARY KEY AUTOINCREMENT,
    record_date TEXT NOT NULL,
    product_id INTEGER NOT NULL,
    production_volume REAL,        -- 产量(kg)
    sales_volume REAL,            -- 销量(kg)
    sales_amount REAL,            -- 销售金额(元)
    inventory_level REAL,         -- 库存水平(kg)
    average_price REAL,           -- 平均价格(元/吨)
    inventory_turnover_days REAL, -- 库存周转天数
    FOREIGN KEY (product_id) REFERENCES Products(product_id),
    UNIQUE(record_date, product_id)
);
```
- **用途**: 记录每个产品每天的产销存数据
- **数据单位**: 重量使用 kg，价格使用 元/吨

#### PriceAdjustments (价格调整表)
```sql
CREATE TABLE PriceAdjustments (
    adjustment_id INTEGER PRIMARY KEY AUTOINCREMENT,
    adjustment_date TEXT NOT NULL,
    product_id INTEGER NOT NULL,
    product_name TEXT NOT NULL,
    specification TEXT,
    adjustment_count INTEGER DEFAULT 1,
    previous_price REAL,
    current_price REAL NOT NULL,
    price_difference REAL NOT NULL,
    category TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES Products(product_id)
);
```
- **用途**: 存储产品价格变动历史
- **特殊字段**: adjustment_count 记录当日调价次数

#### Users (用户表)
```sql
CREATE TABLE Users (
    user_id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);
```
- **用途**: 用户认证信息存储

### 4.2. 索引优化策略

#### 高频查询索引
```sql
-- DailyMetrics 表索引
CREATE INDEX idx_dailymetrics_date ON DailyMetrics(record_date);
CREATE INDEX idx_dailymetrics_product_id ON DailyMetrics(product_id);

-- PriceAdjustments 表索引
CREATE INDEX idx_priceadjustments_date ON PriceAdjustments(adjustment_date);
CREATE INDEX idx_priceadjustments_product_id ON PriceAdjustments(product_id);
CREATE INDEX idx_priceadjustments_price_diff ON PriceAdjustments(price_difference);
```

#### 查询性能优化
- **日期范围查询**: 针对 record_date 和 adjustment_date 建立索引
- **产品关联查询**: product_id 外键索引提升 JOIN 性能
- **价格分析查询**: price_difference 索引支持价格波动分析

### 4.3. 数据完整性约束

- **唯一性约束**: Products.product_name, Users.username
- **外键约束**: 确保 DailyMetrics 和 PriceAdjustments 的 product_id 有效
- **复合唯一约束**: DailyMetrics(record_date, product_id) 防止重复记录

## 5. API 设计

### 5.1. API 架构模式

系统采用 RESTful API 设计，基于 Hono 框架构建，按功能模块组织路由结构。

#### 核心路由模块
```typescript
// 系统健康检查和监控
GET /health                                    // 系统健康状态
GET /api/system/date-range                     // 数据库可用日期范围

// 仪表盘数据
GET /api/dashboard/summary                     // 仪表盘汇总数据

// 库存分析
GET /api/inventory/top                         // 库存排行榜
GET /api/inventory/summary                     // 库存汇总统计
GET /api/inventory/total-summary               // 库存总体汇总

// 产销分析和一致性验证
GET /api/production/validate-consistency       // 产销率一致性验证
GET /api/monitoring/production-consistency     // 生产数据一致性监控
GET /api/trends/ratio                          // 产销率趋势数据

// 价格监控
GET /api/prices/key-products                   // 重点产品价格监控
GET /api/prices/trends                         // 价格趋势分析
GET /api/prices/daily-drops                    // 日度价格下跌分析

// 用户认证
POST /api/register                             // 用户注册 (邀请码: SPRING2025)
POST /api/login                                // 用户登录

// 产品管理
GET /api/products                              // 获取所有产品列表

// 销售分析
GET /api/sales/summary                         // 销售汇总数据
GET /api/sales/trends                          // 销售趋势分析
```

### 5.2. 核心业务 API

#### 仪表盘汇总 API
```typescript
GET /api/dashboard/summary?start_date=2025-06-01&end_date=2025-06-26

Response: {
  totalSales: number,      // 总销量(吨)
  totalProduction: number, // 总产量(吨)
  totalSalesAmount: number,// 总销售额(元)
  totalProducts: number,   // 产品数量
  avgRatio: number,        // 平均产销率(%)
  days: number            // 统计天数
}
```

#### 产销率验证 API
```typescript
GET /api/production/validate-consistency?start_date=2025-06-01&end_date=2025-06-26

Response: {
  isConsistent: boolean,
  aggregateRatio: number,
  dailyAverageRatio: number,
  difference: number,
  details: {
    dateRange: { start: string, end: string },
    totalSales: number,
    totalProduction: number,
    calculationTimestamp: string
  }
}
```

### 5.3. CORS 配置

```typescript
// 完整的 CORS 配置支持多个开发环境
const ALLOWED_ORIGINS = [
  'http://localhost:5173',
  'http://localhost:3001',
  'http://localhost:3003',
  'http://localhost:3004',
  'http://127.0.0.1:5173',
  'http://localhost:8000',
  'http://localhost:8080',
  'https://my-fullstack-project.pages.dev',
  'https://7bbead58.my-fullstack-project.pages.dev',
  'https://backend.qu18354531302.workers.dev',
  'https://my-auth-worker.qu18354531302.workers.dev'
];

const CORS_HEADERS = {
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Accept, Authorization, X-Requested-With',
  'Access-Control-Expose-Headers': 'Content-Length, X-Requested-With',
  'Access-Control-Max-Age': '86400'
};
```

#### CORS 中间件实现
- **预检请求处理**: 自动处理 OPTIONS 请求
- **动态源验证**: 根据请求源动态设置允许的域名
- **完整头部支持**: 支持自定义请求头和响应头暴露
- **缓存优化**: 24小时预检缓存减少网络开销

### 5.4. 错误处理策略

- **统一错误格式**: `{ error: string, details?: string }`
- **HTTP 状态码**: 标准 RESTful 状态码使用
- **业务异常**: 详细的错误信息和建议解决方案
- **日志记录**: 完整的请求/响应日志用于调试

## 6. 部署架构与环境配置

### 6.1. 开发环境配置

#### 前端开发环境
```bash
cd frontend
npm install
npm run dev              # 启动开发服务器 (端口 5173)
npm run build            # 生产构建
npm run build:staging    # 预发布环境构建
npm run preview          # 预览生产构建
npm run test             # 运行单元测试
npm run test:ui          # 运行测试 UI 界面
npm run test:coverage    # 生成测试覆盖率报告
npm run lint             # 代码检查和修复
npm run format           # 代码格式化
npm run clean            # 清理构建缓存
```

#### 后端开发环境
```bash
cd backend
npm install
npm run dev              # 启动本地 Worker (端口 8787)
npm run start            # 启动开发服务器 (端口 8787)
npm run deploy           # 部署到生产环境
npm run test             # 运行 Vitest 测试
npm run cf-typegen       # 生成 Cloudflare 类型定义
```

#### 数据库操作
```bash
# 执行 SQL 文件
wrangler d1 execute DB --file=schema.sql

# 导入数据
wrangler d1 execute DB --file=import_data.sql

# 查询数据库
wrangler d1 execute DB --command="SELECT COUNT(*) FROM Products"
```

#### 数据导入
```bash
# Python 数据导入 (推荐用于生产环境)
python import_real_data.py

# 注意: 种子脚本仅用于开发环境
# import_inventory_data.ts 等脚本不应在生产环境使用
```

### 6.2. 生产环境部署

#### 部署流程
```bash
# 1. 部署后端 API
cd backend && npm run deploy

# 2. 构建并部署前端
cd frontend && npm run build
# 前端通过 Cloudflare Pages 自动部署

# 3. 验证部署
curl "https://spring-snow-api.qu18354531302.workers.dev/health"
```

#### 环境配置文件

**前端环境配置**
- `.env.development`: 本地开发设置
- `.env.production`: 生产 API 端点
- Vite 自动加载对应环境文件

**后端配置 (wrangler.toml)**
```toml
name = "spring-snow-api"
main = "src/index.ts"
compatibility_date = "2023-06-01"

[vars]
JWT_SECRET = "spring-snow-jwt-secret-key-2024"

[[d1_databases]]
binding = "DB"
database_name = "chunxue-prod-db"
database_id = "0f2a500e-0865-47ac-a6d4-2f4af0051da3"

# 开发环境配置
[env.development]
vars = { NODE_ENV = "development" }

# 生产环境配置  
[env.production]
vars = { NODE_ENV = "production" }
```

**关键配置说明**:
- **数据库绑定**: DB 变量绑定到 chunxue-prod-db 数据库
- **JWT 密钥**: 用于用户认证的密钥配置
- **兼容性日期**: 确保 Workers 运行时特性兼容性
- **环境变量**: 支持开发和生产环境的不同配置

### 6.3. 性能优化配置

#### 前端性能优化
- **代码分割**: 按路由和功能模块分割
- **懒加载**: Vue Router 路由懒加载
- **ECharts 优化**: 图表组件按需加载
- **构建优化**: Vite 生产构建优化

#### 后端性能优化
- **SQL 查询优化**: 合理使用索引和聚合查询
- **批处理优化**: D1 数据库批量操作
- **边缘缓存**: Cloudflare 边缘网络缓存

#### 数据库性能优化
- **索引策略**: 针对高频查询字段建立索引
- **批量操作**: 使用预处理语句和批量插入
- **查询优化**: 在 SQL 层面完成复杂计算

### 6.4. 数据导入系统架构

#### Python 数据导入系统
数据导入系统采用模块化 Python 架构，负责将 Excel 数据处理并导入到 D1 数据库。

**核心模块结构**:
```text
data_import/
├── config.py              # 统一配置管理
├── data_processors.py     # 数据处理和过滤逻辑
├── db_handler.py          # 数据库交互封装
├── data_loader.py         # Excel 文件读取
├── bulk_import_main.py    # 批量导入主程序
└── daily_import_main.py   # 日常导入主程序
```

**关键特性**:
- **智能配置**: 自动检测文件修改时间确定业务日期
- **数据过滤**: 统一的产品过滤逻辑，确保与前端一致
- **批处理优化**: 动态批次大小，充分利用 D1 的 1MB 限制
- **错误处理**: 重试机制和详细的错误日志
- **性能监控**: 实时的导入速度和成功率统计

#### 数据处理流程
```mermaid
graph LR
    A[Excel 文件] --> B[数据读取]
    B --> C[业务过滤]
    C --> D[数据验证]
    D --> E[批量处理]
    E --> F[D1 数据库]
    
    C --> G[库存过滤<br/>排除鲜品]
    C --> H[销售过滤<br/>包含鲜品]
```

#### 配置管理
```python
# 功能开关
USE_D1_DATABASE = True      # 使用 D1 数据库
USE_REMOTE_D1 = True        # 使用远程 D1 (生产环境)

# 性能配置
D1_BATCH_SIZE = 500         # 默认批处理大小
TABLE_BATCH_CONFIGS = {
    'Products': {'batch_size': 800, 'delay': 0.05},
    'DailyMetrics': {'batch_size': 10, 'delay': 0.5},
    'PriceAdjustments': {'batch_size': 200, 'delay': 0.1}
}
```

## 7. 核心组件关系

### 7.1. 业务逻辑组件

#### ProductionRatioCalculator (产销率计算器)
```typescript
class ProductionRatioCalculator {
  private db: D1Database;
  
  constructor(db: D1Database) {
    this.db = db;
  }
  
  // 聚合指标计算 - 获取总销量、总产量、总销售额等
  async calculateAggregateMetrics(startDate: string, endDate: string): Promise<AggregateMetrics>
  
  // 权威产销率计算 - 使用聚合总量方法计算平均产销率
  async calculateAggregateRatio(startDate: string, endDate: string): Promise<number>
  
  // 日度产销率计算 - 计算每日产销率数据
  async calculateDailyRatios(startDate: string, endDate: string): Promise<DailyRatio[]>
  
  // 统计数据计算 - 包含最大值、最小值、平均值等
  async calculateStatistics(startDate: string, endDate: string): Promise<RatioStatistics>
  
  // 一致性验证 - 对比聚合方法与日均方法的计算结果
  async validateConsistency(startDate: string, endDate: string): Promise<ValidationResult>
}

interface AggregateMetrics {
  totalSales: number;        // 总销量 (kg)
  totalProduction: number;   // 总产量 (kg)
  totalSalesAmount: number;  // 总销售额 (元)
  totalProducts: number;     // 产品数量
}

interface DailyRatio {
  date: string;              // 日期
  sales_volume: number;      // 当日销量
  production_volume: number; // 当日产量
  ratio: number;             // 当日产销率 (%)
}
```

**核心特性**:
- **统一计算逻辑**: 确保所有 API 端点使用相同的计算方法
- **数据一致性**: 聚合方法与日均方法的结果对比验证 (允许5%业务差异)
- **业务规则**: 500% 上限裁剪，零产量特殊处理
- **可追溯性**: 详细的计算日志和方法记录，包含计算时间戳
- **错误处理**: 完整的异常捕获和错误信息记录
- **性能优化**: 使用 SQL 聚合查询减少数据传输

#### ProductFilter (产品过滤器)
```typescript
class ProductFilter {
  // 库存数据过滤 (严格 - 排除鲜品，用于库存和生产数据)
  static getInventoryFilter(tableAlias: string = 'p'): string {
    return `(
      -- 排除鲜品，但保留凤肠产品
      (${tableAlias}.product_name NOT LIKE '%鲜%' OR ${tableAlias}.product_name LIKE '%凤肠%')
      AND
      -- 排除副产品
      (${tableAlias}.category IS NULL OR ${tableAlias}.category != '副产品')
    )`;
  }
  
  // 销售数据过滤 (宽松 - 包含鲜品，用于销售数据)
  static getSalesFilter(tableAlias: string = 'p'): string {
    return `(
      -- 销售数据已在导入时过滤，无需额外过滤
      -- 包含鲜品但排除副产品和空白分类
      1 = 1
    )`;
  }
  
  // 完整过滤逻辑 - 根据数据类型选择过滤策略
  static getCompleteFilter(forSales: boolean = true, tableAlias: string = 'p'): string
  
  // 价格调整过滤 - 用于 PriceAdjustments 表
  static getPriceAdjustmentFilter(strict: boolean = false): string
  
  // 向后兼容的遗留方法
  static getProductNameFilter(): string  // 委托给 getInventoryFilter
  static getCategoryFilter(strict: boolean = false): string  // 委托给 getSalesFilter
}
```

**过滤规则详解**:
- **库存/生产数据**: 排除鲜品（保留凤肠），排除副产品和空白分类
- **销售数据**: 包含鲜品，仅排除副产品和空白分类  
- **价格调整数据**: 支持严格和宽松两种过滤模式
- **一致性保证**: Python 导入系统与 TypeScript API 使用相同过滤逻辑
- **向后兼容**: 保留遗留方法接口，内部委托给新的统一方法

**业务逻辑说明**:
- 鲜品识别: 产品名称包含"鲜"字的产品
- 凤肠例外: 虽然名称包含"鲜"，但凤肠产品需要包含在库存分析中
- 副产品排除: category 字段为"副产品"的产品在所有分析中排除
- 空白分类处理: NULL 或空字符串分类根据上下文决定是否包含

### 7.2. 前端组件架构

#### 状态管理 (Pinia Stores)
```javascript
// 6个模块化 stores
stores/
├── auth.js         # 用户认证状态
├── dashboard.js    # 仪表盘汇总数据
├── inventory.js    # 库存分析状态
├── pricing.js      # 价格监控状态
├── production.js   # 产销分析状态
└── sales.js        # 销售分析状态
```

#### 组件层次结构
```text
components/
├── auth/           # 认证组件
├── charts/         # 可复用图表组件
├── common/         # 基础 UI 组件
├── dashboard/      # 仪表盘组件
├── inventory/      # 库存分析组件
├── priceMonitoring/# 价格监控组件
├── pricing/        # 价格分析组件
├── production/     # 产销分析组件
└── sales/          # 销售分析组件
```

#### 工具函数模块
```javascript
utils/
├── api.js          # 统一 API 客户端
├── formatters.js   # 数据格式化
├── charts.js       # 图表配置助手
├── errorHandler.js # 全局错误处理
├── performance.js  # 性能监控
└── validators.js   # 输入验证
```

### 7.3. 数据流架构

#### 请求流程
```mermaid
graph LR
    A[Vue 组件] --> B[Pinia Store]
    B --> C[API 工具]
    C --> D[Hono 路由]
    D --> E[业务逻辑]
    E --> F[D1 数据库]
    F --> E
    E --> D
    D --> C
    C --> B
    B --> A
```

#### 文件处理流程
```mermaid
graph TD
    A[Excel 文件] --> B[Python 导入器]
    B --> C[数据处理]
    C --> D[SQL 语句]
    D --> E[D1 数据库]
    F[在线上传] --> G[Worker SheetJS]
    G --> H[验证]
    H --> E
```

### 7.4. 关键依赖关系

#### 前端核心依赖
- `vue@^3.4.0` - 框架核心
- `pinia@^2.1.7` - 状态管理
- `vue-router@^4.2.5` - 路由管理
- `echarts@^5.6.0` - 图表库
- `axios@^1.6.2` - HTTP 客户端
- `dayjs@^1.11.10` - 日期处理库

#### 前端开发依赖
- `typescript@^5.8.3` - TypeScript 支持
- `vite@^5.0.0` - 构建工具
- `vitest@^1.0.0` - 测试框架
- `eslint@^8.57.0` - 代码检查
- `prettier@^3.1.0` - 代码格式化
- `@vue/test-utils@^2.4.0` - Vue 测试工具

#### 后端核心依赖
- `hono@^4.8.3` - Web 框架
- `@tsndr/cloudflare-worker-jwt@^3.2.0` - JWT 认证
- `bcryptjs@^3.0.2` - 密码加密
- `xlsx@^0.18.5` - Excel 处理
- `cors@^2.8.5` - CORS 处理
- `drizzle-orm@^0.44.2` - ORM 支持
- `itty-router@^5.0.18` - 轻量级路由
- `express@^5.1.0` - Web 框架 (用于特定兼容场景)
- `drizzle-kit@^0.31.4` - Drizzle ORM 迁移工具

#### 后端开发依赖
- `@cloudflare/workers-types@^4.20250711.0` - TypeScript 定义
- `wrangler@^4.21.0` - 开发和部署工具
- `typescript@^5.5.2` - TypeScript 编译器
- `vitest@~3.2.0` - 测试框架
- `@cloudflare/vitest-pool-workers@^0.8.19` - Workers 测试环境

#### 依赖管理策略
- **版本锁定**: 使用 package-lock.json 确保依赖版本一致性
- **安全更新**: 定期更新依赖以修复安全漏洞
- **兼容性测试**: 升级前进行完整的功能测试
- **最小化原则**: 仅引入必要的依赖，避免包体积膨胀

### 7.5. 监控与运维

#### 健康检查
- **API 健康检查**: `/health` 端点监控
- **数据一致性检查**: `/api/production/validate-consistency`
- **系统监控**: `/api/monitoring/production-consistency`

#### 常见问题解决

**CORS 问题**
- 确保后端 CORS 包含开发环境域名
- 检查请求头设置正确
- 验证 OPTIONS 预检请求处理

**数据显示问题**
- 最常见: API 过滤条件过严，返回空数组
- 检查 ProductFilter 类的分类过滤逻辑
- 在调试前端前先验证数据库中数据存在

**构建失败**
- 清理 node_modules 并重新安装依赖
- 检查 Node.js 版本兼容性 (>=18.0.0)
- 验证 Wrangler CLI 版本是否最新


---
**文档版本**: v1.0
**最后更新**: 2025-07-30
**负责人**: AI Assistant
