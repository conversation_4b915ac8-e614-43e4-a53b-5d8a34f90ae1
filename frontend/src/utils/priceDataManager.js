/**
 * Price Data Manager - 价格数据管理器
 * 负责价格数据的获取、聚合、缓存和智能更新
 */

import { apiRequest } from './api.js';

class PriceDataManager {
  constructor() {
    // 缓存配置
    this.cacheConfig = {
      maxAge: 5 * 60 * 1000, // 5分钟缓存
      maxSize: 100, // 最大缓存条目数
    };
    
    // 数据缓存
    this.cache = new Map();
    
    // 批量请求队列
    this.batchQueue = [];
    this.batchTimer = null;
    this.batchDelay = 50; // 50ms延迟批量处理
  }

  /**
   * 获取缓存键
   */
  getCacheKey(endpoint, params) {
    return `${endpoint}:${JSON.stringify(params)}`;
  }

  /**
   * 检查缓存是否有效
   */
  isCacheValid(cacheEntry) {
    if (!cacheEntry) return false;
    return Date.now() - cacheEntry.timestamp < this.cacheConfig.maxAge;
  }

  /**
   * 清理过期缓存
   */
  cleanExpiredCache() {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.cacheConfig.maxAge) {
        this.cache.delete(key);
      }
    }
    
    // 如果缓存太大，删除最旧的条目
    if (this.cache.size > this.cacheConfig.maxSize) {
      const sortedEntries = Array.from(this.cache.entries())
        .sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      const entriesToDelete = sortedEntries.slice(0, this.cache.size - this.cacheConfig.maxSize);
      entriesToDelete.forEach(([key]) => this.cache.delete(key));
    }
  }

  /**
   * 获取价格趋势数据（支持缓存）
   * 增强版：当需要显示日内高低价时，使用aggregate接口获取更准确的数据
   */
  async getPriceTrends(productName, options = {}) {
    const { period = 90, includeHistory = false, specification = null, forceRefresh = false, showDailyHighLow = false } = options;
    
    // 如果需要显示日内高低价且没有指定规格，使用aggregate接口
    if (showDailyHighLow && !specification) {
      const endDate = new Date().toISOString().split('T')[0];
      const startDate = new Date(Date.now() - period * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      
      const aggregateResponse = await this.getAggregatedPrices([productName], {
        startDate,
        endDate,
        groupBySpec: false
      });
      
      if (aggregateResponse.success && aggregateResponse.data[productName]) {
        // 转换aggregate格式到trends格式
        const trendData = aggregateResponse.data[productName].map(item => ({
          adjustment_date: item.adjustment_date,
          current_price: item.daily_close,
          previous_price: item.daily_open,
          price_change: item.daily_change,
          daily_high: item.daily_high,
          daily_low: item.daily_low,
          daily_open: item.daily_open,
          daily_close: item.daily_close,
          adjustment_count: item.adjustment_count
        }));
        
        return {
          success: true,
          data: trendData,
          metadata: {
            product_name: productName,
            period_days: period,
            include_history: includeHistory,
            record_count: trendData.length,
            source: 'aggregate'
          }
        };
      }
    }
    
    // 原有的trends接口逻辑
    const params = { productName, period, includeHistory, specification };
    const cacheKey = this.getCacheKey('/api/prices/trends', params);
    
    // 检查缓存
    if (!forceRefresh) {
      const cachedData = this.cache.get(cacheKey);
      if (this.isCacheValid(cachedData)) {
        return cachedData.data;
      }
    }
    
    try {
      // 设置请求超时和重试
      const response = await this.requestWithRetry('/api/prices/trends', {
        method: 'GET',
        params,
        timeout: 25000, // 25秒超时
        retries: 2
      });
      
      if (response.success) {
        // 数据验证
        if (!this.validateTrendData(response.data)) {
          throw new Error('Invalid data format received from API');
        }
        
        // 数据处理
        const processedData = this.processTrendData(response.data, includeHistory);
        
        // 更新缓存
        this.cache.set(cacheKey, {
          data: { ...response, data: processedData },
          timestamp: Date.now()
        });
        
        this.cleanExpiredCache();
        
        return { ...response, data: processedData };
      }
      
      return response;
    } catch (error) {
      console.error('Failed to fetch price trends:', error);
      
      // 降级策略：返回过期缓存
      const cachedData = this.cache.get(cacheKey);
      if (cachedData) {
        console.warn('Returning expired cache due to fetch error');
        return { ...cachedData.data, isStale: true };
      }
      
      // 最后降级：返回空数据
      return {
        success: false,
        data: [],
        message: error.message || 'Failed to fetch data',
        isStale: false
      };
    }
  }

  /**
   * 处理趋势数据 - 填充缺失日期，计算移动平均等
   */
  processTrendData(data, includeHistory) {
    // 数据验证
    if (!Array.isArray(data)) {
      console.warn('Invalid data format: expected array, got', typeof data);
      return [];
    }
    
    if (data.length === 0) {
      console.info('Empty data array received');
      return [];
    }
    
    // 数据清洗和验证
    const validData = data.filter(item => {
      if (!item || typeof item !== 'object') return false;
      if (!item.adjustment_date) return false;
      if (typeof item.current_price === 'undefined') return false;
      
      // 价格数据类型转换和验证
      const price = parseFloat(item.current_price);
      if (isNaN(price) || price < 0 || price > 100000) return false;
      
      return true;
    });
    
    if (validData.length === 0) {
      console.warn('No valid data after filtering');
      return [];
    }
    
    // 标准化数据格式
    const normalizedData = validData.map(item => ({
      adjustment_date: item.adjustment_date,
      current_price: parseFloat(item.current_price),
      previous_price: item.previous_price ? parseFloat(item.previous_price) : null,
      price_difference: item.price_difference ? parseFloat(item.price_difference) : 0,
      adjustment_count: item.adjustment_count || 1,
      template_source: item.template_source || 1,
      // 添加缺失字段的默认值 - 对于聚合数据，这些字段应该已经存在
      daily_high: item.daily_high ? parseFloat(item.daily_high) : parseFloat(item.current_price),
      daily_low: item.daily_low ? parseFloat(item.daily_low) : parseFloat(item.current_price),
      daily_open: item.daily_open ? parseFloat(item.daily_open) : parseFloat(item.current_price),
      daily_close: item.daily_close ? parseFloat(item.daily_close) : parseFloat(item.current_price)
    }));
    
    // 继续原有处理逻辑
    if (includeHistory) {
      const dailyData = this.aggregateDailyData(normalizedData);
      return Object.values(dailyData);
    }
    
    normalizedData.sort((a, b) => a.adjustment_date.localeCompare(b.adjustment_date));
    const filledData = this.fillMissingDates(normalizedData);
    return this.calculateMovingAverages(filledData);
  }

  /**
   * 聚合每日数据（处理同日多次调价）
   */
  aggregateDailyData(data) {
    const dailyMap = {};
    
    data.forEach(record => {
      const date = record.adjustment_date;
      
      if (!dailyMap[date]) {
        dailyMap[date] = {
          adjustment_date: date,
          prices: [],
          specifications: new Set()
        };
      }
      
      dailyMap[date].prices.push(record.current_price);
      if (record.specification) {
        dailyMap[date].specifications.add(record.specification);
      }
    });
    
    // 计算每日汇总数据
    Object.keys(dailyMap).forEach(date => {
      const dayData = dailyMap[date];
      const prices = dayData.prices;
      
      dailyMap[date] = {
        adjustment_date: date,
        current_price: prices[prices.length - 1], // 最后价格作为收盘价
        daily_high: Math.max(...prices),
        daily_low: Math.min(...prices),
        daily_avg: prices.reduce((a, b) => a + b, 0) / prices.length,
        adjustment_count: prices.length,
        specifications: Array.from(dayData.specifications)
      };
    });
    
    return dailyMap;
  }

  /**
   * 填充缺失的日期
   */
  fillMissingDates(data) {
    if (data.length < 2) return data;
    
    const result = [];
    const startDate = new Date(data[0].adjustment_date);
    const endDate = new Date(data[data.length - 1].adjustment_date);
    
    let currentIndex = 0;
    let lastPrice = data[0].current_price;
    
    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split('T')[0];
      
      if (currentIndex < data.length && data[currentIndex].adjustment_date === dateStr) {
        result.push(data[currentIndex]);
        lastPrice = data[currentIndex].current_price;
        currentIndex++;
      } else {
        // 填充缺失日期，使用前一天的价格
        result.push({
          adjustment_date: dateStr,
          current_price: lastPrice,
          previous_price: lastPrice,
          price_difference: 0,
          is_filled: true
        });
      }
    }
    
    return result;
  }

  /**
   * 计算移动平均线
   */
  calculateMovingAverages(data) {
    const periods = [5, 10, 20]; // MA5, MA10, MA20
    
    return data.map((item, index) => {
      const result = { ...item };
      
      periods.forEach(period => {
        if (index >= period - 1) {
          const sum = data
            .slice(index - period + 1, index + 1)
            .reduce((acc, d) => acc + (d.current_price || 0), 0);
          result[`ma${period}`] = sum / period;
        }
      });
      
      return result;
    });
  }

  /**
   * 批量获取多个产品的聚合数据
   */
  async getAggregatedPrices(products, options = {}) {
    const { startDate, endDate, groupBySpec = false } = options;
    
    const params = {
      products: products.join(','),
      startDate,
      endDate,
      groupBySpec
    };
    
    const cacheKey = this.getCacheKey('/api/prices/aggregate', params);
    
    // 检查缓存
    const cachedData = this.cache.get(cacheKey);
    if (this.isCacheValid(cachedData)) {
      return cachedData.data;
    }
    
    try {
      const response = await apiRequest('/api/prices/aggregate', 'GET', null, params);
      
      if (response.success) {
        // 缓存结果
        this.cache.set(cacheKey, {
          data: response,
          timestamp: Date.now()
        });
        
        this.cleanExpiredCache();
      }
      
      return response;
    } catch (error) {
      console.error('Failed to fetch aggregated prices:', error);
      throw error;
    }
  }

  /**
   * 获取关键产品列表（带智能缓存）
   */
  async getKeyProducts(options = {}) {
    const { days = 90, limit = 20, includeSpecs = false } = options;
    
    const params = { days, limit, includeSpecs };
    const cacheKey = this.getCacheKey('/api/prices/key-products', params);
    
    // 关键产品列表缓存时间更长
    const cachedData = this.cache.get(cacheKey);
    if (cachedData && Date.now() - cachedData.timestamp < 10 * 60 * 1000) { // 10分钟
      return cachedData.data;
    }
    
    try {
      const response = await apiRequest('/api/prices/key-products', 'GET', null, params);
      
      if (response.success) {
        this.cache.set(cacheKey, {
          data: response,
          timestamp: Date.now()
        });
      }
      
      return response;
    } catch (error) {
      console.error('Failed to fetch key products:', error);
      throw error;
    }
  }

  /**
   * 获取价格统计信息
   */
  async getPriceStatistics(productName, days = 30) {
    const params = { productName, days };
    const cacheKey = this.getCacheKey('/api/prices/statistics', params);
    
    const cachedData = this.cache.get(cacheKey);
    if (this.isCacheValid(cachedData)) {
      return cachedData.data;
    }
    
    try {
      const response = await apiRequest('/api/prices/statistics', 'GET', null, params);
      
      if (response.success) {
        this.cache.set(cacheKey, {
          data: response,
          timestamp: Date.now()
        });
      }
      
      return response;
    } catch (error) {
      console.error('Failed to fetch price statistics:', error);
      throw error;
    }
  }

  /**
   * 获取系统预警（实时数据，不缓存）
   */
  async getSystemAlerts(options = {}) {
    const {
      consecutiveDays = 3,
      percentageDrop = 5,
      absoluteDrop = 200,
      checkMultipleAdjustments = true
    } = options;
    
    const params = {
      consecutiveDays,
      percentageDrop,
      absoluteDrop,
      checkMultipleAdjustments
    };
    
    // 系统预警不使用缓存
    return await apiRequest('/api/prices/system-alerts', 'GET', null, params);
  }

  /**
   * 预加载常用数据
   */
  async preloadCommonData() {
    try {
      // 预加载关键产品列表
      await this.getKeyProducts();
      
      // 预加载前5个关键产品的趋势数据
      const keyProductsResponse = await this.getKeyProducts({ limit: 5 });
      if (keyProductsResponse.success && keyProductsResponse.data) {
        const preloadPromises = keyProductsResponse.data
          .slice(0, 5)
          .map(product => this.getPriceTrends(product.product_name, { period: 30 }));
        
        await Promise.all(preloadPromises);
      }
    } catch (error) {
      console.error('Failed to preload data:', error);
    }
  }

  /**
   * 清除所有缓存
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    const now = Date.now();
    let validCount = 0;
    let expiredCount = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp < this.cacheConfig.maxAge) {
        validCount++;
      } else {
        expiredCount++;
      }
    }
    
    return {
      totalSize: this.cache.size,
      validCount,
      expiredCount,
      maxSize: this.cacheConfig.maxSize,
      maxAge: this.cacheConfig.maxAge
    };
  }
  
  /**
   * 请求重试机制
   */
  async requestWithRetry(endpoint, options = {}) {
    const { retries = 2, timeout = 30000, ...requestOptions } = options;
    
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const response = await apiRequest(endpoint, requestOptions.method, requestOptions.data, requestOptions.params);
        return response;
      } catch (error) {
        console.warn(`Request attempt ${attempt + 1} failed:`, error.message);
        
        if (attempt === retries) {
          throw error;
        }
        
        // 指数退避
        const delay = Math.min(1000 * Math.pow(2, attempt), 5000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  /**
   * 数据验证
   */
  validateTrendData(data) {
    if (!Array.isArray(data)) return false;
    if (data.length === 0) return true; // 空数组是有效的
    
    return data.every(item =>
      item &&
      typeof item === 'object' &&
      typeof item.adjustment_date === 'string' &&
      (typeof item.current_price === 'number' || !isNaN(parseFloat(item.current_price)))
    );
  }
}

// 创建单例实例
const priceDataManager = new PriceDataManager();

// 导出管理器
export default priceDataManager;

// 导出便捷方法
export const {
  getPriceTrends,
  getAggregatedPrices,
  getKeyProducts,
  getPriceStatistics,
  getSystemAlerts,
  preloadCommonData,
  clearCache,
  getCacheStats
} = {
  getPriceTrends: priceDataManager.getPriceTrends.bind(priceDataManager),
  getAggregatedPrices: priceDataManager.getAggregatedPrices.bind(priceDataManager),
  getKeyProducts: priceDataManager.getKeyProducts.bind(priceDataManager),
  getPriceStatistics: priceDataManager.getPriceStatistics.bind(priceDataManager),
  getSystemAlerts: priceDataManager.getSystemAlerts.bind(priceDataManager),
  preloadCommonData: priceDataManager.preloadCommonData.bind(priceDataManager),
  clearCache: priceDataManager.clearCache.bind(priceDataManager),
  getCacheStats: priceDataManager.getCacheStats.bind(priceDataManager)
};
