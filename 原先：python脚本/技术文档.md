# Spring Snow Food Analysis System - 技术文档

**版本**: v2.1.0 (2025-07-01)
**状态**: ✅ 生产就绪 - 销售数据API问题已解决

## 重要更新 (v2.1.0)
- ✅ **已解决**: 销售数据API返回null值导致的JavaScript错误
- ✅ **已解决**: D1数据库与SQLite文件不一致问题
- ✅ **已解决**: sales_amount字段缺失和计算错误
- ✅ **已优化**: 前端错误处理和null值检查

## 1. 数据源详细说明

### 1.1 调价表数据
- **文件格式**：Excel文件（.xlsx/.xls）
- **文件路径**：由config.DATA_PATH指定
- **数据结构**：
  - 每个sheet代表一个调价日期
  - 每个sheet包含三个并排的模板（列0-8, 9-17, 18-26）
  - 每个模板包含列：
    - 分类
    - 品名
    - 规格
    - 加工一厂-调幅/前价格/价格
    - 加工二厂-调幅/前价格/价格
- **数据处理**：
  - 提取sheet名称中的日期信息（月、日、调价次数）
  - 合并三个模板的数据
  - 只保留加工二厂的数据
  - 删除包含"均价"或"品名"的行
  - 删除重复记录
  - 转换数值列类型

### 1.2 收发存汇总表数据
- **文件格式**：Excel文件
- **文件路径**：由config.INVENTORY_PATH指定
- **关键列**：
  - 物料名称 → 品名
  - 入库 → 产量
  - 出库 → 销量
  - 结存 → 库存量
- **数据处理**：
  - 排除客户为"副产品"、"鲜品"或空白的记录
  - 排除物料分类为"副产品"、"生鲜品其他"的记录
  - 排除物料名称含"鲜"字的记录
  - 转换数值列类型
  - 按品名排序

### 1.3 销售发票执行数据 (已优化 v2.1.0)
- **文件格式**：Excel文件 (`销售发票执行查询.xlsx`)
- **文件路径**：`./Excel文件夹/销售发票执行查询.xlsx`
- **关键列**：
  - `发票日期` → record_date (销售日期)
  - `物料名称` → product_name (产品名称)
  - `主数量` → sales_volume (销售数量，单位：吨)
  - `本币无税金额` → tax_free_amount (无税金额，用于计算)
  - `物料分类` → category (产品分类)
- **数据处理逻辑**：
  - 转换日期格式为 'YYYY-MM-DD'
  - 排除物料分类为"副产品"的记录
  - 排除鲜品(以'鲜'开头)，但保留'凤肠'类产品
  - 按日期和物料分组聚合数据
  - **销售金额计算**: `sales_amount = tax_free_amount * 1.09` (含税)
  - **平均单价计算**: `average_price = (tax_free_amount / sales_volume) * 1.09 * 1000` (元/吨)
- **重要修复**: 解决了sales_amount字段null值问题，确保API返回完整数据

### 1.4 产成品入库数据
- **文件格式**：Excel文件
- **文件路径**：由config.PRODUCTION_PATH指定
- **关键列**：
  - 入库日期
  - 物料名称
  - 主数量
- **数据处理**：
  - 排除物料名称含"鲜"字的记录
  - 排除物料大类为"副产品"和空白的记录
  - 按日期和物料名称分组汇总
  - 生成每日总产量和分产品产量字典

### 1.5 价格对比数据
- **文件名**：春雪与小明农牧价格对比.xlsx
- **文件路径**：固定路径
- **关键列**：
  - 品名
  - 规格
  - 春雪价格
  - 小明中间价
  - 中间价差
- **数据处理**：
  - 转换数值列类型
  - 计算价格差异

### 1.6 行业价格数据
- **数据来源**：卓创资讯
- **文件格式**：多个Excel文件
- **产品类型**：
  - 鸡苗历史价格.xlsx
  - 毛鸡历史价格.xlsx
  - 板冻大胸历史价格.xlsx
  - 琵琶腿历史价格.xlsx
- **数据处理**：
  - 统一日期和价格列名
  - 处理两种不同的文件格式
  - 计算价格变动
  - 生成价格趋势数据

## 2. 重要技术修复记录 (v2.1.0)

### 2.1 销售数据API JavaScript错误修复

**问题描述**:
前端显示销售数据时出现JavaScript错误：`"Cannot read properties of null (reading 'toLocaleString')"`

**根本原因**:
1. **数据库不一致**: 数据导入脚本写入直接SQLite文件，但后端API通过Wrangler D1读取数据
2. **sales_amount字段缺失**: D1数据库中sales_amount字段为null值
3. **前端错误处理不足**: 未对null值进行适当检查

**解决方案**:
```sql
-- 修复D1数据库中的sales_amount计算
UPDATE DailyMetrics
SET sales_amount = (sales_volume * average_price / 1000)
WHERE sales_volume IS NOT NULL AND average_price IS NOT NULL;
```

**技术要点**:
- 使用Wrangler D1接口确保数据库一致性
- 实现正确的销售金额计算公式
- 添加前端null值检查和错误处理

### 2.2 数据映射公式 (已验证)

**销售数据计算公式**:
```python
# 1. 聚合原始数据
df_grouped = df.groupby(['record_date', 'product_name']).agg({
    'sales_volume': 'sum',      # 主数量求和
    'tax_free_amount': 'sum'    # 本币无税金额求和
}).reset_index()

# 2. 计算平均单价 (元/吨)
df_grouped['average_price'] = (df_grouped['tax_free_amount'] / df_grouped['sales_volume']) * 1.09 * 1000

# 3. 计算销售金额 (含税)
df_grouped['sales_amount'] = df_grouped['tax_free_amount'] * 1.09
```

**API响应格式** (已修复):
```json
{
  "record_date": "2025-06-01",
  "total_sales": 102578.25,      // 销售量(吨)
  "total_amount": 652.*********, // 销售金额(元) - 已修复
  "avg_price": 21.533213442913   // 平均价格(元/吨)
}
```

## 3. 数据处理流程

### 2.1 数据加载阶段
1. 调价表数据加载
   ```python
   all_price_data = loader.load_and_process_price_data()
   ```
   - 检查文件路径
   - 读取所有sheet
   - 预处理每个sheet
   - 合并数据
   - 检查日期连续性

2. 库存数据加载
   ```python
   inventory_data = loader.load_inventory_data()
   ```
   - 基础数据清洗
   - 列名映射
   - 数据类型转换

3. 销售数据加载
   ```python
   sales_data = loader.load_sales_data()
   daily_sales_data = loader.load_daily_sales_data()
   ```
   - 日期格式转换
   - 数据分组统计
   - 生成销售汇总

4. 产量数据加载
   ```python
   daily_production_data = loader.load_daily_production_data()
   ```
   - 数据清洗
   - 按日期和物料分组
   - 生成产量字典

### 2.2 数据分析阶段
1. 价格分析
   ```python
   analyzer.analyze_price_changes()
   ```
   - 检测异常价格变动
   - 检查数据一致性
   - 记录价格冲突

2. 产销比分析
   ```python
   ratio_summary_data = calculate_ratio_summary()
   ```
   - 计算每日产销比
   - 限制最大产销比为500%
   - 生成产销比明细

3. 销售趋势分析
   ```python
   processed_daily_sales = analyzer.process_sales_data()
   ```
   - 计算每日销量
   - 统计销售金额
   - 生成趋势数据

### 2.3 报告生成阶段
1. 首页概览
   - 关键指标汇总
   - 异常情况统计
   - 趋势图表

2. 库存报告
   - 当前库存状态
   - 库存变动趋势

3. 产销比报告
   - 总体产销比趋势
   - 产品级别明细

4. 销售报告
   - 销售趋势分析
   - 综合售价分析

5. 详细数据报告
   - 产品销售详情
   - 每日产销率明细

6. 价格波动报告
   - 价格对比分析
   - 价格调整记录

7. 行业价格报告
   - 行业价格趋势
   - 市场行情分析

## 3. 数据依赖关系

### 3.1 产销比计算
- 依赖数据：
  - 产成品入库数据（产量）
  - 销售发票数据（销量）
- 计算公式：产销比 = 销量 / 产量 × 100%

### 3.2 价格分析
- 依赖数据：
  - 调价表数据
  - 价格对比数据
  - 行业价格数据
- 分析维度：
  - 价格变动趋势
  - 同业对比
  - 市场行情对比

### 3.3 库存分析
- 依赖数据：
  - 收发存汇总表
- 分析维度：
  - 当前库存状态
  - 库存周转情况
  - 库存预警

## 4. 注意事项

### 4.1 数据文件要求
1. 调价表
   - sheet名称必须包含日期信息（月.日）
   - 三个模板必须对齐
   - 数据列不能为空

2. 收发存汇总表
   - 必须包含物料名称、入库、出库、结存列
   - 物料分类必须准确

3. 销售发票数据
   - 发票日期必须有效
   - 物料分类必须准确
   - 数量和金额必须为数值

4. 产成品入库数据
   - 入库日期必须有效
   - 物料名称必须规范
   - 主数量必须为数值

### 4.2 数据处理注意点
1. 日期处理
   - 所有日期统一转换为datetime格式
   - 检查日期连续性
   - 记录缺失日期

2. 数值处理
   - 确保数值列类型正确
   - 处理异常值和空值
   - 限制产销比最大值

3. 数据过滤
   - 统一过滤副产品
   - 统一过滤鲜品
   - 处理空值记录

### 4.3 报告生成注意点
1. 文件路径
   - 确保输出目录存在
   - 检查文件写入权限
   - 处理文件名冲突

2. 数据验证
   - 检查必要数据是否存在
   - 验证数据完整性
   - 处理异常情况

3. 错误处理
   - 记录错误信息
   - 提供友好的错误提示
   - 保持程序稳定运行

## 5. 维护建议

### 5.1 日常维护
1. 数据文件
   - 定期备份数据文件
   - 检查文件格式正确性
   - 更新行业价格数据

2. 系统配置
   - 更新配置文件中的路径
   - 检查依赖包版本
   - 维护日志文件

3. 报告检查
   - 验证报告完整性
   - 检查数据准确性
   - 更新报告样式

### 5.2 异常处理
1. 数据异常
   - 检查数据源是否正确
   - 验证数据格式
   - 修复数据问题

2. 程序异常
   - 查看错误日志
   - 定位问题原因
   - 更新程序代码

3. 报告异常
   - 检查报告生成过程
   - 验证数据计算
   - 修复报告问题 