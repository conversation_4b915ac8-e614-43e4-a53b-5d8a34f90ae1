<template>
  <div id="app">
    <!-- 认证遮罩层 -->
    <AuthModal v-if="!isAuthenticated" />
    
    <!-- 主应用内容 -->
    <div v-else class="main-app">
      <!-- 用户信息栏 -->
      <UserBar />
      
      <!-- 主要内容区域 -->
      <div class="container">
        <!-- 应用头部 -->
        <AppHeader />
        
        <!-- 导航栏 -->
        <AppNavigation />
        
        <!-- 路由视图 -->
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import AuthModal from '@/components/auth/AuthModal.vue'
import UserBar from '@/components/layout/UserBar.vue'
import AppHeader from '@/components/layout/AppHeader.vue'
import AppNavigation from '@/components/layout/AppNavigation.vue'

const authStore = useAuthStore()

const isAuthenticated = computed(() => authStore.isAuthenticated)

onMounted(() => {
  // 检查用户认证状态
  authStore.checkAuth()
})
</script>

<style scoped>
.main-app {
  min-height: 100vh;
  background: #ffffff;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}
</style>
