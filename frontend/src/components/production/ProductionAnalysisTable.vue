<template>
  <BaseCard title="产销率分析明细" :loading="loading" :error="error">
    <template #extra>
      <div class="table-controls">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索日期..."
          class="search-input"
        />
        
        <select v-model="sortBy" @change="updateSort" class="sort-selector">
          <option value="date">按日期排序</option>
          <option value="ratio">按产销率排序</option>
          <option value="production">按产量排序</option>
          <option value="sales">按销量排序</option>
        </select>
        
        <BaseButton
          @click="exportData"
          variant="secondary"
          size="small"
          icon="📊"
        >
          导出数据
        </BaseButton>
      </div>
    </template>

    <div class="table-container">
      <table class="analysis-table">
        <thead>
          <tr>
            <th @click="setSortBy('date')" class="sortable">
              日期
              <span v-if="sortBy === 'date'" class="sort-indicator">
                {{ sortOrder === 'asc' ? '↑' : '↓' }}
              </span>
            </th>
            <th @click="setSortBy('production')" class="sortable">
              产量(吨)
              <span v-if="sortBy === 'production'" class="sort-indicator">
                {{ sortOrder === 'asc' ? '↑' : '↓' }}
              </span>
            </th>
            <th @click="setSortBy('sales')" class="sortable">
              销量(吨)
              <span v-if="sortBy === 'sales'" class="sort-indicator">
                {{ sortOrder === 'asc' ? '↑' : '↓' }}
              </span>
            </th>
            <th @click="setSortBy('ratio')" class="sortable">
              产销率(%)
              <span v-if="sortBy === 'ratio'" class="sort-indicator">
                {{ sortOrder === 'asc' ? '↑' : '↓' }}
              </span>
            </th>
            <th>状态</th>
            <th>趋势</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="filteredData.length === 0">
            <td colspan="6" class="no-data">暂无数据</td>
          </tr>
          <tr
            v-for="(item, index) in paginatedData"
            :key="item.date"
            :class="getRowClass(item.ratio)"
          >
            <td class="date-cell">{{ formatDate(item.date) }}</td>
            <td class="number-cell">{{ formatVolumeForTable(item.production) }}</td>
            <td class="number-cell">{{ formatVolumeForTable(item.sales) }}</td>
            <td class="ratio-cell" :class="getRatioColorClass(item.ratio)">
              {{ formatPercentage(item.ratio) }}
            </td>
            <td class="status-cell">
              <span class="status-badge" :class="getStatusClass(item.ratio)">
                {{ getStatusText(item.ratio) }}
              </span>
            </td>
            <td class="trend-cell">
              <span class="trend-indicator" :class="getTrendClass(index)">
                {{ getTrendIcon(index) }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页控件 -->
    <div class="pagination-container" v-if="totalPages > 1">
      <div class="pagination-info">
        显示 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredData.length) }} 
        条，共 {{ filteredData.length }} 条记录
      </div>
      <div class="pagination-controls">
        <BaseButton
          @click="goToPage(currentPage - 1)"
          :disabled="currentPage === 1"
          variant="ghost"
          size="small"
        >
          上一页
        </BaseButton>
        
        <span class="page-numbers">
          <button
            v-for="page in visiblePages"
            :key="page"
            @click="goToPage(page)"
            :class="['page-button', { active: page === currentPage }]"
          >
            {{ page }}
          </button>
        </span>
        
        <BaseButton
          @click="goToPage(currentPage + 1)"
          :disabled="currentPage === totalPages"
          variant="ghost"
          size="small"
        >
          下一页
        </BaseButton>
      </div>
    </div>

    <!-- 统计摘要 -->
    <div class="table-summary">
      <div class="summary-stats">
        <div class="stat-item">
          <span class="stat-label">平均产销率：</span>
          <span class="stat-value" :class="getRatioColorClass(productionSalesRatio)">
            {{ formatPercentage(productionSalesRatio) }}
          </span>
        </div>
        <div class="stat-item">
          <span class="stat-label">最高产销率：</span>
          <span class="stat-value">{{ formatPercentage(maxRatio) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">最低产销率：</span>
          <span class="stat-value">{{ formatPercentage(minRatio) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">平衡天数：</span>
          <span class="stat-value">{{ balancedDays }}/{{ filteredData.length }} 天</span>
        </div>
      </div>
    </div>
  </BaseCard>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useProductionStore } from '@/stores/production'
import { useDashboardStore } from '@/stores/dashboard'
import { formatInteger, formatPercentage, formatSalesVolume } from '@/utils/formatters'
import BaseCard from '@/components/common/BaseCard.vue'
import BaseButton from '@/components/common/BaseButton.vue'

const props = defineProps({
  startDate: {
    type: String,
    default: '2025-06-01' // 备用默认值，实际值由父组件传入
  },
  endDate: {
    type: String,
    default: '2025-07-29' // 备用默认值，实际值由父组件传入
  }
})

const emit = defineEmits(['export-data'])

const productionStore = useProductionStore()
const { averageRatio: productionSalesRatio } = storeToRefs(productionStore)

const loading = ref(false)
const error = ref('')
const searchQuery = ref('')
const sortBy = ref('date')
const sortOrder = ref('desc')
const currentPage = ref(1)
const pageSize = ref(10)

// 计算属性
const tableData = computed(() => {
  return productionStore.ratioData.map((item, index) => ({
    date: item.date,
    production: item.production_volume || 0,
    sales: item.sales_volume || 0,
    ratio: item.ratio || 0,
    index
  }))
})

const filteredData = computed(() => {
  let filtered = tableData.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(item => 
      item.date.toLowerCase().includes(query)
    )
  }

  // 排序
  filtered.sort((a, b) => {
    let aVal = a[sortBy.value]
    let bVal = b[sortBy.value]
    
    if (sortBy.value === 'date') {
      aVal = new Date(aVal)
      bVal = new Date(bVal)
    }
    
    if (sortOrder.value === 'asc') {
      return aVal > bVal ? 1 : -1
    } else {
      return aVal < bVal ? 1 : -1
    }
  })

  return filtered
})

const totalPages = computed(() => Math.ceil(filteredData.value.length / pageSize.value))

const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredData.value.slice(start, end)
})

const visiblePages = computed(() => {
  const pages = []
  const total = totalPages.value
  const current = currentPage.value
  
  if (total <= 7) {
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    if (current <= 4) {
      for (let i = 1; i <= 5; i++) pages.push(i)
      pages.push('...')
      pages.push(total)
    } else if (current >= total - 3) {
      pages.push(1)
      pages.push('...')
      for (let i = total - 4; i <= total; i++) pages.push(i)
    } else {
      pages.push(1)
      pages.push('...')
      for (let i = current - 1; i <= current + 1; i++) pages.push(i)
      pages.push('...')
      pages.push(total)
    }
  }
  
  return pages
})

// 统计数据
const maxRatio = computed(() => {
  if (filteredData.value.length === 0) return 0
  return Math.max(...filteredData.value.map(item => item.ratio))
})

const minRatio = computed(() => {
  if (filteredData.value.length === 0) return 0
  return Math.min(...filteredData.value.map(item => item.ratio))
})

const balancedDays = computed(() => {
  return filteredData.value.filter(item => item.ratio >= 90 && item.ratio <= 110).length
})

// 方法
const fetchData = async () => {
  // 检查日期参数是否有效
  if (!props.startDate || !props.endDate) {
    console.log('⏳ [ProductionAnalysisTable] Waiting for valid date range...', { start: props.startDate, end: props.endDate })
    return
  }
  
  loading.value = true
  error.value = ''
  
  try {
    console.log('📅 [ProductionAnalysisTable] Fetching data for date range:', { start: props.startDate, end: props.endDate })
    await productionStore.fetchProductionRatioData(props.startDate, props.endDate)
    // --- 诊断日志开始 ---
    console.log('📈 [Table] Raw data from store (productionStore.ratioData):', JSON.stringify(productionStore.ratioData, null, 2));
    // --- 诊断日志结束 ---
  } catch (err) {
    error.value = '数据加载失败'
    console.error('Failed to load production analysis data:', err)
  } finally {
    loading.value = false
  }
}

const setSortBy = (field) => {
  if (sortBy.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortBy.value = field
    sortOrder.value = 'desc'
  }
  currentPage.value = 1
}

const updateSort = () => {
  currentPage.value = 1
}

const goToPage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
  }
}

const exportData = () => {
  emit('export-data', filteredData.value)
}

const formatDate = (dateStr) => {
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

// 专门用于表格中体积数据的格式化函数
const formatVolumeForTable = (value) => {
  if (value === null || value === undefined || isNaN(value)) return '--'
  
  const number = parseFloat(value)
  if (number === 0) return '0'
  
  // 后端API已经返回吨单位的数据，无需转换
  return number.toLocaleString('zh-CN', {
    minimumFractionDigits: 1,
    maximumFractionDigits: 1
  })
}

const getRatioColorClass = (ratio) => {
  // 按照产销率逻辑：>=100%为消费型（绿色），<100%为积压型（红色）
  if (ratio >= 100) return 'trend-positive' // 绿色：消费型
  if (ratio < 100) return 'trend-negative'  // 红色：积压型
  return 'trend-neutral'
}

const getRowClass = (ratio) => {
  // 根据产销率设置行背景色
  if (ratio < 90) return 'row-danger'   // 严重积压
  if (ratio < 100) return 'row-warning' // 轻微积压
  return '' // 正常消费
}

const getStatusClass = (ratio) => {
  if (ratio >= 100) return 'status-balanced' // 消费型：绿色
  if (ratio >= 90) return 'status-surplus'   // 轻微积压：橙色
  return 'status-shortage' // 严重积压：红色
}

const getStatusText = (ratio) => {
  if (ratio >= 100) return '消费型'    // 销量>=产量
  if (ratio >= 90) return '轻微积压'   // 90-100%
  return '严重积压' // <90%
}

const getTrendClass = (index) => {
  if (index === 0) return 'trend-neutral'
  const current = paginatedData.value[index]?.ratio || 0
  const previous = paginatedData.value[index - 1]?.ratio || 0
  
  if (current > previous * 1.05) return 'trend-up'
  if (current < previous * 0.95) return 'trend-down'
  return 'trend-stable'
}

const getTrendIcon = (index) => {
  const trendClass = getTrendClass(index)
  const icons = {
    'trend-up': '↗️',
    'trend-down': '↘️',
    'trend-stable': '➡️',
    'trend-neutral': '—'
  }
  return icons[trendClass] || '—'
}

// 监听props变化
watch([() => props.startDate, () => props.endDate], ([newStart, newEnd]) => {
  // 只有当两个日期都有效时才获取数据
  if (newStart && newEnd) {
    console.log('📅 [ProductionAnalysisTable] Date props changed:', { newStart, newEnd })
    fetchData()
  }
})

// 重置搜索时回到第一页
watch(searchQuery, () => {
  currentPage.value = 1
})

onMounted(() => {
  console.log('🚀 [ProductionAnalysisTable] Component mounted')
  // 尝试初始加载，但如果日期无效会被fetchData内部的检查拦截
  fetchData()
})

// 暴露方法
defineExpose({
  refresh: fetchData,
  exportData
})
</script>

<style scoped>
.table-controls {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.search-input,
.sort-selector {
  padding: 6px 12px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.9em;
}

.search-input:focus,
.sort-selector:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.table-container {
  overflow-x: auto;
  margin: 16px 0;
}

.analysis-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9em;
}

.analysis-table th,
.analysis-table td {
  padding: 12px 8px;
  text-align: left;
  border-bottom: 1px solid var(--border-light);
}

.analysis-table th {
  background: var(--background-light);
  font-weight: 600;
  color: var(--text-primary);
}

.sortable {
  cursor: pointer;
  user-select: none;
  position: relative;
}

.sortable:hover {
  background: var(--background-hover);
}

.sort-indicator {
  margin-left: 4px;
  color: var(--primary-blue);
}

.number-cell,
.ratio-cell {
  text-align: right;
  font-family: 'Monaco', 'Menlo', monospace;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8em;
  font-weight: 500;
}

.status-balanced {
  background: rgba(52, 199, 89, 0.1);
  color: var(--success-green);
}

.status-surplus {
  background: rgba(255, 149, 0, 0.1);
  color: var(--warning-orange);
}

.status-shortage {
  background: rgba(217, 46, 46, 0.1);
  color: var(--signal-red);
}

.trend-indicator {
  font-size: 1.2em;
}

.trend-up {
  color: var(--success-green);
}

.trend-down {
  color: var(--signal-red);
}

.trend-stable,
.trend-neutral {
  color: var(--neutral-gray);
}

.row-warning {
  background: rgba(255, 149, 0, 0.05);
}

.row-danger {
  background: rgba(217, 46, 46, 0.05);
}

.no-data {
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--border-light);
}

.pagination-info {
  color: var(--text-secondary);
  font-size: 0.9em;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.page-button {
  padding: 6px 10px;
  border: 1px solid var(--border-light);
  background: white;
  cursor: pointer;
  border-radius: 4px;
  font-size: 0.9em;
}

.page-button:hover {
  background: var(--background-hover);
}

.page-button.active {
  background: var(--primary-blue);
  color: white;
  border-color: var(--primary-blue);
}

.table-summary {
  margin-top: 16px;
  padding: 16px;
  background: var(--background-light);
  border-radius: 8px;
}

.summary-stats {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 16px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  color: var(--text-secondary);
  font-size: 0.85em;
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-weight: 600;
  font-size: 1em;
}

.trend-positive {
  color: var(--success-green);
}

.trend-negative {
  color: var(--signal-red);
}

@media (max-width: 768px) {
  .table-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .pagination-container {
    flex-direction: column;
    gap: 12px;
  }
  
  .summary-stats {
    flex-direction: column;
    gap: 12px;
  }
  
  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .stat-label,
  .stat-value {
    display: inline;
    margin: 0;
  }
}
</style>
