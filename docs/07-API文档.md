# 春雪食品产销分析系统 API 文档 v2.0

## 1. 概述

本文档详细描述了春雪食品产销分析系统后端 API 接口。系统基于 Cloudflare Workers 和 Hono 框架构建，使用 Cloudflare D1 数据库，提供全面的产销存数据分析和价格监控功能。本文档根据最新的代码实现进行了完全重写，确保了准确性和完整性。

### 1.1. 基础信息

- **生产环境 Base URL**: `https://backend.qu18354531302.workers.dev`
- **开发环境 Base URL**: `http://localhost:8787`
- **认证方式**: 模拟用户认证系统 (大部分端点为公开访问)
- **数据格式**: 所有请求和响应均为 `application/json`
- **字符编码**: `UTF-8`

### 1.2. CORS 配置

系统支持多个源的跨域请求，主要用于本地开发和 Cloudflare Pages 部署。

### 1.3. 响应格式

#### 成功响应
直接返回请求的数据（数组或对象），HTTP 状态码为 200。部分端点会返回包含 `success: true` 和 `data` 字段的结构化响应。

#### 错误响应
返回包含错误信息的 JSON 对象：
```json
{
  "error": "错误描述信息",
  "details": "详细错误信息 (可选)"
}
```
常见的 HTTP 状态码包括 `200`, `400`, `401`, `404`, `409`, `500`。

### 1.4. 核心业务逻辑

- **ProductionRatioCalculator**: 统一负责产销率相关的所有计算，确保指标一致性。
- **ProductFilter**: 集中管理产品筛选逻辑，区分销售和库存/生产数据的不同需求。

---

## 2. API 端点详解

### 2.1. 系统与监控 (System & Monitoring)

#### **GET /health**
- **描述**: 系统健康检查，返回服务状态。
- **认证**: 无
- **响应**:
  ```json
  {
    "status": "healthy",
    "timestamp": "2025-07-30T12:00:00.000Z",
    "version": "1.0.0"
  }
  ```

#### **GET /api/system/date-range**
- **描述**: 获取数据库中 `DailyMetrics` 表的可用日期范围。
- **认证**: 无
- **响应**:
  ```json
  {
    "start_date": "2025-06-01",
    "end_date": "2025-06-30",
    "total_days": 30,
    "source": "database"
  }
  ```
- **注意**: 若无数据，会返回默认的 `fallback` 值。

#### **GET /api/production/validate-consistency**
- **描述**: 验证不同产销率计算方法之间的一致性。
- **认证**: 无
- **查询参数**:
  - `start_date` (string, 必需): 开始日期 `YYYY-MM-DD`
  - `end_date` (string, 必需): 结束日期 `YYYY-MM-DD`
- **响应**: 包含一致性状态、不同方法计算结果及差异。

#### **GET /api/monitoring/production-consistency**
- **描述**: 自动监控过去1天、7天、30天的数据一致性。
- **认证**: 无
- **响应**: 返回 `healthy` 或 `warning` 状态，并附带各时段的详细检查结果。

### 2.2. 用户认证 (Authentication)

#### **POST /api/register**
- **描述**: 用户注册。
- **认证**: 无
- **请求体**:
  ```json
  {
    "username": "testuser",
    "password": "password123",
    "inviteCode": "SPRING2025"
  }
  ```
- **响应**: 成功时返回 `token` 和 `user` 对象。

#### **POST /api/login**
- **描述**: 用户登录。
- **认证**: 无
- **请求体**:
  ```json
  {
    "username": "testuser",
    "password": "password123"
  }
  ```
- **响应**: 成功时返回 `token` 和 `user` 对象。

### 2.3. 核心指标与仪表盘 (Dashboard & Core Metrics)

#### **GET /api/dashboard/summary**
- **描述**: 获取仪表盘核心指标汇总。
- **认证**: 无
- **查询参数**:
  - `start_date` (string, 必需)
  - `end_date` (string, 必需)
- **响应**:
  ```json
  {
    "total_products": 250,
    "days": 30,
    "total_sales": 12000.5, // 吨
    "total_production": 14000.7, // 吨
    "total_sales_amount": 50000000,
    "total_inventory": 3500.2, // 吨
    "average_price": 4166.5,
    "sales_to_production_ratio": 85.71
  }
  ```

#### **GET /api/products**
- **描述**: 获取所有产品的基础信息列表。
- **认证**: 无
- **响应**: `[{ "product_id": 1, "product_name": "鸡大胸", ... }]`

### 2.4. 库存分析 (Inventory Analysis)

#### **GET /api/inventory/top**
- **描述**: 获取指定日期库存量最高的产品排名。
- **认证**: 无
- **查询参数**:
  - `date` 或 `end_date` (string, 必需)
  - `limit` (number, 可选, 默认 15)
- **响应**: `[{ "product_name": "鸡大胸", "inventory_level": 500.5, "percentage": 15.2, "rank": 1, ... }]`

#### **GET /api/inventory/summary**
- **描述**: 获取指定日期范围的库存统计摘要。
- **认证**: 无
- **查询参数**:
  - `date` 或 (`start_date` 和 `end_date`) (string, 必需)
- **响应**: 包含 `total_inventory`, `top15_percentage`, `product_count`, `avg_price`。

#### **GET /api/inventory/total-summary**
- **描述**: 获取未经过滤的总库存信息。
- **认证**: 无
- **查询参数**:
  - `date` 或 `end_date` (string, 必需)
- **响应**: 包含 `total_inventory` 和 `actual_date`。

#### **GET /api/inventory/distribution**
- **描述**: 获取库存分布数据，用于饼图。
- **认证**: 无
- **查询参数**:
  - `date` (string, 必需)
  - `limit` (number, 可选, 默认 15)
- **响应**: `[{ "product_name": "鸡大胸", "inventory_level": 500.5, "percentage": 15.2 }]`

#### **GET /api/inventory/trend**
- **描述**: 获取总库存随时间变化的趋势。
- **认证**: 无
- **查询参数**:
  - `start_date` (string, 必需)
  - `end_date` (string, 必需)
- **响应**: `[{ "record_date": "2025-06-01", "total_inventory": 3500.2, ... }]`

#### **GET /api/inventory/trends**
- **描述**: 获取单个产品的库存水平和周转天数趋势。
- **认证**: 无
- **查询参数**:
  - `start_date` (string, 必需)
  - `end_date` (string, 必需)
  - `product_id` (number, 必需)
- **响应**: `[{ "record_date": "2025-06-01", "inventory_level": 500.5, "inventory_turnover_days": 10.2 }]`

### 2.5. 产销分析 (Production & Sales Analysis)

#### **GET /api/trends/ratio**
- **描述**: 获取每日产销率、销量和产量的趋势数据。
- **认证**: 无
- **查询参数**:
  - `start_date` (string, 必需)
  - `end_date` (string, 必需)
- **响应**: 包含 `daily_data` 数组和权威的 `avg_ratio`。

#### **GET /api/production/ratio-stats**
- **描述**: 获取产销率的详细统计数据（平均、最大、最小等）。
- **认证**: 无
- **查询参数**:
  - `start_date` (string, 必需)
  - `end_date` (string, 必需)
- **响应**: 包含 `avg_ratio`, `min_ratio`, `max_ratio`, `total_sales` 等。

#### **GET /api/trends/sales-price**
- **描述**: 获取每日总销量、总销售额和平均价格的趋势。
- **认证**: 无
- **查询参数**:
  - `start_date` (string, 必需)
  - `end_date` (string, 必需)
- **响应**: `[{ "date": "2025-06-01", "volume": 450.5, "amount": 2000000, "price": 4439.5 }]`

#### **GET /api/sales**
- **描述**: 获取详细的单品销售记录。
- **认证**: 无
- **查询参数**:
  - `start_date` (string, 必需)
  - `end_date` (string, 必需)
- **响应**: `[{ "date": "2025-06-01", "product_name": "鸡大胸", "volume": 50.2, "amount": 220000, "price": 4382.47 }]`

### 2.6. 价格监控 (Price Monitoring)

#### **GET /api/price-changes**
- **描述**: 获取符合条件的价格变动记录。
- **认证**: 无
- **查询参数**:
  - `start_date` (string, 必需)
  - `end_date` (string, 必需)
  - `min_price_diff` (number, 可选, 默认 200)
- **响应**: `[{ "adjustment_date": "2025-06-15", "product_name": "鸡大胸", "price_difference": 300, ... }]`

#### **GET /api/price-trends**
- **描述**: 获取单个或多个产品的价格调整历史。这是一个简化的趋势接口。
- **认证**: 无
- **查询参数**:
  - `start_date` (string, 必需)
  - `end_date` (string, 必需)
  - `product_name` (string, 可选)
- **响应**: `[{ "adjustment_date": "2025-06-01", "current_price": 12500, ... }]`
- **注意**: 推荐使用功能更全面的 `GET /api/pricing/trends` 端点获取详细的价格趋势数据和统计摘要。

#### **GET /api/prices/key-products**
- **描述**: 获取价格变动最频繁或最重要的产品列表。
- **认证**: 无
- **查询参数**:
  - `days` (number, 可选, 默认 90)
  - `limit` (number, 可选, 默认 300)
  - `includeSpecs` (boolean, 可选, 默认 false)
- **响应**: 包含产品列表和 `metadata`，并高亮`is_recommended` 的产品。

#### **GET /api/pricing/trends**
- **描述**: 获取价格调整的综合趋势数据，包含详细列表和统计摘要。
- **认证**: 无
- **查询参数**:
  - `product_id` (number, 可选): 按产品ID筛选
  - `product_name` (string, 可选): 按产品名称模糊搜索
  - `start_date` (string, 可选): 开始日期
  - `end_date` (string, 可选): 结束日期
  - `limit` (number, 可选, 默认 100): 返回记录数
  - `include_alerts` (boolean, 可选): 是否在响应中包含相关预警信息
- **响应**:
  ```json
  {
    "success": true,
    "data": {
      "trends": [
        {
          "adjustment_date": "2025-06-15",
          "product_id": 123,
          "product_name": "鸡大胸",
          "current_price": 12800,
          "price_difference": 300,
          "change_percentage": 2.34
        }
      ],
      "summary": {
        "total_products": 50,
        "date_range": { "start_date": "2025-06-01", "end_date": "2025-06-30" },
        "price_stats": {
          "avg_price": 13500.50,
          "min_price": 8000,
          "max_price": 25000,
          "total_adjustments": 150
        }
      },
      "alerts": []
    }
  }
  ```

#### **GET /api/prices/aggregate**
- **描述**: 获取多个产品的聚合日度价格数据 (OHLC)。
- **认证**: 无
- **查询参数**:
  - `products` (string, 必需, 逗号分隔)
  - `startDate` (string, 可选)
  - `endDate` (string, 可选)
- **响应**: 按产品名称分组的日度价格数据。

#### **GET /api/prices/statistics**
- **描述**: 获取单个产品在指定周期内的详细价格统计。
- **认证**: 无
- **查询参数**:
  - `productName` (string, 必需)
  - `days` (number, 可选, 默认 30)
- **响应**: 包含各规格的统计数据和总体 `summary`。

#### **GET /api/prices/system-alerts**
- **描述**: 检查系统范围内的价格预警，如连续下跌、单日暴跌等。
- **认证**: 无
- **查询参数**:
  - `consecutiveDays` (number, 可选, 默认 3)
  - `percentageDrop` (number, 可选, 默认 5)
  - `absoluteDrop` (number, 可选, 默认 200)
- **响应**: 返回触发的预警列表和 `metadata`。

### 2.7. 数据导入/导出 (Data Import/Export)

#### **POST /api/upload**
- **描述**: 上传并处理通用的 `DailyMetrics` 数据 Excel 文件。
- **认证**: 无
- **请求**: `multipart/form-data`，包含名为 `file` 的文件。
- **响应**:
  ```json
  {
    "message": "Upload successful",
    "processedRows": 100,
    "skippedRows": 5,
    "errors": [
      "Row 10: Invalid product_id"
    ]
  }
  ```
- **注意**: 此接口用于批量导入 `DailyMetrics` 数据，具备行级数据校验和错误报告功能。

#### **POST /api/upload/price-adjustments**
- **描述**: 上传并处理价格调整的 Excel 文件 (`调价表.xlsx`)。
- **认证**: 无
- **请求**: `multipart/form-data`，包含名为 `file` 的文件。
- **响应**: `{ "message": "...", "processedRecords": 150, "errors": [] }`

#### **POST /api/admin/import-batch**
- **描述**: 管理员接口，用于批量导入 `DailyMetrics` 数据。
- **认证**: (需要管理员权限，当前未实现)
- **请求体**: `{ "data": [{...}, {...}] }`
- **响应**: `{ "success": true, "inserted": 100, "total": 100 }`

#### **GET /api/prices/export**
- **描述**: 导出指定产品的价格数据。
- **认证**: 无
- **查询参数**:
  - `format` (string, 必需, `csv`, `json`, `xlsx`)
  - `products` (string, 必需, 逗号分隔)
  - `startDate` (string, 可选)
  - `endDate` (string, 可选)
- **响应**: 相应格式的文件流。

### 2.8. 调试端点 (Debug)

- **GET /api/debug/ratio-data**: 检查产销率计算的原始数据。
- **GET /api/debug/sales-simple**: 简化的无过滤销售查询。
- **GET /api/debug/sales-raw**: 原始销售数据抽样。
- **GET /api/debug/db-test**: 测试数据库连接和基本查询。
- **GET /api/debug/analyze-date**: 分析特定日期的销售数据差异。

---

## 3. 数据模型与过滤

### 3.1. ProductFilter 逻辑

- **库存/生产 (严格)**: 排除名称含 `鲜` 的产品（`凤肠` 除外），并排除 `副产品` 分类。
- **销售 (宽松)**: 包含所有产品，因为数据已在导入时按业务逻辑处理。

### 3.2. 核心数据表

- **DailyMetrics**: 每日各产品的产、销、存、价核心数据。
- **PriceAdjustments**: 每次价格调整的详细记录。
- **Products**: 产品主数据。
- **Users**: 用户信息。
- **PriceAlerts**: 系统生成的预警记录。
- **UserPriceAlerts**: 用户自定义的预警规则。


---
**文档版本**: v1.0
**最后更新**: 2025-07-30
**负责人**: AI Assistant
