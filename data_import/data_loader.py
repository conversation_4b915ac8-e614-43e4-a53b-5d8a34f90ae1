"""
数据加载模块
负责从Excel文件中加载原始数据
"""
import pandas as pd
import os
from .config import (
    INVENTORY_SUMMARY_PATH, PRODUCTION_PATH, SALES_PATH, PRICE_ADJUSTMENT_PATH
)


def load_inventory_data():
    """Load inventory data from Excel file"""
    try:
        print(f"Loading inventory data from: {INVENTORY_SUMMARY_PATH}")
        df_inventory = pd.read_excel(INVENTORY_SUMMARY_PATH)
        print(f"Loaded inventory data: {len(df_inventory)} rows")
        return df_inventory
    except FileNotFoundError:
        print(f"Warning: Inventory file not found at {INVENTORY_SUMMARY_PATH}")
        return pd.DataFrame()
    except Exception as e:
        print(f"Error loading inventory data: {e}")
        return pd.DataFrame()


def load_production_data():
    """Load production data from Excel file"""
    try:
        print(f"Loading production data from: {PRODUCTION_PATH}")
        df_production = pd.read_excel(PRODUCTION_PATH)
        print(f"Loaded production data: {len(df_production)} rows")
        return df_production
    except FileNotFoundError:
        print(f"Warning: Production file not found at {PRODUCTION_PATH}")
        return pd.DataFrame()
    except Exception as e:
        print(f"Error loading production data: {e}")
        return pd.DataFrame()


def load_sales_data():
    """Load sales data from Excel file"""
    try:
        print(f"Loading sales data from: {SALES_PATH}")
        df_sales = pd.read_excel(SALES_PATH)
        print(f"Loaded sales data: {len(df_sales)} rows")
        return df_sales
    except FileNotFoundError:
        print(f"Warning: Sales file not found at {SALES_PATH}")
        return pd.DataFrame()
    except Exception as e:
        print(f"Error loading sales data: {e}")
        return pd.DataFrame()


def load_price_adjustment_data():
    """Load price adjustment data from Excel file"""
    try:
        print(f"Loading price adjustment data from: {PRICE_ADJUSTMENT_PATH}")
        xls_price_adjustments = pd.ExcelFile(PRICE_ADJUSTMENT_PATH)
        print(f"Loaded price adjustment data: {len(xls_price_adjustments.sheet_names)} sheets")
        return xls_price_adjustments
    except FileNotFoundError:
        print(f"Warning: Price adjustment file not found at {PRICE_ADJUSTMENT_PATH}")
        return None
    except Exception as e:
        print(f"Error loading price adjustment data: {e}")
        return None


def load_all_data():
    """Load all Excel data files and return a dictionary of DataFrames"""
    print("=== Loading Excel Data Files ===")
    
    data = {}
    
    # Load each data source
    data['inventory'] = load_inventory_data()
    data['production'] = load_production_data()
    data['sales'] = load_sales_data()
    data['price_adjustments'] = load_price_adjustment_data()
    
    # Summary
    loaded_files = []
    for key, value in data.items():
        if key == 'price_adjustments':
            if value is not None:
                loaded_files.append(f"{key}: {len(value.sheet_names)} sheets")
            else:
                loaded_files.append(f"{key}: None")
        else:
            loaded_files.append(f"{key}: {len(value)} rows")
    
    print("Data loading summary:")
    for summary in loaded_files:
        print(f"  - {summary}")
    
    # Check if any critical files are missing
    missing_files = []
    if data['inventory'].empty:
        missing_files.append('inventory')
    if data['production'].empty:
        missing_files.append('production')
    if data['sales'].empty:
        missing_files.append('sales')
    
    if missing_files:
        print(f"⚠️ Warning: Missing critical data files: {', '.join(missing_files)}")
    else:
        print("✅ All critical Excel files loaded successfully")
    
    return data


def validate_data_structure(data):
    """Validate that loaded data has expected structure"""
    print("\n=== Validating Data Structure ===")
    
    validation_errors = []
    
    # Check inventory data structure
    if not data['inventory'].empty:
        required_inventory_cols = ['客户', '物料名称', '结存']  # Updated to match actual columns
        missing_cols = [col for col in required_inventory_cols if col not in data['inventory'].columns]
        if missing_cols:
            validation_errors.append(f"Inventory missing columns: {missing_cols}")
    
    # Check production data structure
    if not data['production'].empty:
        required_production_cols = ['物料名称', '主数量', '物料大类']  # Updated to match actual columns
        missing_cols = [col for col in required_production_cols if col not in data['production'].columns]
        if missing_cols:
            validation_errors.append(f"Production missing columns: {missing_cols}")
    
    # Check sales data structure
    if not data['sales'].empty:
        required_sales_cols = ['物料名称', '主数量', '客户名称', '物料分类']
        missing_cols = [col for col in required_sales_cols if col not in data['sales'].columns]
        if missing_cols:
            validation_errors.append(f"Sales missing columns: {missing_cols}")
    
    if validation_errors:
        print("❌ Data validation errors found:")
        for error in validation_errors:
            print(f"  - {error}")
        return False
    else:
        print("✅ Data structure validation passed")
        return True


if __name__ == "__main__":
    # Test data loading
    data = load_all_data()
    validate_data_structure(data)