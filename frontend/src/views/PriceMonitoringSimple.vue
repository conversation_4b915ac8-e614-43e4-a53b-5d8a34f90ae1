<template>
  <div class="price-monitoring-simple">
    <h2>价格监控（调试版）</h2>
    <div v-if="loading">加载中...</div>
    <div v-else>
      <div class="stats">
        <h3>统计信息</h3>
        <pre>{{ JSON.stringify(priceStats, null, 2) }}</pre>
      </div>
      <div class="alerts">
        <h3>预警信息 ({{ alerts.length }} 条)</h3>
        <pre>{{ JSON.stringify(alerts.slice(0, 5), null, 2) }}</pre>
      </div>
      <div class="test-components">
        <h3>测试组件加载</h3>
        <button @click="testComponent = 'stats'">加载统计组件</button>
        <button @click="testComponent = 'chart'">加载图表组件</button>
        <button @click="testComponent = 'alert'">加载预警组件</button>
        <button @click="testComponent = 'config'">加载配置组件</button>
        <button @click="runAlertDetection" style="background: #ff9800; color: white;">
          运行预警检测
        </button>
        <button @click="generateMockAlerts" style="background: #4CAF50; color: white;">
          生成模拟预警
        </button>
        <button @click="testComponent = null">清除</button>
        
        <div v-if="testComponent === 'stats'" class="test-area">
          <PriceStatsSummary :stats="priceStats" :loading="false" />
        </div>
        
        <div v-if="testComponent === 'chart'" class="test-area">
          <h4>测试图表组件</h4>
          <PriceTrendChart :data="trendData" :loading="false" />
        </div>
        
        <div v-if="testComponent === 'alert'" class="test-area">
          <h4>测试预警组件</h4>
          <PriceAlertPanel 
            :alerts="alerts" 
            @acknowledge-alert="console.log('acknowledge-alert')"
            @acknowledge-all="console.log('acknowledge-all')"
            @refresh="console.log('refresh')"
            @view-details="console.log('view-details')"
          />
        </div>
        
        <div v-if="testComponent === 'config'" class="test-area">
          <SimpleAlertConfig />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { usePricingStore } from '@/stores/pricing'
import PriceStatsSummary from '@/components/priceMonitoring/PriceStatsSummary.vue'
import PriceTrendChart from '@/components/priceMonitoring/PriceTrendChart.vue'
import PriceAlertPanel from '@/components/priceMonitoring/PriceAlertPanel.vue'
import SimpleAlertConfig from '@/components/priceMonitoring/SimpleAlertConfig.vue'

const pricingStore = usePricingStore()
const loading = ref(true)
const priceStats = ref({})
const alerts = ref([])
const trendData = ref([])
const testComponent = ref(null)

onMounted(async () => {
  try {
    console.log('开始加载价格监控数据...')
    await pricingStore.refreshMonitoringData()
    priceStats.value = pricingStore.priceStats || {}
    alerts.value = pricingStore.alerts || []
    trendData.value = pricingStore.trendData || []
    console.log('价格监控数据加载成功:', { 
      statsKeys: Object.keys(priceStats.value),
      alertCount: alerts.value.length,
      trendCount: trendData.value.length
    })
  } catch (error) {
    console.error('加载失败:', error)
  } finally {
    loading.value = false
  }
})

// 运行预警检测
const runAlertDetection = async () => {
  try {
    console.log('开始运行预警检测...')
    const today = new Date().toISOString().split('T')[0]
    await pricingStore.runAlertDetection(today)
    
    // 重新加载预警数据
    await pricingStore.fetchAlerts()
    alerts.value = pricingStore.alerts || []
    
    console.log('预警检测完成，发现', alerts.value.length, '条预警')
    alert(`预警检测完成！发现 ${alerts.value.length} 条预警`)
  } catch (error) {
    console.error('预警检测失败:', error)
    alert('预警检测失败，请查看控制台')
  }
}

// 生成模拟预警数据
const generateMockAlerts = () => {
  const mockAlerts = [
    {
      alert_id: 1,
      alert_date: new Date().toISOString().split('T')[0],
      product_id: 1,
      product_name: '冷冻鸡胸肉',
      alert_type: 'DAILY_DROP',
      alert_level: 'CRITICAL',
      current_price: 4500,
      previous_price: 5200,
      price_change: -700,
      change_percentage: -13.46,
      alert_message: '冷冻鸡胸肉价格单日下降13.46%，降幅700元/吨',
      is_acknowledged: false,
      created_at: new Date().toISOString()
    },
    {
      alert_id: 2,
      alert_date: new Date().toISOString().split('T')[0],
      product_id: 2,
      product_name: '鸡翅根',
      alert_type: 'CONSECUTIVE_DROP',
      alert_level: 'WARNING',
      current_price: 6800,
      previous_price: 7200,
      price_change: -400,
      change_percentage: -5.56,
      alert_message: '鸡翅根连续3天价格下降，累计降幅400元/吨',
      is_acknowledged: false,
      created_at: new Date(Date.now() - 3600000).toISOString()
    },
    {
      alert_id: 3,
      alert_date: new Date().toISOString().split('T')[0],
      product_id: 3,
      product_name: '鸡肉串',
      alert_type: 'VOLATILITY',
      alert_level: 'INFO',
      current_price: 5500,
      previous_price: 5300,
      price_change: 200,
      change_percentage: 3.77,
      alert_message: '鸡肉串最近7天价格波动率18.5%，超过正常范围',
      is_acknowledged: true,
      created_at: new Date(Date.now() - 7200000).toISOString()
    },
    {
      alert_id: 4,
      alert_date: new Date().toISOString().split('T')[0],
      product_id: 4,
      product_name: '鸡腿肉',
      alert_type: 'DAILY_DROP',
      alert_level: 'WARNING',
      current_price: 7100,
      previous_price: 7500,
      price_change: -400,
      change_percentage: -5.33,
      alert_message: '鸡腿肉价格单日下降5.33%，降幅400元/吨',
      is_acknowledged: false,
      created_at: new Date(Date.now() - 1800000).toISOString()
    }
  ]
  
  alerts.value = mockAlerts
  pricingStore.alerts = mockAlerts
  console.log('已生成', mockAlerts.length, '条模拟预警')
  alert(`已生成 ${mockAlerts.length} 条模拟预警！`)
}
</script>

<style scoped>
.price-monitoring-simple {
  padding: 20px;
}
pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 300px;
}
.test-components {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 4px;
}
.test-components button {
  margin-right: 10px;
  padding: 5px 10px;
}
.test-area {
  margin-top: 20px;
  padding: 20px;
  background: #f9f9f9;
  border-radius: 4px;
}
</style>