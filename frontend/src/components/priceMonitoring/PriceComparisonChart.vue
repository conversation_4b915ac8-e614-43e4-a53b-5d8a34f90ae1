<template>
  <div class="price-comparison-chart">
    <div class="chart-header">
      <h3>价格对比分析</h3>
      <div class="chart-controls">
        <select v-model="chartType" @change="updateChart" class="chart-type-select">
          <option value="line">折线图</option>
          <option value="bar">柱状图</option>
          <option value="candlestick">K线图</option>
        </select>
        <button @click="exportChart" class="export-btn" :disabled="loading">
          导出图表
        </button>
      </div>
    </div>
    
    <div class="products-selector">
      <label v-for="product in availableProducts" :key="product.product_name" class="product-checkbox">
        <input 
          type="checkbox" 
          :value="product.product_name"
          v-model="selectedProducts"
          @change="onProductsChange"
          :disabled="selectedProducts.length >= 5 && !selectedProducts.includes(product.product_name)"
        >
        <span>{{ product.product_name }}</span>
        <span class="product-badge">{{ product.adjustment_days }}天</span>
      </label>
    </div>
    
    <div ref="chartContainer" class="chart-container" :class="{ loading: loading }"></div>
    
    <div v-if="loading" class="loading-overlay">
      <div class="loader"></div>
      <p>正在加载数据...</p>
    </div>
    
    <div v-if="performanceStats" class="performance-stats">
      <span>数据点: {{ performanceStats.dataCount }}</span>
      <span v-if="performanceStats.isOptimized" class="optimized-badge">已优化</span>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { getKeyProducts, getAggregatedPrices } from '@/utils/priceDataManager';
import chartOptimizer from '@/utils/chartOptimizer';

const props = defineProps({
  period: {
    type: Number,
    default: 30
  }
});

const emit = defineEmits(['product-selected']);

const chartContainer = ref(null);
let chartInstance = null;

const loading = ref(false);
const chartType = ref('line');
const selectedProducts = ref([]);
const availableProducts = ref([]);
const aggregatedData = ref({});
const performanceStats = ref(null);

const colorPalette = [
  '#005BAC', '#49A9E8', '#D92E2E', '#FFA500', '#32CD32',
  '#9370DB', '#FF69B4', '#20B2AA', '#FFD700', '#DC143C'
];

async function loadAvailableProducts() {
  try {
    const response = await getKeyProducts({ 
      days: props.period, 
      limit: 10,
      includeSpecs: false 
    });
    
    if (response.success && response.data) {
      availableProducts.value = response.data;
      
      // 默认选择前3个产品
      selectedProducts.value = response.data
        .slice(0, 3)
        .map(p => p.product_name);
      
      await loadComparisonData();
    }
  } catch (error) {
    console.error('Failed to load products:', error);
  }
}

async function loadComparisonData() {
  if (selectedProducts.value.length === 0) {
    if (chartInstance) {
      chartInstance.clear();
    }
    return;
  }
  
  loading.value = true;
  
  try {
    const endDate = new Date().toISOString().split('T')[0];
    const startDate = new Date(Date.now() - props.period * 24 * 60 * 60 * 1000)
      .toISOString().split('T')[0];
    
    const response = await getAggregatedPrices(selectedProducts.value, {
      startDate,
      endDate,
      groupBySpec: false
    });
    
    if (response.success) {
      aggregatedData.value = response.data;
      updateChart();
    }
  } catch (error) {
    console.error('Failed to load comparison data:', error);
  } finally {
    loading.value = false;
  }
}

function updateChart() {
  if (!chartInstance || !aggregatedData.value) return;
  
  const option = generateChartOption();
  chartOptimizer.batchUpdate(chartInstance, option, true);
  
  // 更新性能统计
  performanceStats.value = chartOptimizer.getPerformanceStats(chartInstance);
}

function generateChartOption() {
  const allDates = new Set();
  const seriesData = {};
  
  // 收集所有日期和数据
  Object.entries(aggregatedData.value).forEach(([product, data]) => {
    if (!selectedProducts.value.includes(product)) return;
    
    seriesData[product] = {};
    data.forEach(item => {
      allDates.add(item.adjustment_date);
      seriesData[product][item.adjustment_date] = item;
    });
  });
  
  const sortedDates = Array.from(allDates).sort();
  
  let series = [];
  
  if (chartType.value === 'line') {
    series = generateLineSeries(seriesData, sortedDates);
  } else if (chartType.value === 'bar') {
    series = generateBarSeries(seriesData, sortedDates);
  } else if (chartType.value === 'candlestick') {
    series = generateCandlestickSeries(seriesData, sortedDates);
  }
  
  return {
    title: {
      text: `价格对比分析 (${props.period}天)`,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        animation: false
      },
      formatter: function(params) {
        let html = `<div style="font-weight: bold">${params[0].axisValue}</div>`;
        params.forEach((param, index) => {
          if (param.value !== null && param.value !== undefined) {
            const value = Array.isArray(param.value) ? param.value[1] : param.value;
            html += `<div>${param.marker} ${param.seriesName}: ${value.toFixed(2)} 元/吨</div>`;
          }
        });
        return html;
      }
    },
    legend: {
      data: selectedProducts.value,
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: sortedDates,
      axisLabel: {
        rotate: 45,
        formatter: (value) => value.substring(5)
      }
    },
    yAxis: {
      type: 'value',
      name: '价格 (元/吨)',
      scale: true,
      axisLabel: {
        formatter: '{value}'
      }
    },
    dataZoom: [
      {
        type: 'slider',
        start: Math.max(0, 100 - (30 / sortedDates.length) * 100),
        end: 100,
        bottom: 20
      },
      {
        type: 'inside',
        start: Math.max(0, 100 - (30 / sortedDates.length) * 100),
        end: 100
      }
    ],
    series: series
  };
}

function generateLineSeries(seriesData, dates) {
  return Object.entries(seriesData).map(([product, data], index) => ({
    name: product,
    type: 'line',
    smooth: true,
    symbol: 'circle',
    symbolSize: 4,
    itemStyle: {
      color: colorPalette[index % colorPalette.length]
    },
    emphasis: {
      focus: 'series'
    },
    data: dates.map(date => {
      const item = data[date];
      return item ? item.daily_close || item.daily_avg : null;
    })
  }));
}

function generateBarSeries(seriesData, dates) {
  return Object.entries(seriesData).map(([product, data], index) => ({
    name: product,
    type: 'bar',
    itemStyle: {
      color: colorPalette[index % colorPalette.length]
    },
    emphasis: {
      focus: 'series'
    },
    data: dates.map(date => {
      const item = data[date];
      return item ? item.daily_close || item.daily_avg : null;
    })
  }));
}

function generateCandlestickSeries(seriesData, dates) {
  return Object.entries(seriesData).map(([product, data], index) => ({
    name: product,
    type: 'candlestick',
    itemStyle: {
      color: colorPalette[index % colorPalette.length],
      color0: '#00da3c',
      borderColor: colorPalette[index % colorPalette.length],
      borderColor0: '#008f28'
    },
    data: dates.map(date => {
      const item = data[date];
      if (!item) return null;
      return [
        item.daily_open || item.daily_close,
        item.daily_close,
        item.daily_low || item.daily_close,
        item.daily_high || item.daily_close
      ];
    })
  }));
}

async function onProductsChange() {
  await nextTick();
  loadComparisonData();
}

async function exportChart() {
  if (!chartInstance) return;
  
  try {
    const url = await chartOptimizer.exportChart(chartInstance, {
      type: 'png',
      pixelRatio: 2
    });
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `price-comparison-${new Date().toISOString().split('T')[0]}.png`;
    link.click();
  } catch (error) {
    console.error('Failed to export chart:', error);
  }
}

function handleResize() {
  if (chartInstance && chartContainer.value) {
    chartOptimizer.resizeChart(chartInstance);
  }
}

onMounted(async () => {
  await nextTick();
  
  if (chartContainer.value) {
    chartInstance = chartOptimizer.createChart(chartContainer.value);
    window.addEventListener('resize', handleResize);
    await loadAvailableProducts();
  }
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (chartInstance) {
    chartOptimizer.disposeChart(chartInstance);
  }
});

watch(() => props.period, () => {
  loadAvailableProducts();
});
</script>

<style scoped>
.price-comparison-chart {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.chart-header h3 {
  margin: 0;
  color: #333;
}

.chart-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.chart-type-select {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
}

.export-btn {
  padding: 0.5rem 1rem;
  background-color: #005BAC;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.export-btn:hover:not(:disabled) {
  background-color: #004590;
}

.export-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.products-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.product-checkbox {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.product-checkbox input[type="checkbox"] {
  cursor: pointer;
}

.product-checkbox span {
  font-size: 0.9rem;
}

.product-badge {
  font-size: 0.8rem;
  padding: 0.2rem 0.4rem;
  background-color: #e0e0e0;
  border-radius: 3px;
  color: #666;
}

.chart-container {
  width: 100%;
  height: 500px;
  position: relative;
  transition: opacity 0.3s;
}

.chart-container.loading {
  opacity: 0.5;
}

.loading-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #005BAC;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.performance-stats {
  position: absolute;
  bottom: 10px;
  right: 10px;
  font-size: 0.8rem;
  color: #666;
  display: flex;
  gap: 1rem;
  align-items: center;
}

.optimized-badge {
  padding: 0.2rem 0.5rem;
  background-color: #4caf50;
  color: white;
  border-radius: 3px;
  font-size: 0.75rem;
}

@media (max-width: 768px) {
  .chart-container {
    height: 400px;
  }
  
  .products-selector {
    gap: 0.5rem;
  }
  
  .chart-controls {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>