<template>
  <BaseCard title="价格对比" class="price-comparison-chart">
    <EChartsWrapper
      :options="chartOptions"
      :loading="isLoading"
      height="400px"
      @chart-ready="handleChartReady"
    />
  </BaseCard>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { usePricingStore } from '@/stores/pricing'
import BaseCard from '@/components/common/BaseCard.vue'
import EChartsWrapper from '@/components/charts/EChartsWrapper.vue'

const props = defineProps({
  startDate: {
    type: String,
    required: true
  },
  endDate: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['data-updated'])

const pricingStore = usePricingStore()
const isLoading = ref(false)

// 图表配置
const chartOptions = computed(() => {
  if (!pricingStore.priceData.length) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'middle'
      }
    }
  }

  return {
    title: {
      text: '价格对比分析',
      left: 'left'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['当前价格', '历史价格'],
      top: 30
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: pricingStore.priceData.map(item => item.date)
    },
    yAxis: {
      type: 'value',
      name: '价格 (元/T)',
      axisLabel: {
        formatter: '{value}'
      }
    },
    series: [
      {
        name: '当前价格',
        type: 'line',
        data: pricingStore.priceData.map(item => item.currentPrice),
        itemStyle: {
          color: '#1976D2'
        }
      },
      {
        name: '历史价格',
        type: 'line',
        data: pricingStore.priceData.map(item => item.historicalPrice),
        itemStyle: {
          color: '#FF9800'
        }
      }
    ]
  }
})

const handleChartReady = (chart) => {
  console.log('✅ Price comparison chart ready')
}

const fetchData = async () => {
  try {
    isLoading.value = true
    await pricingStore.fetchPriceData(props.startDate, props.endDate)
    emit('data-updated', pricingStore.priceData)
  } catch (error) {
    console.error('Failed to load price comparison data:', error)
  } finally {
    isLoading.value = false
  }
}

// 监听日期变化
watch([() => props.startDate, () => props.endDate], fetchData)

onMounted(fetchData)
</script>

<style scoped>
.price-comparison-chart {
  height: 100%;
}
</style>