<template>
  <div class="price-trend-chart-container">
    <div class="chart-header">
      <h3>{{ productName }} 价格趋势</h3>
      <div class="chart-controls">
        <label>
          <input type="checkbox" v-model="showMA" @change="updateChart">
          显示移动平均线
        </label>
        <label>
          <input type="checkbox" v-model="includeHistory" @change="fetchPriceTrends">
          显示完整历史
        </label>
      </div>
    </div>
    <div ref="chart" class="chart-instance"></div>
    <div v-if="loading" class="loading-overlay">
      <div class="loader"></div>
    </div>
    <div v-if="error" class="error-message">
      <p>{{ error }}</p>
      <button @click="retry" class="retry-button">重试</button>
    </div>
    <div v-if="isStale" class="stale-warning">
      数据可能已过期，显示的是缓存数据
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted, nextTick, computed } from 'vue';
import * as echarts from 'echarts';
import { getPriceTrends } from '@/utils/priceDataManager';

const props = defineProps({
  productName: {
    type: String,
    required: true,
  },
  period: {
    type: Number,
    default: 90,
  },
  specification: {
    type: String,
    default: null,
  }
});

const chart = ref(null);
let chartInstance = null;
const loading = ref(false);
const error = ref(null);
const isStale = ref(false);
const showMA = ref(true);
const includeHistory = ref(false);
const trendData = ref([]);

// 数据验证辅助函数
function validateAPIResponse(response) {
  if (!response) {
    throw new Error('空响应');
  }
  
  if (!response.success) {
    throw new Error(response.message || '请求失败');
  }
  
  if (!response.data || !Array.isArray(response.data)) {
    console.error('Invalid data structure:', response);
    throw new Error('响应数据格式无效');
  }
  
  // 验证每条数据记录
  const validData = response.data.filter(item => {
    if (!item || typeof item !== 'object') return false;
    if (!item.adjustment_date) return false;
    if (item.current_price === undefined || item.current_price === null) return false;
    
    const price = parseFloat(item.current_price);
    return !isNaN(price) && price >= 0;
  });
  
  if (validData.length === 0 && response.data.length > 0) {
    console.warn('All data records were filtered out as invalid');
  }
  
  return {
    ...response,
    data: validData
  };
}

// 初始化基础图表配置，确保所有必要属性都有默认值
const getDefaultChartOption = () => ({
  color: ['#005BAC', '#49A9E8', '#D92E2E', '#FFA500', '#32CD32', '#9370DB'],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      animation: false
    },
    formatter: function(params) {
      if (!params || params.length === 0) return '';
      
      let html = `<div style="font-weight: bold">${params[0]?.axisValue || ''}</div>`;
      params.forEach(param => {
        if (param && param.value !== undefined && param.value !== null && !isNaN(param.value)) {
          const value = parseFloat(param.value);
          html += `<div>${param.marker} ${param.seriesName}: ${value.toFixed(2)} 元/吨</div>`;
        }
      });
      return html;
    }
  },
  legend: {
    data: ['当前价格'],
    top: 20
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [],
    boundaryGap: false,
    axisLabel: {
      rotate: 45,
      formatter: (value) => {
        if (!value) return '';
        // 安全的字符串处理
        const parts = value.split('-');
        return parts.length >= 3 ? `${parts[1]}-${parts[2]}` : value;
      }
    }
  },
  yAxis: {
    type: 'value',
    name: '价格 (元/吨)',
    min: 0,
    axisLabel: {
      formatter: (value) => Math.round(value)
    }
  },
  dataZoom: [
    {
      type: 'slider',
      start: 0,
      end: 100,
      bottom: 10
    },
    {
      type: 'inside',
      start: 0,
      end: 100
    }
  ],
  series: [{
    name: '当前价格',
    type: 'line',
    data: [],
    smooth: true,
    symbol: 'circle',
    symbolSize: 6,
    connectNulls: true
  }]
});

async function fetchPriceTrends(forceRefresh = false) {
  if (!props.productName) {
    console.warn('No product name provided');
    return;
  }

  loading.value = true;
  error.value = null;
  isStale.value = false;
  
  try {
    console.log('Fetching price trends for:', props.productName);
    
    const response = await getPriceTrends(props.productName, {
      period: props.period,
      specification: props.specification,
      includeHistory: includeHistory.value,
      forceRefresh
    });
    
    console.log('Price trends response:', response);
    
    if (response && response.success) {
      // 使用验证辅助函数
      const validatedResponse = validateAPIResponse(response);
      
      trendData.value = validatedResponse.data;
      isStale.value = response.isStale || false;
      
      // 立即更新图表
      await nextTick();
      updateChart();
    } else {
      // 处理API返回的错误
      const errorMsg = response?.message || response?.error?.message || '获取价格趋势失败';
      
      // 如果是产品不存在的错误，显示建议
      if (response?.suggestions && response.suggestions.length > 0) {
        throw new Error(`${errorMsg}\n建议尝试: ${response.suggestions.join(', ')}`);
      }
      
      throw new Error(errorMsg);
    }
  } catch (err) {
    console.error('Failed to fetch price trends:', err);

    // 错误分类处理
    let displayError = '加载失败';
    
    if (err.name === 'NetworkError' || err.code === 'ECONNABORTED') {
      displayError = '网络连接超时，请检查网络后重试';
    } else if (err.response?.status === 404) {
      displayError = err.message || '未找到该产品的价格数据';
    } else if (err.response?.status === 500) {
      displayError = '服务器错误，请稍后重试';
    } else if (err.response?.status === 400) {
      displayError = '请求参数错误，请检查产品名称';
    } else {
      displayError = err.message || '未知错误，请刷新页面重试';
    }
    
    error.value = displayError;

    // 降级方案：显示错误状态的图表
    if (chartInstance) {
      chartInstance.setOption({
        title: {
          text: '数据加载失败',
          subtext: displayError,
          left: 'center',
          top: 'center',  
          textStyle: { color: '#ff4d4f', fontSize: 16 },
          subtextStyle: { fontSize: 12, width: 300, overflow: 'break' }
        },
        xAxis: { type: 'category', data: [] },
        yAxis: { type: 'value' },
        series: []
      });
    }
  } finally {
    loading.value = false;
  }
}

function updateChart() {
  if (!chartInstance) {
    console.error('Chart instance not initialized');
    return;
  }

  // 处理空数据或无效数据
  if (!trendData.value || !Array.isArray(trendData.value) || trendData.value.length === 0) {
    // 使用mergeOption而不是setOption，避免破坏图表结构
    const emptyOption = getDefaultChartOption();
    emptyOption.title = {
      text: '暂无数据',
      subtext: '请检查产品名称或选择其他时间段',
      left: 'center',
      top: 'center',
      textStyle: { color: '#999', fontSize: 16 },
      subtextStyle: { fontSize: 12 }
    };
    emptyOption.xAxis.data = [];
    emptyOption.series[0].data = [];
    
    chartInstance.clear();
    chartInstance.setOption(emptyOption, true);
    return;
  }

  const data = trendData.value;

  // 数据验证和清洗 - 更严格的验证
  const validData = data.filter(item => {
    if (!item) return false;
    if (!item.adjustment_date) {
      console.warn('Missing adjustment_date:', item);
      return false;
    }
    if (item.current_price === undefined || item.current_price === null) {
      console.warn('Missing current_price:', item);
      return false;
    }
    const price = parseFloat(item.current_price);
    if (isNaN(price) || price < 0) {
      console.warn('Invalid current_price:', item.current_price);
      return false;
    }
    return true;
  });

  if (validData.length === 0) {
    console.error('No valid data after filtering');
    chartInstance.setOption({
      title: {
        text: '数据格式错误',
        subtext: '无法解析价格数据',
        left: 'center',
        top: 'center',
        textStyle: { color: '#ff4d4f', fontSize: 16 }
      },
      xAxis: { data: [] },
      yAxis: {},
      series: []
    });
    return;
  }

  // 按日期排序确保时间序列正确
  const sortedData = [...validData].sort((a, b) => 
    new Date(a.adjustment_date).getTime() - new Date(b.adjustment_date).getTime()
  );

  // 安全的数据提取与转换
  const dates = sortedData.map(item => {
    try {
      // 确保日期格式正确
      const date = new Date(item.adjustment_date);
      return date.toISOString().split('T')[0];
    } catch (e) {
      console.error('Invalid date:', item.adjustment_date);
      return item.adjustment_date;
    }
  });
  
  const currentPrices = sortedData.map(item => {
    const price = parseFloat(item.current_price);
    return isNaN(price) ? 0 : price;
  });

  // 处理可选字段，提供默认值
  const dailyHighs = sortedData.map(item => {
    if (item.daily_high !== undefined && item.daily_high !== null) {
      const high = parseFloat(item.daily_high);
      return isNaN(high) ? parseFloat(item.current_price) : high;
    }
    return parseFloat(item.current_price);
  });
  
  const dailyLows = sortedData.map(item => {
    if (item.daily_low !== undefined && item.daily_low !== null) {
      const low = parseFloat(item.daily_low);
      return isNaN(low) ? parseFloat(item.current_price) : low;
    }
    return parseFloat(item.current_price);
  });
  
  const series = [
    {
      name: '当前价格',
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      data: currentPrices,
      markPoint: {
        data: [
          { type: 'max', name: '最高价' },
          { type: 'min', name: '最低价' }
        ]
      },
      markLine: {
        data: [
          { type: 'average', name: '平均价' }
        ]
      }
    }
  ];

  // 添加日内高低价（如果有）
  if (includeHistory.value && dailyHighs.some(v => v !== undefined)) {
    series.push({
      name: '日内最高',
      type: 'line',
      smooth: true,
      lineStyle: { type: 'dotted' },
      data: dailyHighs
    });
    series.push({
      name: '日内最低',
      type: 'line',
      smooth: true,
      lineStyle: { type: 'dotted' },
      data: dailyLows
    });
  }

  // 添加移动平均线 - 在前端计算
  if (showMA.value && currentPrices.length > 0) {
    // 计算移动平均线的辅助函数
    const calculateMA = (data, period) => {
      return data.map((_, index) => {
        if (index < period - 1) return null;
        const sum = data.slice(index - period + 1, index + 1).reduce((a, b) => a + b, 0);
        return sum / period;
      });
    };
    
    const maConfig = [
      { period: 5, name: 'MA5', color: '#FFA500' },
      { period: 10, name: 'MA10', color: '#32CD32' },
      { period: 20, name: 'MA20', color: '#9370DB' }
    ];
    
    maConfig.forEach(({ period, name, color }) => {
      if (currentPrices.length >= period) {
        const maData = calculateMA(currentPrices, period);
        series.push({
          name: name,
          type: 'line',
          smooth: true,
          lineStyle: { width: 2, color },
          data: maData,
          connectNulls: false,
          symbol: 'none' // 不显示数据点
        });
      }
    });
  }

  // 添加填充数据标记
  if (validData.some(item => item.is_filled)) {
    series[0].markArea = {
      silent: true,
      data: [[{
        xAxis: validData.find(item => item.is_filled)?.adjustment_date,
        itemStyle: {
          color: 'rgba(255, 173, 177, 0.2)'
        }
      }, {
        xAxis: validData.filter(item => item.is_filled).pop()?.adjustment_date
      }]]
    };
  }

  const legendData = series.map(s => s.name);
  
  // 安全的图表更新
  try {
    // 创建新的配置对象，避免修改原始配置
    const newOption = getDefaultChartOption();
    
    // 计算Y轴范围，避免数据过于集中
    const allPrices = [...currentPrices, ...dailyHighs, ...dailyLows].filter(p => !isNaN(p) && p > 0);
    
    if (allPrices.length === 0) {
      console.error('No valid price data');
      throw new Error('没有有效的价格数据');
    }
    
    const minPrice = Math.min(...allPrices);
    const maxPrice = Math.max(...allPrices);
    const priceRange = maxPrice - minPrice;
    const yAxisMin = Math.max(0, Math.floor(minPrice - priceRange * 0.1));
    const yAxisMax = Math.ceil(maxPrice + priceRange * 0.1);

    // 更新配置
    newOption.legend.data = legendData;
    newOption.xAxis.data = dates;
    newOption.yAxis.min = yAxisMin;
    newOption.yAxis.max = yAxisMax;
    newOption.series = series;
    
    // 清除之前的图表内容并重新设置
    chartInstance.clear();
    chartInstance.setOption(newOption, true);
    
    console.log('Chart updated successfully with', dates.length, 'data points');
  } catch (err) {
    console.error('Chart update failed:', err);
    error.value = '图表更新失败: ' + err.message;
    
    // 显示错误状态 - 使用安全的配置
    const errorOption = getDefaultChartOption();
    errorOption.title = {
      text: '图表渲染错误',
      subtext: err.message,
      left: 'center',
      top: 'center',
      textStyle: { color: '#ff4d4f', fontSize: 16 },
      subtextStyle: { fontSize: 12, width: 300, overflow: 'break' }
    };
    errorOption.xAxis.data = [];
    errorOption.series[0].data = [];
    
    chartInstance.clear();
    chartInstance.setOption(errorOption, true);
  }
}

function retry() {
  fetchPriceTrends(true); // 强制刷新
}

function handleResize() {
  if (chartInstance) {
    chartInstance.resize();
  }
}

onMounted(() => {
  nextTick(() => {
    try {
      // 确保DOM元素存在
      if (!chart.value) {
        console.error('Chart DOM element not found');
        return;
      }
      
      // 初始化ECharts实例
      chartInstance = echarts.init(chart.value);
      
      // 使用安全的初始配置
      const initialOption = getDefaultChartOption();
      chartInstance.setOption(initialOption, true);
      
      // 如果有产品名称，获取数据
      if (props.productName) {
        fetchPriceTrends();
      }
      
      window.addEventListener('resize', handleResize);
    } catch (err) {
      console.error('Chart initialization failed:', err);
      error.value = '图表初始化失败: ' + err.message;
    }
  });
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (chartInstance) {
    chartInstance.dispose();
  }
});

watch(() => [props.productName, props.period, props.specification], () => {
  if (props.productName) {
    fetchPriceTrends();
  }
});
</script>

<style scoped>
.price-trend-chart-container {
  position: relative;
  min-height: 450px;
  background-color: #fff;
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.chart-header h3 {
  margin: 0;
  color: #333;
}

.chart-controls {
  display: flex;
  gap: 1rem;
}

.chart-controls label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  cursor: pointer;
}

.chart-instance {
  width: 100%;
  height: 400px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.loader {
  border: 5px solid #f3f3f3;
  border-top: 5px solid #005BAC;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #D92E2E;
  z-index: 10;
}

.retry-button {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #005BAC;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
}

.retry-button:hover {
  background-color: #004590;
}

.stale-warning {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #fff3cd;
  color: #856404;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.85rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
