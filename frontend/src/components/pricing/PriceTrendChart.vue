<template>
  <BaseCard title="价格趋势分析" :loading="loading" :error="error">
    <template #extra>
      <div class="chart-controls">        
        <select v-model="chartType" @change="updateChartType" class="chart-type-selector">
          <option value="line">折线图</option>
          <option value="area">面积图</option>
          <option value="candlestick">K线图</option>
        </select>
      </div>
    </template>

    <EChartsWrapper
      :options="chartOptions"
      :height="height"
      :loading="loading"
      @chart-ready="onChartReady"
      @chart-click="onChartClick"
    />

    <div class="price-summary">
      <div class="summary-item">
        <span class="label">平均价格：</span>
        <span class="value">{{ formatPrice(averagePrice) }}</span>
      </div>
      <div class="summary-item">
        <span class="label">价格变化：</span>
        <span class="value" :class="getTrendColorClass(latestPrice, firstPrice)">
          {{ getTrendIcon(latestPrice, firstPrice) }} {{ formatChangeRate(priceChangeRate) }}
        </span>
      </div>
      <div class="summary-item">
        <span class="label">波动幅度：</span>
        <span class="value">{{ volatilityRange }}</span>
      </div>
      <div class="summary-item">
        <span class="label">趋势方向：</span>
        <span class="value" :class="trendColorClass">
          {{ trendIcon }} {{ trendText }}
        </span>
      </div>
    </div>
  </BaseCard>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { usePricingStore } from '@/stores/pricing'
import { formatPrice, formatChangeRate, getTrendIcon, getTrendColorClass } from '@/utils/formatters'
import { getBaseChartOption, getTooltipConfig, getXAxisConfig, getYAxisConfig, getLineSeriesConfig, getGridConfig } from '@/utils/charts'
import { CHART_COLORS } from '@/utils/constants'
import BaseCard from '@/components/common/BaseCard.vue'
import EChartsWrapper from '@/components/charts/EChartsWrapper.vue'

const props = defineProps({
  startDate: {
    type: String,
    default: '2025-06-01' // 备用默认值，实际值由父组件传入
  },
  endDate: {
    type: String,
    default: '2025-07-29' // 备用默认值，实际值由父组件传入
  },
  height: {
    type: String,
    default: '400px'
  }
})

const emit = defineEmits(['chart-ready', 'chart-click', 'data-updated'])

const pricingStore = usePricingStore()
const loading = ref(false)
const error = ref('')
const chartType = ref('line')
const chartInstance = ref(null)

// 计算属性
const chartData = computed(() => {
  return pricingStore.priceData.map(item => ({
    date: item.date,
    price: item.price,
    volume: item.volume
  }))
})

const averagePrice = computed(() => {
  if (chartData.value.length === 0) return 0
  const total = chartData.value.reduce((sum, item) => sum + item.price, 0)
  return total / chartData.value.length
})

const latestPrice = computed(() => {
  return chartData.value.length > 0 ? 
    chartData.value[chartData.value.length - 1]?.price || 0 : 0
})

const firstPrice = computed(() => {
  return chartData.value.length > 0 ? 
    chartData.value[0]?.price || 0 : 0
})

const priceChangeRate = computed(() => {
  if (firstPrice.value === 0) return 0
  return ((latestPrice.value - firstPrice.value) / firstPrice.value) * 100
})

const volatilityRange = computed(() => {
  if (chartData.value.length === 0) return '--'
  
  const prices = chartData.value.map(item => item.price)
  const max = Math.max(...prices)
  const min = Math.min(...prices)
  
  return `${formatPrice(min)} - ${formatPrice(max)}`
})

const trendDirection = computed(() => {
  if (chartData.value.length < 2) return 'stable'
  
  const first = firstPrice.value
  const last = latestPrice.value
  
  if (last > first * 1.05) return 'increasing'
  if (last < first * 0.95) return 'decreasing'
  return 'stable'
})

const trendIcon = computed(() => {
  const icons = {
    increasing: '📈',
    decreasing: '📉',
    stable: '➡️'
  }
  return icons[trendDirection.value]
})

const trendText = computed(() => {
  const texts = {
    increasing: '上涨趋势',
    decreasing: '下跌趋势',
    stable: '平稳趋势'
  }
  return texts[trendDirection.value]
})

const trendColorClass = computed(() => {
  const classes = {
    increasing: 'trend-positive',
    decreasing: 'trend-negative',
    stable: 'trend-neutral'
  }
  return classes[trendDirection.value]
})

// 图表配置
const chartOptions = computed(() => {
  if (chartData.value.length === 0) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#999',
          fontSize: 14
        }
      }
    }
  }

  const dates = chartData.value.map(item => item.date)
  const prices = chartData.value.map(item => item.price)

  const option = {
    ...getBaseChartOption(),
    title: {
      text: '价格趋势分析',
      left: 'center',
      textStyle: {
        color: CHART_COLORS.PRIMARY,
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: getTooltipConfig((params) => {
      const point = params[0]
      return `${point.axisValue}<br/>价格: ${formatPrice(point.value)}`
    }),
    xAxis: getXAxisConfig(dates, { rotate: 45 }),
    yAxis: getYAxisConfig({
      name: '价格(元/T)',
      formatter: (value) => formatPrice(value)
    }),
    grid: getGridConfig()
  }

  // 根据图表类型生成不同的系列配置
  switch (chartType.value) {
    case 'area':
      option.series = [
        getLineSeriesConfig('价格', prices, {
          color: CHART_COLORS.PRIMARY,
          smooth: true,
          area: true
        })
      ]
      break
    case 'candlestick':
      // K线图需要特殊处理，这里简化为折线图
      option.series = [
        getLineSeriesConfig('价格', prices, {
          color: CHART_COLORS.ACCENT,
          smooth: false
        })
      ]
      break
    case 'line':
    default:
      option.series = [
        getLineSeriesConfig('价格', prices, {
          color: CHART_COLORS.PRIMARY,
          smooth: true
        })
      ]
      break
  }

  // 添加平均价格线
  option.series[0].markLine = {
    data: [{
      yAxis: averagePrice.value,
      lineStyle: {
        color: CHART_COLORS.SECONDARY,
        type: 'dashed',
        width: 2
      },
      label: {
        formatter: `平均价格: ${formatPrice(averagePrice.value)}`,
        position: 'end'
      }
    }]
  }

  return option
})

// 方法
const fetchData = async () => {
  loading.value = true
  error.value = ''
  
  try {
    await pricingStore.fetchPriceData(props.startDate, props.endDate)
    emit('data-updated', pricingStore.priceData)
  } catch (err) {
    error.value = '数据加载失败'
    console.error('Failed to load price trend data:', err)
  } finally {
    loading.value = false
  }
}

const updateChartType = () => {
  console.log('Chart type updated to:', chartType.value)
}

const onChartReady = (chart) => {
  chartInstance.value = chart
  emit('chart-ready', chart)
}

const onChartClick = (params) => {
  emit('chart-click', params)
}

// 监听props变化
watch([() => props.startDate, () => props.endDate], () => {
  if (props.startDate && props.endDate) {
    fetchData()
  }
})

onMounted(() => {
  if (props.startDate && props.endDate) {
    fetchData()
  }
})

// 暴露方法
defineExpose({
  refresh: fetchData,
  getChart: () => chartInstance.value
})
</script>

<style scoped>
.chart-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.time-selector,
.chart-type-selector {
  padding: 6px 12px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.9em;
  background: white;
  cursor: pointer;
}

.time-selector:focus,
.chart-type-selector:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.price-summary {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  padding: 16px;
  background: var(--background-light);
  border-radius: 8px;
  border-top: 1px solid #f0f0f0;
}

.summary-item {
  text-align: center;
  flex: 1;
}

.label {
  display: block;
  color: var(--text-secondary);
  font-size: 0.85em;
  margin-bottom: 4px;
}

.value {
  display: block;
  color: var(--primary-blue);
  font-weight: 600;
  font-size: 1em;
}

.trend-positive {
  color: var(--success-green);
}

.trend-negative {
  color: var(--signal-red);
}

.trend-neutral {
  color: var(--neutral-gray);
}

@media (max-width: 768px) {
  .chart-controls {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .price-summary {
    flex-direction: column;
    gap: 12px;
  }
  
  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .label, .value {
    display: inline;
    margin: 0;
  }
}
</style>
