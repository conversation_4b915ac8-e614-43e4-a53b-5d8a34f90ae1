<template>
  <BaseCard title="销售量与平均单价趋势" :loading="loading" :error="error">
    <template #extra>
      <select v-model="selectedPeriod" @change="handlePeriodChange" class="period-selector">
        <option value="7">近7天</option>
        <option value="30">近30天</option>
        <option value="90">近90天</option>
      </select>
    </template>

    <EChartsWrapper
      :options="chartOptions"
      height="400px"
      :loading="loading"
      @chart-ready="onChartReady"
      @chart-click="onChartClick"
    />

    <div class="chart-summary">
      <div class="summary-item">
        <span class="label">总销量：</span>
        <span class="value">{{ formatSalesVolume(totalSales) }}</span>
      </div>
      <div class="summary-item">
        <span class="label">平均单价：</span>
        <span class="value">{{ formatPrice(averagePrice) }}</span>
      </div>
      <div class="summary-item">
        <span class="label">价格趋势：</span>
        <span class="value" :class="getTrendColorClass(latestPrice, firstPrice)">
          {{ getTrendIcon(latestPrice, firstPrice) }} {{ formatChangeRate(latestPrice, firstPrice) }}
        </span>
      </div>
    </div>
  </BaseCard>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { fetchData as apiFetchData } from '@/utils/api'
import { formatSalesVolume, formatPrice, formatChangeRate, getTrendIcon, getTrendColorClass } from '@/utils/formatters'
import { getSalesPriceChartOption } from '@/utils/charts'
import BaseCard from '@/components/common/BaseCard.vue'
import EChartsWrapper from './EChartsWrapper.vue'

const props = defineProps({
  startDate: {
    type: String,
    default: '2025-06-01' // 备用默认值，实际值由父组件传入
  },
  endDate: {
    type: String,
    default: '2025-07-29' // 备用默认值，实际值由父组件传入
  }
})

const emit = defineEmits(['chart-ready', 'chart-click', 'data-updated'])

const loading = ref(false)
const error = ref('')
const data = ref([])
const selectedPeriod = ref('30')
const chartInstance = ref(null)

// 计算属性
const totalSales = computed(() => {
  return data.value.reduce((sum, item) => sum + (item.volume || 0), 0)
})

const averagePrice = computed(() => {
  if (data.value.length === 0) return 0
  const totalPrice = data.value.reduce((sum, item) => sum + (item.price || 0), 0)
  return totalPrice / data.value.length
})

const latestPrice = computed(() => {
  return data.value.length > 0 ? data.value[data.value.length - 1]?.price || 0 : 0
})

const firstPrice = computed(() => {
  return data.value.length > 0 ? data.value[0]?.price || 0 : 0
})

// 图表配置
const chartOptions = computed(() => {
  if (data.value.length === 0) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#999',
          fontSize: 14
        }
      }
    }
  }
  
  return getSalesPriceChartOption(data.value)
})

// 获取数据
const fetchSalesPriceData = async () => {
  loading.value = true
  error.value = ''
  
  try {
    const response = await apiFetchData(`/api/trends/sales-price?start_date=${props.startDate}&end_date=${props.endDate}`)
    
    if (response && Array.isArray(response)) {
      data.value = response.map(item => ({
        date: item.date,
        volume: parseFloat(item.volume) || 0,
        price: parseFloat(item.price) || 0
      }))
      
      emit('data-updated', data.value)
      console.log('✅ Sales price data loaded:', data.value.length, 'records')
    } else {
      throw new Error('Invalid data format')
    }
  } catch (err) {
    error.value = '数据加载失败'
    console.error('❌ Failed to load sales price data:', err)
  } finally {
    loading.value = false
  }
}

const handlePeriodChange = () => {
  fetchSalesPriceData()
}

const onChartReady = (chart) => {
  chartInstance.value = chart
  emit('chart-ready', chart)
}

const onChartClick = (params) => {
  emit('chart-click', params)
}

onMounted(() => {
  fetchSalesPriceData()
})

// 暴露方法
defineExpose({
  refresh: fetchSalesPriceData,
  getChart: () => chartInstance.value
})
</script>

<style scoped>
.period-selector {
  padding: 6px 12px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.9em;
  background: white;
  cursor: pointer;
}

.period-selector:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.chart-summary {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  padding: 16px;
  background: var(--background-light);
  border-radius: 8px;
  border-top: 1px solid #f0f0f0;
}

.summary-item {
  text-align: center;
  flex: 1;
}

.label {
  display: block;
  color: var(--text-secondary);
  font-size: 0.85em;
  margin-bottom: 4px;
}

.value {
  display: block;
  color: var(--primary-blue);
  font-weight: 600;
  font-size: 1em;
}

.trend-positive {
  color: var(--success-green);
}

.trend-negative {
  color: var(--signal-red);
}

.trend-neutral {
  color: var(--neutral-gray);
}

@media (max-width: 768px) {
  .chart-summary {
    flex-direction: column;
    gap: 12px;
  }
  
  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .label, .value {
    display: inline;
    margin: 0;
  }
}
</style>
