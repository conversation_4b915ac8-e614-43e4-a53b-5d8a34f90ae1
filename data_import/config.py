"""
数据导入配置模块
统一管理所有配置参数，包括文件路径、数据库设置和功能开关
"""
import os
from datetime import datetime

# --- 项目路径配置 ---
# 获取脚本文件所在的目录（项目根目录）
SCRIPT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# Excel文件夹路径
EXCEL_FOLDER = os.path.join(SCRIPT_DIR, 'Excel文件夹')

# --- 数据库配置 ---
# SQLite数据库路径
DB_NAME = os.path.join(SCRIPT_DIR, 'backend/.wrangler/state/v3/d1/miniflare-D1DatabaseObject/69bae91604e81ab0cd3928711cd5f8ef7c0dd2d0549451f287350acede11328f.sqlite')

# Cloudflare D1数据库名称
D1_DB_NAME = 'chunxue-prod-db'

# --- 功能开关配置 ---
# 设置为True使用D1数据库，False使用本地SQLite
USE_D1_DATABASE = True

# 设置为True使用远程D1(生产环境)，False使用本地D1(开发环境)
USE_REMOTE_D1 = True

# --- Excel文件路径配置 ---
# 库存汇总表路径
INVENTORY_SUMMARY_PATH = os.path.join(EXCEL_FOLDER, '收发存汇总表查询.xlsx')

# 生产数据路径
PRODUCTION_PATH = os.path.join(EXCEL_FOLDER, '产成品入库列表.xlsx')

# 销售数据路径
SALES_PATH = os.path.join(EXCEL_FOLDER, '销售发票执行查询.xlsx')

# 调价表路径
PRICE_ADJUSTMENT_PATH = os.path.join(EXCEL_FOLDER, '调价表.xlsx')

# --- 业务日期配置 ---
# 库存数据的业务日期配置
# 重要说明：
# 1. 设置为None时，系统将自动根据Excel文件修改时间确定库存日期
# 2. 设置为具体日期时，使用固定的业务日期
# 3. 命令行参数 --inventory-date 可以临时覆盖此配置
INVENTORY_BUSINESS_DATE = None            # None=自动检测文件日期，或设置固定日期如'2025-07-28'

# 生产数据的业务日期配置（通常使用当前日期）
PRODUCTION_BUSINESS_DATE = None           # None表示使用当前日期

# 销售数据有自己的日期字段，不需要配置

# --- 数据处理配置 ---
# D1批量操作配置 - 性能优化版本
D1_BATCH_SIZE = 500         # 默认批处理大小（会根据表动态调整）
D1_MAX_RETRIES = 3          # 最大重试次数（快速失败策略）
D1_BATCH_DELAY = 0.1        # 批次间延迟(秒) - 减少延迟提升速度
D1_TIMEOUT = 45             # 命令超时时间(秒)

# 表特定的性能配置 - 优化以充分利用D1的1MB限制
TABLE_BATCH_CONFIGS = {
    'Products': {
        'batch_size': 800,       # 产品表记录小，可以更大批次
        'delay': 0.05,           # 最小延迟
        'target_size_kb': 900    # 使用90%的1MB限制
    },
    'DailyMetrics': {
        'batch_size': 10,        # 极小批次确保成功
        'delay': 0.5,            # 更长延迟
        'target_size_kb': 100    # 非常保守的大小限制
    },
    'PriceAdjustments': {
        'batch_size': 200,       # 大记录，保守批次
        'delay': 0.1,
        'target_size_kb': 900    # 使用90%的1MB限制
    }
}

# 性能监控配置
ENABLE_PERFORMANCE_MONITORING = True
SHOW_BATCH_SIZE_ESTIMATES = True

# 数据验证配置
INVENTORY_RANGE_MIN = 5000  # 库存最小值(吨)
INVENTORY_RANGE_MAX = 8000  # 库存最大值(吨)

# --- 日志配置 ---
LOG_LEVEL = 'INFO'
LOG_FORMAT = '%(asctime)s - %(levelname)s - %(message)s'

# --- 运行时配置 ---
def get_current_date():
    """获取当前日期字符串(YYYY-MM-DD格式)"""
    return datetime.now().strftime('%Y-%m-%d')

def get_inventory_date_from_file():
    """根据Excel文件修改时间确定库存日期"""
    try:
        if os.path.exists(INVENTORY_SUMMARY_PATH):
            mod_time = os.path.getmtime(INVENTORY_SUMMARY_PATH)
            file_date = datetime.fromtimestamp(mod_time).strftime('%Y-%m-%d')
            print(f"📁 检测到库存文件修改日期: {file_date}")
            return file_date
        else:
            print(f"⚠️  库存文件不存在: {INVENTORY_SUMMARY_PATH}")
            return get_current_date()
    except Exception as e:
        print(f"⚠️  获取文件修改时间失败: {e}")
        return get_current_date()

def validate_inventory_date(date_str):
    """验证库存日期的合理性"""
    try:
        from datetime import timedelta
        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
        current_date = datetime.now()
        
        # 检查日期不能超过当前日期太多（允许1天容差）
        if date_obj > current_date + timedelta(days=1):
            return False, f"库存日期不能是未来日期: {date_str}"
        
        # 检查日期不能太久远（不超过1年前）
        if date_obj < current_date - timedelta(days=365):
            return False, f"库存日期不能超过1年前: {date_str}"
            
        return True, "日期有效"
    except ValueError:
        return False, f"日期格式错误: {date_str}，应为YYYY-MM-DD格式"

def get_inventory_business_date():
    """智能获取库存数据的业务日期
    
    优先级：
    1. 手动配置的固定日期 (INVENTORY_BUSINESS_DATE不为None)
    2. Excel文件修改时间
    3. 当前系统日期（兜底方案）
    """
    # 1. 优先使用手动配置的固定日期
    if INVENTORY_BUSINESS_DATE:
        print(f"🔧 使用配置的固定库存日期: {INVENTORY_BUSINESS_DATE}")
        return INVENTORY_BUSINESS_DATE
    
    # 2. 使用Excel文件修改时间
    file_date = get_inventory_date_from_file()
    
    # 3. 验证日期合理性
    is_valid, message = validate_inventory_date(file_date)
    if is_valid:
        print(f"✅ 智能检测库存业务日期: {file_date}")
        return file_date
    else:
        print(f"⚠️  文件日期验证失败: {message}")
        # 4. 兜底使用当前日期
        current_date = get_current_date()
        print(f"🔄 使用当前系统日期作为兜底: {current_date}")
        return current_date

def get_production_business_date():
    """获取生产数据的业务日期"""
    return PRODUCTION_BUSINESS_DATE or get_current_date()

def get_database_mode():
    """获取数据库模式描述"""
    if USE_D1_DATABASE:
        return f"Cloudflare D1 ({'远程/生产环境' if USE_REMOTE_D1 else '本地/开发环境'})"
    else:
        return "Local SQLite"

# --- 验证配置 ---
def validate_config():
    """验证配置的有效性"""
    errors = []
    
    # 检查必要的目录是否存在
    if not os.path.exists(EXCEL_FOLDER):
        errors.append(f"Excel文件夹不存在: {EXCEL_FOLDER}")
    
    # 检查必要的Excel文件是否存在
    excel_files = [
        INVENTORY_SUMMARY_PATH,
        PRODUCTION_PATH,
        SALES_PATH
        # 注意：调价表文件可能不存在，所以不在必需文件列表中
    ]
    
    for file_path in excel_files:
        if not os.path.exists(file_path):
            errors.append(f"必需的Excel文件不存在: {file_path}")
    
    # 检查SQLite数据库文件(如果使用本地SQLite)
    if not USE_D1_DATABASE and not os.path.exists(DB_NAME):
        errors.append(f"SQLite数据库文件不存在: {DB_NAME}")
    
    return errors

# --- 配置信息打印 ---
def print_config_info():
    """打印当前配置信息"""
    print("=== 数据导入配置信息 ===")
    print(f"项目根目录: {SCRIPT_DIR}")
    print(f"Excel文件夹: {EXCEL_FOLDER}")
    print(f"数据库模式: {get_database_mode()}")
    print(f"当前系统日期: {get_current_date()}")
    print(f"库存业务日期: {get_inventory_business_date()}")
    print(f"生产业务日期: {get_production_business_date()}")
    print(f"D1批量大小: {D1_BATCH_SIZE}")
    print("========================")

if __name__ == "__main__":
    # 配置验证和信息打印
    print_config_info()
    
    errors = validate_config()
    if errors:
        print("\n⚠️ 配置验证发现问题:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("\n✅ 配置验证通过")