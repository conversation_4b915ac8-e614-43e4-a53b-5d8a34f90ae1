<template>
  <div class="base-card" :class="cardClasses">
    <!-- 卡片头部 -->
    <div v-if="title || $slots.header || $slots.extra" class="card-header">
      <div class="card-title-section">
        <h3 v-if="title" class="card-title">{{ title }}</h3>
        <slot name="header" />
      </div>
      <div v-if="$slots.extra" class="card-extra">
        <slot name="extra" />
      </div>
    </div>

    <!-- 卡片内容 -->
    <div class="card-body" :class="{ 'no-padding': noPadding }">
      <div v-if="loading" class="card-loading">
        <div class="loading-spinner"></div>
        <span class="loading-text">{{ loadingText }}</span>
      </div>
      <div v-else-if="error" class="card-error">
        <div class="error-icon">⚠️</div>
        <span class="error-text">{{ error }}</span>
        <button v-if="onRetry" @click="onRetry" class="retry-btn">重试</button>
      </div>
      <div v-else class="card-content">
        <slot />
      </div>
    </div>

    <!-- 卡片底部 -->
    <div v-if="$slots.footer" class="card-footer">
      <slot name="footer" />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  loading: {
    type: Boolean,
    default: false
  },
  loadingText: {
    type: String,
    default: '加载中...'
  },
  error: {
    type: String,
    default: ''
  },
  onRetry: {
    type: Function,
    default: null
  },
  noPadding: {
    type: Boolean,
    default: false
  },
  shadow: {
    type: String,
    default: 'normal', // 'none', 'small', 'normal', 'large'
    validator: (value) => ['none', 'small', 'normal', 'large'].includes(value)
  },
  bordered: {
    type: Boolean,
    default: true
  },
  hoverable: {
    type: Boolean,
    default: false
  }
})

const cardClasses = computed(() => ({
  [`shadow-${props.shadow}`]: props.shadow !== 'none',
  'bordered': props.bordered,
  'hoverable': props.hoverable,
  'loading': props.loading,
  'error': !!props.error
}))
</script>

<style scoped>
.base-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.base-card.bordered {
  border: 1px solid var(--border-light);
}

.base-card.shadow-small {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.base-card.shadow-normal {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.base-card.shadow-large {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.base-card.hoverable:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.card-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.card-title {
  font-size: 1.1em;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.card-extra {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-body {
  padding: 24px;
  min-height: 60px;
  position: relative;
}

.card-body.no-padding {
  padding: 0;
}

.card-content {
  position: relative;
}

.card-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--text-secondary);
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--border-light);
  border-top-color: var(--primary-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.loading-text {
  font-size: 0.9em;
}

.card-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--signal-red);
  text-align: center;
}

.error-icon {
  font-size: 2em;
  margin-bottom: 12px;
}

.error-text {
  font-size: 0.9em;
  margin-bottom: 16px;
}

.retry-btn {
  background: var(--signal-red);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.8em;
  cursor: pointer;
  transition: background 0.3s ease;
}

.retry-btn:hover {
  background: #b91c1c;
}

.card-footer {
  padding: 16px 24px 20px;
  border-top: 1px solid #f0f0f0;
  background: var(--background-light);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .card-body {
    padding: 16px;
  }
  
  .card-footer {
    padding: 12px 16px 16px;
  }
}
</style>
