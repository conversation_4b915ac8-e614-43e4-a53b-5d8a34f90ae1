/**
 * Date calculation utilities for dynamic date range handling
 */

/**
 * Calculate date range based on number of days
 * @param {number} days - Number of days to calculate range for
 * @returns {{start: string, end: string}} Object containing start and end dates in 'YYYY-MM-DD' format
 */
export function calculateDateRange(days) {
  const end = new Date();
  const start = new Date();
  start.setDate(end.getDate() - (days - 1));
  
  return {
    start: formatDateToYYYYMMDD(start),
    end: formatDateToYYYYMMDD(end)
  };
}

/**
 * Format date to 'YYYY-MM-DD' string
 * @param {Date} date - Date object to format
 * @returns {string} Formatted date string
 */
function formatDateToYYYYMMDD(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/**
 * Get the start and end dates of the current month
 * @returns {{start: string, end: string}} Object containing start and end dates of current month
 */
export function getCurrentMonthRange() {
  const now = new Date();
  const start = new Date(now.getFullYear(), now.getMonth(), 1);
  const end = new Date(now.getFullYear(), now.getMonth() + 1, 0);
  
  return {
    start: formatDateToYYYYMMDD(start),
    end: formatDateToYYYYMMDD(end)
  };
}

/**
 * Get the start and end dates of a specific month
 * @param {number} year - Year
 * @param {number} month - Month (1-12)
 * @returns {{start: string, end: string}} Object containing start and end dates of the month
 */
export function getMonthRange(year, month) {
  const start = new Date(year, month - 1, 1);
  const end = new Date(year, month, 0);
  
  return {
    start: formatDateToYYYYMMDD(start),
    end: formatDateToYYYYMMDD(end)
  };
}