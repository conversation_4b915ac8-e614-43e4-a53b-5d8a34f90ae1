<template>
  <div class="not-found-page">
    <div class="not-found-container">
      <div class="error-illustration">
        <div class="error-code">404</div>
        <div class="error-icon">🔍</div>
      </div>
      
      <div class="error-content">
        <h1 class="error-title">页面未找到</h1>
        <p class="error-description">
          抱歉，您访问的页面不存在或已被移动。
        </p>
        
        <div class="error-suggestions">
          <h3>您可以尝试：</h3>
          <ul>
            <li>检查网址是否正确</li>
            <li>返回首页重新导航</li>
            <li>使用搜索功能查找内容</li>
            <li>联系管理员获取帮助</li>
          </ul>
        </div>
        
        <div class="error-actions">
          <BaseButton
            @click="goHome"
            variant="primary"
            size="large"
            icon="🏠"
          >
            返回首页
          </BaseButton>
          
          <BaseButton
            @click="goBack"
            variant="secondary"
            size="large"
            icon="⬅️"
          >
            返回上页
          </BaseButton>
        </div>
      </div>
    </div>
    
    <!-- 快速导航 -->
    <div class="quick-navigation">
      <h3>快速导航</h3>
      <div class="nav-grid">
        <router-link
          v-for="route in quickNavRoutes"
          :key="route.name"
          :to="route.path"
          class="nav-item"
        >
          <div class="nav-icon">{{ route.meta.icon }}</div>
          <div class="nav-title">{{ route.meta.title }}</div>
          <div class="nav-description">{{ route.meta.description }}</div>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import BaseButton from '@/components/common/BaseButton.vue'

const router = useRouter()

// 快速导航路由
const quickNavRoutes = computed(() => {
  return router.getRoutes()
    .filter(route => route.meta?.requiresAuth && route.name !== 'NotFound')
    .slice(0, 6) // 只显示前6个
})

// 方法
const goHome = () => {
  router.push('/')
}

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}
</script>

<style scoped>
.not-found-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.not-found-container {
  max-width: 600px;
  text-align: center;
  background: white;
  border-radius: 20px;
  padding: 60px 40px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  margin-bottom: 40px;
}

.error-illustration {
  position: relative;
  margin-bottom: 40px;
}

.error-code {
  font-size: 8em;
  font-weight: 900;
  color: var(--primary-blue);
  line-height: 1;
  margin-bottom: 20px;
  text-shadow: 0 4px 8px rgba(0, 91, 172, 0.2);
}

.error-icon {
  font-size: 3em;
  opacity: 0.8;
}

.error-title {
  font-size: 2.5em;
  color: var(--text-primary);
  margin-bottom: 16px;
  font-weight: 700;
}

.error-description {
  font-size: 1.2em;
  color: var(--text-secondary);
  margin-bottom: 40px;
  line-height: 1.6;
}

.error-suggestions {
  text-align: left;
  margin-bottom: 40px;
  padding: 20px;
  background: var(--background-light);
  border-radius: 12px;
}

.error-suggestions h3 {
  color: var(--text-primary);
  margin-bottom: 16px;
  font-size: 1.1em;
}

.error-suggestions ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.error-suggestions li {
  padding: 8px 0;
  color: var(--text-secondary);
  position: relative;
  padding-left: 20px;
}

.error-suggestions li::before {
  content: '•';
  color: var(--primary-blue);
  position: absolute;
  left: 0;
  font-weight: bold;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.quick-navigation {
  max-width: 800px;
  width: 100%;
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.quick-navigation h3 {
  text-align: center;
  color: var(--text-primary);
  margin-bottom: 30px;
  font-size: 1.5em;
}

.nav-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.nav-item {
  display: block;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  text-decoration: none;
  transition: all 0.3s ease;
  background: white;
}

.nav-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-blue);
}

.nav-icon {
  font-size: 2em;
  margin-bottom: 12px;
}

.nav-title {
  font-size: 1.1em;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.nav-description {
  font-size: 0.9em;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .not-found-container {
    padding: 40px 20px;
  }
  
  .error-code {
    font-size: 6em;
  }
  
  .error-title {
    font-size: 2em;
  }
  
  .error-description {
    font-size: 1em;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .quick-navigation {
    padding: 20px;
  }
  
  .nav-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .not-found-page {
    padding: 20px 10px;
  }
  
  .error-code {
    font-size: 4em;
  }
  
  .error-title {
    font-size: 1.5em;
  }
}
</style>
