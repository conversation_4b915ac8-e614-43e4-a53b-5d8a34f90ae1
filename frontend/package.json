{"name": "spring-snow-frontend", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:staging": "vite build --mode staging", "build:check": "node scripts/build-check.js", "build:analyze": "npm run build && npm run build:check", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "deploy": "scripts/deploy.sh", "deploy:prod": "scripts/deploy.sh --commit-build", "clean": "rm -rf dist node_modules/.vite", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix", "format": "prettier --write src/"}, "dependencies": {"axios": "^1.6.2", "dayjs": "^1.11.10", "echarts": "^5.6.0", "pinia": "^2.1.7", "vue": "^3.4.0", "vue-router": "^4.2.5"}, "devDependencies": {"@types/node": "^24.1.0", "@vitejs/plugin-vue": "^5.0.0", "@vue/test-utils": "^2.4.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.17.0", "jsdom": "^26.1.0", "prettier": "^3.1.0", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vite": "^5.0.0", "vitest": "^1.0.0"}}