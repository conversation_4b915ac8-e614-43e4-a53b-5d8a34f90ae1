<template>
  <nav class="nav-container">
    <div class="nav-tabs">
      <router-link
        v-for="route in navigationRoutes"
        :key="route.path"
        :to="route.path"
        class="nav-tab"
        :class="{ active: $route.path === route.path }"
        :title="route.meta.title"
      >
        <span class="nav-icon">{{ route.meta.icon }}</span>
        <span class="nav-title">{{ route.meta.title }}</span>
      </router-link>
    </div>
  </nav>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

// 从路由配置中获取导航项
const navigationRoutes = computed(() => {
  return [
    { path: '/dashboard', meta: { title: '分析摘要', icon: '📊' } },
    { path: '/realtime', meta: { title: '实时分析', icon: '⚡' } },
    { path: '/inventory', meta: { title: '库存情况', icon: '📦' } },
    { path: '/production', meta: { title: '产销率分析', icon: '🏭' } },
    { path: '/sales', meta: { title: '销售情况', icon: '📈' } },
    { path: '/details', meta: { title: '详细数据', icon: '📋' } },
    { path: '/pricing', meta: { title: '价格波动', icon: '💰' } },
    { path: '/price-monitoring', meta: { title: '价格监控', icon: '⚠️' } },
    { path: '/inventory-turnover', meta: { title: '库存周转', icon: '🔄' } },
    { path: '/news', meta: { title: '桌创资讯', icon: '📰' } }
  ]
})
</script>

<style scoped>
.nav-container {
  margin-bottom: 32px;
}

.nav-tabs {
  display: flex;
  background: white;
  border-radius: 12px;
  padding: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  overflow-x: auto;
  gap: 4px;
}

.nav-tab {
  flex: 1;
  min-width: max-content;
  padding: 12px 16px;
  text-align: center;
  background: transparent;
  border: none;
  border-radius: 8px;
  color: #666;
  font-size: 0.9em;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.nav-icon {
  font-size: 1.1em;
  line-height: 1;
}

.nav-title {
  line-height: 1;
}

.nav-tab:hover {
  background: rgba(0, 91, 172, 0.05);
  color: var(--primary-blue);
  transform: translateY(-1px);
}

.nav-tab.active {
  background: var(--primary-blue);
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 91, 172, 0.3);
}

.nav-tab.active:hover {
  background: var(--primary-blue);
  color: white;
  transform: translateY(-1px);
}

/* 滚动条样式 */
.nav-tabs::-webkit-scrollbar {
  height: 4px;
}

.nav-tabs::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.nav-tabs::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 2px;
}

.nav-tabs::-webkit-scrollbar-thumb:hover {
  background: #999;
}

@media (max-width: 768px) {
  .nav-tabs {
    flex-direction: column;
    gap: 8px;
  }

  .nav-tab {
    flex: none;
    width: 100%;
    justify-content: flex-start;
    padding: 16px 20px;
  }

  .nav-icon {
    font-size: 1.2em;
  }

  .nav-title {
    font-size: 1em;
  }
}

@media (max-width: 1200px) {
  .nav-tabs {
    justify-content: flex-start;
  }
  
  .nav-tab {
    flex: none;
    min-width: 100px;
  }
}
</style>
