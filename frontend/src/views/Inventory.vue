<template>
  <div class="inventory-page">
    <!-- 库存概览卡片 -->
    <div class="inventory-overview">
      <div class="overview-cards">
        <MetricCard
          title="库存总量"
          :value="inventoryStore.totalInventory"
          unit="T"
          icon="📦"
          variant="primary"
          :loading="inventoryStore.isLoading"
          :format="formatInventoryValue"
        />
        <MetricCard
          title="TOP15占比"
          :value="inventoryStore.top15Percentage"
          unit="%"
          icon="📊"
          variant="secondary"
          :loading="inventoryStore.isLoading"
          :format="formatPercentageValue"
        />
        <MetricCard
          title="产品种类"
          :value="inventoryStore.top15Data.length"
          unit="种"
          icon="📋"
          variant="tertiary"
          :loading="inventoryStore.isLoading"
        />
        <MetricCard
          title="查询日期"
          :value="currentDate"
          icon="📅"
          variant="accent"
          :loading="inventoryStore.isLoading"
          :format="formatDisplayDate"
        />
      </div>
    </div>

    <!-- 库存图表区域 -->
    <div class="inventory-charts">
      <!-- 库存TOP15柱状图 -->
      <div class="chart-section">
        <InventoryBarChart
          :date="currentDate"
          @data-updated="handleInventoryDataUpdate"
        />
      </div>

      <!-- 库存TOP15饼图 -->
      <div class="chart-section">
        <InventoryPieChart
          :date="currentDate"
          @data-updated="handlePieDataUpdate"
        />
      </div>
    </div>

    <!-- 库存趋势图 -->
    <div class="inventory-trend">
      <InventoryTrendChart
        :start-date="trendDateRange.start"
        :end-date="trendDateRange.end"
      />
    </div>

    <!-- 库存明细表格 -->
    <div class="inventory-details">
      <InventoryDetailTable
        :date="currentDate"
        @export-data="handleExportData"
      />
    </div>

    <!-- 控制面板 -->
    <div class="inventory-controls">
      <div class="date-selector">
        <label>查询日期：</label>
        <input
          v-model="currentDate"
          type="date"
          @change="updateDate"
        />
      </div>

      <div class="trend-range-selector">
        <label>趋势范围：</label>
        <input
          v-model="trendDateRange.start"
          type="date"
          @change="updateTrendRange"
        />
        <span>至</span>
        <input
          v-model="trendDateRange.end"
          type="date"
          @change="updateTrendRange"
        />
      </div>

      <div class="control-buttons">
        <BaseButton
          @click="refreshData"
          :loading="inventoryStore.isLoading"
          variant="primary"
          size="small"
          icon="🔄"
        >
          刷新数据
        </BaseButton>

        <BaseButton
          @click="exportData"
          variant="secondary"
          size="small"
          icon="📊"
        >
          导出数据
        </BaseButton>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useInventoryStore } from '@/stores/inventory'
import { formatInventory, formatPercentage, formatDate } from '@/utils/formatters'
import { useDateRange } from '@/composables/useDateRange'
import MetricCard from '@/components/dashboard/MetricCard.vue'
import InventoryBarChart from '@/components/inventory/InventoryBarChart.vue'
import InventoryPieChart from '@/components/inventory/InventoryPieChart.vue'
import InventoryTrendChart from '@/components/inventory/InventoryTrendChart.vue'
import InventoryDetailTable from '@/components/inventory/InventoryDetailTable.vue'
import BaseButton from '@/components/common/BaseButton.vue'

const inventoryStore = useInventoryStore()
const { dateRange, ensureDateRangeLoaded } = useDateRange()

// 本地状态 - 使用动态计算的默认日期范围
const currentDate = ref(dateRange.value.end)
const trendDateRange = ref({
  start: dateRange.value.start,
  end: dateRange.value.end
})

// 格式化函数 - 无单位版本，避免重复显示
const formatInventoryValue = (value) => {
  return formatInventory(value, 'T', false) // 不显示单位
}

const formatPercentageValue = (value) => {
  return formatPercentage(value, 1, false) // 不显示单位
}
const formatDisplayDate = (value) => {
  return formatDate(value, 'MM月DD日')
}

// 方法
const updateDate = async () => {
  try {
    await inventoryStore.updateDate(currentDate.value)
  } catch (error) {
    console.error('Failed to update date:', error)
  }
}

const updateTrendRange = () => {
  // 趋势图会自动更新
  console.log('Trend range updated:', trendDateRange.value)
}

const refreshData = async () => {
  try {
    await inventoryStore.refreshData()
  } catch (error) {
    console.error('Failed to refresh inventory data:', error)
  }
}

const exportData = () => {
  // TODO: 实现数据导出功能
  console.log('Export inventory data')
}

const handleInventoryDataUpdate = (data) => {
  console.log('Inventory bar chart data updated:', data.length, 'items')
}

const handlePieDataUpdate = (data) => {
  console.log('Inventory pie chart data updated:', data.length, 'items')
}

const handleExportData = (data) => {
  console.log('Export data:', data.length, 'records')
}

// 生命周期
onMounted(async () => {
  try {
    // 确保日期范围已加载
    await ensureDateRangeLoaded()
    
    // 更新本地日期状态
    currentDate.value = dateRange.value.end
    trendDateRange.value = {
      start: dateRange.value.start,
      end: dateRange.value.end
    }
    
    // 总是获取最新的库存数据，确保summaryData被正确设置
    await inventoryStore.fetchAllInventoryData(currentDate.value)
  } catch (error) {
    console.error('Failed to load inventory data:', error)
  }
})
</script>

<style scoped>
.inventory-page {
  padding: 0;
}

.inventory-overview {
  margin-bottom: 32px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.inventory-charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.chart-section {
  min-height: 400px;
}

.inventory-trend {
  margin-bottom: 32px;
}

.inventory-details {
  margin-bottom: 32px;
}

.inventory-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: var(--background-light);
  border-radius: 12px;
  flex-wrap: wrap;
  gap: 16px;
}

.date-selector,
.trend-range-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.9em;
}

.date-selector label,
.trend-range-selector label {
  color: var(--text-secondary);
  font-weight: 500;
  white-space: nowrap;
}

.date-selector input,
.trend-range-selector input {
  padding: 6px 10px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.9em;
}

.date-selector input:focus,
.trend-range-selector input:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.control-buttons {
  display: flex;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .inventory-charts {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .overview-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .inventory-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .date-selector,
  .trend-range-selector {
    justify-content: center;
    flex-wrap: wrap;
  }

  .control-buttons {
    justify-content: center;
  }
}
</style>
