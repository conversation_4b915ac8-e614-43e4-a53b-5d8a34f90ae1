{"name": "backend", "version": "0.0.0", "private": true, "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "start": "wrangler dev --port 8787", "test": "vitest", "cf-typegen": "wrangler types", "import:inventory": "wrangler d1 execute DB --file=./scripts/import_inventory_data.ts", "get:categories": "wrangler dev scripts/get_categories.ts --experimental-vm"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.8.19", "@cloudflare/workers-types": "^4.20250711.0", "@types/node": "^24.1.0", "ts-node": "^10.9.2", "typescript": "^5.5.2", "vitest": "~3.2.0", "wrangler": "^4.21.0"}, "dependencies": {"@tsndr/cloudflare-worker-jwt": "^3.2.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.2", "express": "^5.1.0", "hono": "^4.8.3", "itty-router": "^5.0.18", "xlsx": "^0.18.5"}}