import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { fetchData } from '@/utils/api'
import { getDefaultDateRange } from '@/utils/constants'

export const useSalesStore = defineStore('sales', () => {
  // 状态
  const salesData = ref([])
  const salesPriceData = ref([])
  const isLoading = ref(false)
  const error = ref(null)
  const lastUpdated = ref(null)
  
  // 日期范围 - 使用动态计算的默认日期范围
  const defaultRange = getDefaultDateRange()
  const dateRange = ref({
    start: defaultRange.START,
    end: defaultRange.END
  })

  // 计算属性
  const totalSalesVolume = computed(() => {
    return salesPriceData.value.reduce((sum, item) => sum + (item.volume || 0), 0)
  })

  const totalSalesAmount = computed(() => {
    return salesPriceData.value.reduce((sum, item) => sum + (item.amount || 0), 0)
  })

  const averagePrice = computed(() => {
    if (salesPriceData.value.length === 0) return 0
    const totalPrice = salesPriceData.value.reduce((sum, item) => sum + (item.price || 0), 0)
    return totalPrice / salesPriceData.value.length
  })

  const latestPrice = computed(() => {
    return salesPriceData.value.length > 0 ? 
      salesPriceData.value[salesPriceData.value.length - 1]?.price || 0 : 0
  })

  const firstPrice = computed(() => {
    return salesPriceData.value.length > 0 ? 
      salesPriceData.value[0]?.price || 0 : 0
  })

  const priceChangeRate = computed(() => {
    if (firstPrice.value === 0) return 0
    return ((latestPrice.value - firstPrice.value) / firstPrice.value) * 100
  })

  const hasData = computed(() => salesData.value.length > 0 || salesPriceData.value.length > 0)

  // 方法
  const fetchSalesPriceData = async (startDate = dateRange.value.start, endDate = dateRange.value.end) => {
    isLoading.value = true
    error.value = null

    try {
      console.log('🔄 Fetching sales price data...')
      const response = await fetchData(`/api/trends/sales-price?start_date=${startDate}&end_date=${endDate}`)
      
      if (response && Array.isArray(response)) {
        salesPriceData.value = response.map(item => ({
          date: item.date,
          volume: parseFloat(item.volume) || 0,
          amount: parseFloat(item.amount) || 0,
          price: parseFloat(item.price) || 0
        }))
        
        // 更新日期范围
        dateRange.value = { start: startDate, end: endDate }
        
        console.log('✅ Sales price data loaded:', salesPriceData.value.length, 'records')
        return salesPriceData.value
      } else {
        throw new Error('Invalid data format')
      }
    } catch (err) {
      error.value = err.message || '获取销售价格数据失败'
      console.error('❌ Failed to fetch sales price data:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const fetchSalesData = async (startDate = dateRange.value.start, endDate = dateRange.value.end) => {
    try {
      console.log('🔄 Fetching sales data...')
      const response = await fetchData(`/api/sales?start_date=${startDate}&end_date=${endDate}`)
      
      if (response && Array.isArray(response)) {
        salesData.value = response.map(item => ({
          date: item.date,
          product_name: item.product_name,
          volume: parseFloat(item.volume) || 0,
          amount: parseFloat(item.amount) || 0,
          price: parseFloat(item.price) || 0
        }))
        
        console.log('✅ Sales data loaded:', salesData.value.length, 'records')
        return salesData.value
      } else {
        throw new Error('Invalid data format')
      }
    } catch (err) {
      console.error('❌ Failed to fetch sales data:', err)
      throw err
    }
  }

  const fetchAllSalesData = async (startDate = dateRange.value.start, endDate = dateRange.value.end) => {
    isLoading.value = true
    error.value = null

    try {
      // 并行获取数据
      const [priceData, detailData] = await Promise.all([
        fetchSalesPriceData(startDate, endDate),
        fetchSalesData(startDate, endDate)
      ])

      lastUpdated.value = new Date().toISOString()
      return { priceData, detailData }
    } catch (err) {
      error.value = err.message || '获取销售数据失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const refreshData = async () => {
    return await fetchAllSalesData(dateRange.value.start, dateRange.value.end)
  }

  const updateDateRange = async (startDate, endDate) => {
    dateRange.value = { start: startDate, end: endDate }
    return await fetchAllSalesData(startDate, endDate)
  }

  const clearData = () => {
    salesData.value = []
    salesPriceData.value = []
    error.value = null
    lastUpdated.value = null
  }

  const clearError = () => {
    error.value = null
  }

  // 获取特定日期的销售数据
  const getSalesByDate = (date) => {
    return salesData.value.filter(item => item.date === date)
  }

  // 获取特定产品的销售数据
  const getSalesByProduct = (productName) => {
    return salesData.value.filter(item => item.product_name === productName)
  }

  // 获取销售趋势（增长/下降）
  const getSalesTrend = () => {
    if (salesPriceData.value.length < 2) return 'stable'
    
    const recent = salesPriceData.value.slice(-7) // 最近7天
    const earlier = salesPriceData.value.slice(-14, -7) // 之前7天
    
    const recentAvg = recent.reduce((sum, item) => sum + item.volume, 0) / recent.length
    const earlierAvg = earlier.reduce((sum, item) => sum + item.volume, 0) / earlier.length
    
    if (recentAvg > earlierAvg * 1.05) return 'increasing'
    if (recentAvg < earlierAvg * 0.95) return 'decreasing'
    return 'stable'
  }

  return {
    // 状态
    salesData,
    salesPriceData,
    isLoading,
    error,
    lastUpdated,
    dateRange,
    
    // 计算属性
    totalSalesVolume,
    totalSalesAmount,
    averagePrice,
    latestPrice,
    firstPrice,
    priceChangeRate,
    hasData,
    
    // 方法
    fetchSalesPriceData,
    fetchSalesData,
    fetchAllSalesData,
    refreshData,
    updateDateRange,
    clearData,
    clearError,
    getSalesByDate,
    getSalesByProduct,
    getSalesTrend
  }
})
