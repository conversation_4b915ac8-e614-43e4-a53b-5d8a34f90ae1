# 务实的、可执行的项目优化方案

本方案旨在提供一份清晰、可操作的优化路线图，以解决当前项目中在性能、可维护性和健壮性方面最突出的问题。所有建议都遵循务实原则，优先考虑高投入产出比的修改，并避免不必要的大规模重构。

---

## 一、 数据导入脚本 (`data_import/`)

当前的数据导入脚本功能强大，但结构混乱、健壮性不足。以下优化将显著提高其可靠性和可维护性。

### 1. 优化数据导入的健壮性

*   **是什么 (What)**: 修改 `data_import/bulk_sql_import.py` 文件。
*   **为什么 (Why)**: 当前脚本采用“先删除后插入”的策略，但这两个操作不是原子性的。如果在删除旧数据后、插入新数据前脚本失败，将导致数据永久丢失。
*   **怎么做 (How)**:
    *   将 `DELETE` 和 `INSERT` 语句包裹在同一个SQL事务中。在生成的SQL文件开头添加 `BEGIN TRANSACTION;`，在文件末尾添加 `COMMIT;`。
    *   这样可以确保整个导入过程要么完全成功，要么在失败时完全回滚，保证数据完整性。

    **伪代码示例 (`generate_daily_metrics_sql`):**
    ```python
    def generate_daily_metrics_sql(daily_metrics_data, output_file):
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("BEGIN TRANSACTION;\n\n") # 开始事务

            # ... (生成 DELETE 语句) ...

            # ... (生成 INSERT 语句) ...

            f.write("\nCOMMIT;\n") # 提交事务
    ```

### 2. 简化并统一代码结构

*   **是什么 (What)**: 重构 `data_import/bulk_import_main.py`。
*   **为什么 (Why)**: 该文件目前存在对一个已废弃（或不存在）的 `daily_import_main.py` 的引用，这表明新旧代码逻辑耦合，增加了理解和维护的难度。
*   **怎么做 (How)**:
    *   移除所有对 `daily_import_main` 的 `import` 和函数调用。
    *   将 `main_api_import` 函数彻底移除或标记为 `@deprecated`，因为它依赖于旧的逻辑。
    *   确保 `bulk_import_main.py` 成为唯一的数据导入入口点，只专注于推荐的SQL文件导入模式。

### 3. 外部化硬编码的配置

*   **是什么 (What)**: 修改 `data_processors.py` 和 `config.py`。
*   **为什么 (Why)**: 数据处理逻辑中硬编码了大量的Excel列名（如 `物料名称`, `结存`）和过滤条件（如 `副产品`）。这使得脚本非常脆弱，一旦源Excel文件格式发生微小变化，脚本就会失败。
*   **怎么做 (How)**:
    *   在 `config.py` 中创建一个 `COLUMN_MAPPINGS` 字典，将业务逻辑中的字段名映射到Excel中的列名。
    *   在 `data_processors.py` 中，读取配置并使用映射后的列名来访问DataFrame，而不是直接使用硬编码的字符串。

    **示例 (`config.py`):**
    ```python
    COLUMN_MAPPINGS = {
        'inventory': {
            'product_name': '物料名称',
            'customer': '客户',
            'balance': '结存'
        },
        'sales': {
            'product_name': '物料名称',
            'quantity': '主数量',
            'customer': '客户名称'
        }
    }
    ```

---

## 二、 后端 API (`backend/`)

后端是整个系统的核心，当前的单体文件结构和SQL拼接方式带来了巨大的维护和安全风险。

### 1. 模块化拆分巨型文件

*   **是什么 (What)**: 拆分 `backend/src/index.ts`。
*   **为什么 (Why)**: 超过2800行的 `index.ts` 文件包含了所有API的路由、业务逻辑和数据处理，严重违反了关注点分离原则，使其几乎无法维护。
*   **怎么做 (How)**:
    *   创建新的目录 `backend/src/routes/`。
    *   根据业务领域（如 `inventory`, `production`, `sales`, `pricing`）将相关的路由和逻辑拆分到不同的文件中。例如，创建 `backend/src/routes/inventory.ts`、`backend/src/routes/production.ts` 等。
    *   在主文件 `index.ts` 中，仅保留Hono应用的初始化和路由的挂载逻辑。

    **示例 (`backend/src/index.ts`):**
    ```typescript
    import { Hono } from 'hono';
    import inventoryRoutes from './routes/inventory';
    import productionRoutes from './routes/production';

    const app = new Hono();
    app.route('/api/inventory', inventoryRoutes);
    app.route('/api/production', productionRoutes);

    export default app;
    ```

### 2. 统一使用ORM以消除SQL注入风险

*   **是什么 (What)**: 在整个后端项目中统一使用 Drizzle ORM。
*   **为什么 (Why)**: 当前代码大量使用字符串拼接来构建SQL查询，这是导致SQL注入漏洞的常见原因。虽然项目已经引入了Drizzle，但并未充分利用。
*   **怎么做 (How)**:
    *   重写 `ProductFilter` 类和所有直接执行SQL查询的地方，改用Drizzle的查询构建器。
    *   利用Drizzle的类型安全特性，确保所有数据库交互都是类型安全的。

    **示例 (重写 `ProductFilter`):**
    ```typescript
    import { sql } from 'drizzle-orm';
    import { products } from './schema'; // 假设的schema文件

    class ProductFilter {
      static getInventoryFilter() {
        return sql`${products.name} NOT LIKE '%鲜%' OR ${products.name} LIKE '%凤肠%'`;
      }
    }
    ```

### 3. 整合重复的业务逻辑

*   **是什么 (What)**: 重构 `ProductionRatioCalculator` 类及其调用方。
*   **为什么 (Why)**: 多个API端点（如 `/api/trends/ratio`, `/api/production/ratio-stats`）都包含获取和计算产销比的逻辑，存在代码重复和逻辑不一致的风险。
*   **怎么做 (How)**:
    *   将 `ProductionRatioCalculator` 提取到一个独立的业务逻辑文件，例如 `backend/src/services/productionService.ts`。
    *   确保所有相关的API路由都调用这个单一的服务来获取数据，而不是在路由处理函数中重复实现计算逻辑。

---

## 三、 前端应用 (`frontend/`)

前端的主要问题在于状态管理的混乱和数据获取的低效，导致潜在的数据不一致和性能问题。

### 1. 统一数据状态管理

*   **是什么 (What)**: 重构Pinia stores，特别是 `dashboard.js` 和 `production.js`。
*   **为什么 (Why)**: 多个Store独立获取和维护重叠的数据（如产销率），这不仅导致了冗余的API调用，还引发了UI上数据不一致的问题。
*   **怎么做 (How)**:
    *   让 `productionStore` 成为产销率数据的唯一权威来源。
    *   移除 `dashboardStore` 中所有关于产销率的状态和action。
    *   在 `Dashboard.vue` 组件中，直接从 `productionStore` 获取产销率数据。
    *   通过这种方式，确保整个应用中只有一个地方负责获取和管理产销率数据，从而保证数据的一致性。

### 2. 优化数据获取流程

*   **是什么 (What)**: 修改 `Dashboard.vue` 和其他视图组件的 `onMounted` 生命周期钩子。
*   **为什么 (Why)**: 组件在挂载时会触发多个Store的action，导致并行的、冗余的API请求。
*   **怎么做 (How)**:
    *   在顶层组件（如 `App.vue` 或布局组件）或路由守卫中，实现一个集中的初始化action。
    *   这个action负责调用各个Store，以协调的方式获取应用初始化所需的全部数据。
    *   在各个Store内部实现缓存逻辑，例如，检查 `lastUpdated` 时间戳，避免在短时间内重复请求相同的数据。

    **示例 (`productionStore`):**
    ```javascript
    const fetchAllProductionData = async (forceRefresh = false) => {
      if (!forceRefresh && !isDataStale.value) {
        console.log('✅ Using cached production data.');
        return;
      }
      // ... (执行数据获取逻辑) ...
    }