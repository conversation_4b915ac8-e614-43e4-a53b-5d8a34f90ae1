import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { fetchData } from '@/utils/api'
import { getDefaultDateRange } from '@/utils/constants'

export const useProductionStore = defineStore('production', () => {
  // 状态
  const productionData = ref([])
  const ratioData = ref([])
  const ratioStats = ref(null) // 新增：用于存储统计数据
  const isLoading = ref(false)
  const error = ref(null)
  const lastUpdated = ref(null)
  const consistencyWarning = ref(null) // 新增：数据一致性警告
  
  // 日期范围 - 使用动态计算的默认日期范围
  const defaultRange = getDefaultDateRange()
  const dateRange = ref({
    start: defaultRange.START,
    end: defaultRange.END
  })

  // 计算属性
  const totalProduction = computed(() => {
    // 后端现在已统一返回吨单位的数据，无需转换
    return ratioData.value.reduce((sum, item) => sum + (item.production_volume || 0), 0)
  })

  const totalSales = computed(() => {
    // 后端现在已统一返回吨单位的数据，无需转换
    return ratioData.value.reduce((sum, item) => sum + (item.sales_volume || 0), 0)
  })

  // 使用从后端获取的权威平均产销率
  const averageRatio = computed(() => ratioStats.value?.avg_ratio ?? 0)

  const balanceStatus = computed(() => {
    const ratio = averageRatio.value
    // 按照产销率逻辑：>=100%为消费型，<100%为积压型
    if (ratio >= 100) return '消费型' // 销量>=产量，库存消耗
    if (ratio < 100) return '积压型'  // 销量<产量，产品积压
    return '产销平衡'
  })

  const hasData = computed(() => productionData.value.length > 0 || ratioData.value.length > 0)

  // 数据是否过期（超过5分钟）
  const isDataStale = computed(() => {
    if (!lastUpdated.value) return true
    const now = new Date()
    const updated = new Date(lastUpdated.value)
    return (now - updated) > 5 * 60 * 1000 // 5分钟
  })

  // 方法
  const fetchProductionRatioData = async (startDate = dateRange.value.start, endDate = dateRange.value.end) => {
    // 这个函数现在获取明细数据和权威平均值
    console.log('🔄 [Store] fetchProductionRatioData called with:', { startDate, endDate })
    isLoading.value = true
    error.value = null
    try {
      console.log('🌐 [Store] Making API request to /api/trends/ratio...')
      const response = await fetchData(`/api/trends/ratio?start_date=${startDate}&end_date=${endDate}`)
      // --- 诊断日志开始 ---
      console.log('🔍 [Store] Raw response from /api/trends/ratio:', JSON.stringify(response, null, 2));
      // --- 诊断日志结束 ---
      
      // Check for consistency warnings from the API
      if (response && response.consistency_check && !response.consistency_check.is_consistent) {
        consistencyWarning.value = {
          message: '后端检测到数据一致性问题',
          details: `API报告的差异: ${response.consistency_check.difference.toFixed(2)}%`,
          difference: response.consistency_check.difference.toFixed(2),
          timestamp: new Date().toISOString(),
          source: 'trends/ratio API'
        };
        
        console.warn('⚠️ [Store] Backend reported data inconsistency:', response.consistency_check);
      }
      
      // Handle new unified response format
      if (response && response.daily_data && Array.isArray(response.daily_data)) {
        ratioData.value = response.daily_data.map(item => ({
          date: item.record_date || item.date,
          production_volume: parseFloat(item.daily_production) || 0,
          sales_volume: parseFloat(item.daily_sales) || 0,
          ratio: parseFloat(item.ratio) || 0
        }))
        
        // Update ratioStats with authoritative average from trends endpoint
        if (response.avg_ratio !== undefined) {
          if (!ratioStats.value) {
            ratioStats.value = {}
          }
          ratioStats.value.avg_ratio = response.avg_ratio
          console.log('🔍 [Store] Updated authoritative average from trends endpoint:', response.avg_ratio)
        }
        
        return ratioData.value
      } else if (response && Array.isArray(response)) {
        // Fallback for old response format
        ratioData.value = response.map(item => ({
          date: item.record_date || item.date,
          production_volume: parseFloat(item.daily_production) || 0,
          sales_volume: parseFloat(item.daily_sales) || 0,
          ratio: parseFloat(item.ratio) || 0
        }))
        return ratioData.value
      } else {
        throw new Error('Invalid data format for ratio details')
      }
    } catch (err) {
      error.value = err.message || '获取产销率明细失败'
      console.error('❌ Failed to fetch production ratio data:', err)
      ratioData.value = [] // 清空数据
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const fetchRatioStats = async (startDate = dateRange.value.start, endDate = dateRange.value.end) => {
    // 新增：专门获取统计数据的函数
    isLoading.value = true
    error.value = null
    try {
      const response = await fetchData(`/api/production/ratio-stats?start_date=${startDate}&end_date=${endDate}`)
      // --- 诊断日志开始 ---
      console.log('🔍 [Store] Raw response from /api/production/ratio-stats:', JSON.stringify(response, null, 2));
      // --- 诊断日志结束 ---
      
      // Check for consistency warnings from the API
      if (response && response.consistency_check && !response.consistency_check.is_consistent) {
        consistencyWarning.value = {
          message: '后端检测到数据一致性问题',
          details: `API报告的差异: ${response.consistency_check.difference.toFixed(2)}%`,
          difference: response.consistency_check.difference.toFixed(2),
          timestamp: new Date().toISOString(),
          source: 'ratio-stats API'
        };
        
        console.warn('⚠️ [Store] Backend reported data inconsistency:', response.consistency_check);
      }
      
      if (response) {
        ratioStats.value = response
        return ratioStats.value
      } else {
        throw new Error('Invalid data format for ratio stats')
      }
    } catch (err) {
      error.value = err.message || '获取产销率统计失败'
      console.error('❌ Failed to fetch ratio stats:', err)
      ratioStats.value = null // 清空数据
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const fetchProductionData = async (startDate = dateRange.value.start, endDate = dateRange.value.end) => {
    try {
      console.log('🔄 Fetching production data...')
      const response = await fetchData(`/api/production?start_date=${startDate}&end_date=${endDate}`)
      
      if (response && Array.isArray(response)) {
        productionData.value = response.map(item => ({
          date: item.date,
          product_name: item.product_name,
          production_volume: parseFloat(item.production_volume) || 0,
          sales_volume: parseFloat(item.sales_volume) || 0,
          ratio: parseFloat(item.ratio) || 0
        }))
        
        console.log('✅ Production data loaded:', productionData.value.length, 'records')
        return productionData.value
      } else {
        throw new Error('Invalid data format')
      }
    } catch (err) {
      console.error('❌ Failed to fetch production data:', err)
      
      // 使用模拟数据
      productionData.value = generateMockProductionData(startDate, endDate)
      return productionData.value
    }
  }

  // 新增：检查数据一致性
  const checkDataConsistency = () => {
    consistencyWarning.value = null
    
    // 如果没有数据，不进行检查
    if (!ratioStats.value || !ratioData.value || ratioData.value.length === 0) {
      return true;
    }
    
    // 从ratio-stats端点获取的权威平均值
    const statsRatio = ratioStats.value.avg_ratio;
    
    // 从trends/ratio端点获取的平均值（如果有）
    let trendsRatio = null;
    if (ratioData.value && ratioData.value.length > 0 && 'avg_ratio' in ratioData.value) {
      trendsRatio = ratioData.value.avg_ratio;
    }
    
    // 如果trends/ratio没有提供avg_ratio，则不进行比较
    if (trendsRatio === null) {
      return true;
    }
    
    // 比较两个值，允许0.1%的误差（浮点数计算可能有微小差异）
    const isConsistent = Math.abs(statsRatio - trendsRatio) < 0.1;
    
    if (!isConsistent) {
      consistencyWarning.value = {
        message: '检测到产销率数据不一致',
        details: `统计平均值: ${statsRatio.toFixed(2)}%, 趋势平均值: ${trendsRatio.toFixed(2)}%`,
        difference: Math.abs(statsRatio - trendsRatio).toFixed(2),
        timestamp: new Date().toISOString()
      };
      
      // 记录到控制台以便调试
      console.warn('⚠️ [数据一致性警告]', consistencyWarning.value);
      
      return false;
    }
    
    return true;
  }

  const fetchAllProductionData = async (startDate = dateRange.value.start, endDate = dateRange.value.end) => {
    isLoading.value = true
    error.value = null
    consistencyWarning.value = null
    dateRange.value = { start: startDate, end: endDate }

    try {
      // 并行获取所有数据
      const [ratioDetails, stats] = await Promise.all([
        fetchProductionRatioData(startDate, endDate),
        fetchRatioStats(startDate, endDate)
      ])

      // 检查数据一致性
      checkDataConsistency();
      
      lastUpdated.value = new Date().toISOString()
      return { ratioDetails, stats }
    } catch (err) {
      error.value = err.message || '获取生产数据失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const refreshData = async () => {
    return await fetchAllProductionData(dateRange.value.start, dateRange.value.end)
  }

  const updateDateRange = async (startDate, endDate) => {
    dateRange.value = { start: startDate, end: endDate }
    return await fetchAllProductionData(startDate, endDate)
  }

  const clearData = () => {
    productionData.value = []
    ratioData.value = []
    error.value = null
    lastUpdated.value = null
  }

  const clearError = () => {
    error.value = null
  }

  // 生成模拟数据
  const generateMockRatioData = (startDate, endDate) => {
    const mockData = []
    const start = new Date(startDate)
    const end = new Date(endDate)
    
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split('T')[0]
      const production = Math.random() * 500 + 300
      const sales = production * (0.8 + Math.random() * 0.4) // 80%-120%的产销率
      
      mockData.push({
        date: dateStr,
        production_volume: production,
        sales_volume: sales,
        ratio: (sales / production) * 100
      })
    }
    
    return mockData
  }

  const generateMockProductionData = (startDate, endDate) => {
    const mockData = []
    const products = ['产品A', '产品B', '产品C', '产品D', '产品E']
    const start = new Date(startDate)
    const end = new Date(endDate)
    
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split('T')[0]
      
      products.forEach(product => {
        const production = Math.random() * 100 + 50
        const sales = production * (0.8 + Math.random() * 0.4)
        
        mockData.push({
          date: dateStr,
          product_name: product,
          production_volume: production,
          sales_volume: sales,
          ratio: (sales / production) * 100
        })
      })
    }
    
    return mockData
  }

  // 获取特定日期的产销率
  const getRatioByDate = (date) => {
    return ratioData.value.find(item => item.date === date)
  }

  // 获取特定产品的产销数据
  const getProductionByProduct = (productName) => {
    return productionData.value.filter(item => item.product_name === productName)
  }

  // 获取产销率趋势（增长/下降）
  const getProductionTrend = () => {
    if (ratioData.value.length < 2) return 'stable'
    
    const recent = ratioData.value.slice(-7) // 最近7天
    const earlier = ratioData.value.slice(-14, -7) // 之前7天
    
    if (recent.length === 0 || earlier.length === 0) return 'stable'
    
    const recentAvg = recent.reduce((sum, item) => sum + item.ratio, 0) / recent.length
    const earlierAvg = earlier.reduce((sum, item) => sum + item.ratio, 0) / earlier.length
    
    if (recentAvg > earlierAvg * 1.05) return 'increasing'
    if (recentAvg < earlierAvg * 0.95) return 'decreasing'
    return 'stable'
  }

  return {
    // 状态
    productionData,
    ratioData,
    isLoading,
    error,
    lastUpdated,
    dateRange,
    consistencyWarning,
    
    // 计算属性
    totalProduction,
    totalSales,
    averageRatio,
    balanceStatus,
    hasData,
    isDataStale,
    
    // 方法
    fetchProductionRatioData,
    fetchProductionData,
    fetchAllProductionData,
    refreshData,
    updateDateRange,
    clearData,
    clearError,
    getRatioByDate,
    getProductionByProduct,
    getProductionTrend,
    checkDataConsistency
  }
})
