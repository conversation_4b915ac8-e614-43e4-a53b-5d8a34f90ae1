name = "spring-snow-api"
main = "src/index.ts"
compatibility_date = "2023-06-01"

# 变量配置
[vars]
JWT_SECRET = "spring-snow-jwt-secret-key-2024-very-secure-change-in-production"

# D1数据库配置
[[d1_databases]]
binding = "DB"
database_name = "chunxue-prod-db"
database_id = "0f2a500e-0865-47ac-a6d4-2f4af0051da3"
migrations_dir = "migrations"
migrations_table = "d1_migrations"

# 开发环境配置
[env.dev]
# 开发环境的D1数据库配置
[[env.dev.d1_databases]]
binding = "DB"
database_name = "chunxue-prod-db"
database_id = "0f2a500e-0865-47ac-a6d4-2f4af0051da3"