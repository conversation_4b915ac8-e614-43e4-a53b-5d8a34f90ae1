<template>
  <EChartsWrapper
    :options="chartOptions"
    :width="width"
    :height="height"
    :loading="loading"
    @chart-ready="onChartReady"
  />
</template>

<script setup>
import { computed } from 'vue'
import EChartsWrapper from './EChartsWrapper.vue'
import { getSparklineOption } from '@/utils/charts'
import { CHART_COLORS } from '@/utils/constants'

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '60px'
  },
  loading: {
    type: Boolean,
    default: false
  },
  color: {
    type: String,
    default: CHART_COLORS.PRIMARY
  },
  smooth: {
    type: Boolean,
    default: true
  },
  area: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['chart-ready'])

// 图表配置
const chartOptions = computed(() => {
  return getSparklineOption(props.data, props.color)
})

const onChartReady = (chart) => {
  emit('chart-ready', chart)
}
</script>
