import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'

// 导入全局样式
import './styles/main.css'

// 导入性能监控和错误处理
import performanceMonitor, { startMeasure, endMeasure } from './utils/performance'
import errorHandler from './utils/errorHandler'

// 开始应用启动性能测量
startMeasure('app-startup')

const app = createApp(App)

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  errorHandler.logError(err, {
    component: vm?.$options.name || 'Unknown',
    info,
    route: vm?.$route?.path
  })
}

// 全局警告处理
app.config.warnHandler = (msg, vm, trace) => {
  if (!import.meta.env.PROD) {
    console.warn(`Vue warning: ${msg}`, { component: vm?.$options.name, trace })
  }
}

app.use(createPinia())
app.use(router)

// 在应用挂载前预加载日期范围
async function initializeApp() {
  try {
    // 动态导入日期范围 store 并预加载数据
    const { useDateRangeStore } = await import('./stores/dateRange')
    const dateRangeStore = useDateRangeStore()
    
    console.log('🚀 Initializing app with dynamic date range...')
    await dateRangeStore.fetchAvailableDateRange()
    console.log('✅ Date range loaded successfully')
  } catch (error) {
    console.warn('⚠️ Failed to preload date range, will use fallback values:', error)
  } finally {
    // 无论是否成功加载日期范围，都要挂载应用
    app.mount('#app')
    
    // 结束应用启动性能测量
    endMeasure('app-startup')
  }
}

// 启动应用
initializeApp()

// 结束应用启动性能测量
endMeasure('app-startup')

// 页面加载完成后生成性能报告
window.addEventListener('load', () => {
  setTimeout(() => {
    const report = performanceMonitor.generateReport()
    console.log('📊 Application Performance Report:', report)
  }, 1000)
})

// 开发环境下的调试工具
if (!import.meta.env.PROD) {
  // 将工具挂载到全局对象，方便调试
  window.__DEBUG__ = {
    performanceMonitor,
    errorHandler,
    app
  }

  console.log('🛠️ Debug tools available at window.__DEBUG__')
}
