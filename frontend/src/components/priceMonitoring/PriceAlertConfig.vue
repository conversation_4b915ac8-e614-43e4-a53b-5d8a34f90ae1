<template>
  <BaseModal
    v-model:visible="localVisible"
    title="价格预警配置"
    width="600px"
    @close="handleClose"
  >
    <div class="alert-config-form">
      <form @submit.prevent="handleSave">
        <div class="form-group">
          <label for="configName" class="form-label">
            <i class="fas fa-tag"></i>
            配置名称
          </label>
          <input
            id="configName"
            v-model="formData.config_name"
            type="text"
            class="form-control"
            placeholder="输入配置名称，如：默认预警配置"
            required
          />
        </div>

        <div class="form-section">
          <h4>单日价格下降预警</h4>
          <div class="form-row">
            <div class="form-group">
              <label for="dailyDropThreshold" class="form-label">
                下降比例阈值 (%)
              </label>
              <input
                id="dailyDropThreshold"
                v-model.number="formData.daily_drop_threshold"
                type="number"
                class="form-control"
                min="0"
                max="100"
                step="0.01"
                placeholder="5.00"
              />
              <div class="form-help">
                当价格单日下降超过此比例时触发预警
              </div>
            </div>

            <div class="form-group">
              <label for="amountDropThreshold" class="form-label">
                下降金额阈值 (元/吨)
              </label>
              <input
                id="amountDropThreshold"
                v-model.number="formData.amount_drop_threshold"
                type="number"
                class="form-control"
                min="0"
                step="1"
                placeholder="200"
              />
              <div class="form-help">
                当价格单日下降超过此金额时触发预警
              </div>
            </div>
          </div>
        </div>

        <div class="form-section">
          <h4>连续价格下降预警</h4>
          <div class="form-group">
            <label for="consecutiveDays" class="form-label">
              连续下降天数阈值
            </label>
            <input
              id="consecutiveDays"
              v-model.number="formData.consecutive_days"
              type="number"
              class="form-control"
              min="2"
              max="30"
              step="1"
              placeholder="3"
            />
            <div class="form-help">
              当价格连续下降超过此天数时触发预警
            </div>
          </div>
        </div>

        <div class="form-section">
          <h4>价格波动异常预警</h4>
          <div class="form-group">
            <label for="volatilityThreshold" class="form-label">
              波动率阈值 (%)
            </label>
            <input
              id="volatilityThreshold"
              v-model.number="formData.volatility_threshold"
              type="number"
              class="form-control"
              min="0"
              max="100"
              step="0.01"
              placeholder="15.00"
            />
            <div class="form-help">
              当价格波动率超过此阈值时触发预警
            </div>
          </div>
        </div>

        <div class="form-section">
          <div class="form-group">
            <label class="checkbox-label">
              <input
                v-model="formData.is_active"
                type="checkbox"
                class="form-checkbox"
              />
              <span class="checkmark"></span>
              启用此配置
            </label>
            <div class="form-help">
              选中后此配置将立即生效
            </div>
          </div>
        </div>

        <div class="preview-section">
          <h4>预警预览</h4>
          <div class="preview-grid">
            <div class="preview-item">
              <span class="preview-label">单日下降超过:</span>
              <span class="preview-value">
                {{ formData.daily_drop_threshold }}% 或 {{ formData.amount_drop_threshold }}元/吨
              </span>
            </div>
            <div class="preview-item">
              <span class="preview-label">连续下降超过:</span>
              <span class="preview-value">{{ formData.consecutive_days }}天</span>
            </div>
            <div class="preview-item">
              <span class="preview-label">波动率超过:</span>
              <span class="preview-value">{{ formData.volatility_threshold }}%</span>
            </div>
            <div class="preview-item">
              <span class="preview-label">配置状态:</span>
              <span class="preview-value" :class="{ 'active': formData.is_active }">
                {{ formData.is_active ? '启用' : '禁用' }}
              </span>
            </div>
          </div>
        </div>
      </form>
    </div>

    <template #footer>
      <div class="modal-footer">
        <button
          type="button"
          class="btn btn-secondary"
          @click="handleClose"
        >
          取消
        </button>
        <button
          type="button"
          class="btn btn-primary"
          @click="handleSave"
          :disabled="!isFormValid || saving"
        >
          <i v-if="saving" class="fas fa-spinner fa-spin"></i>
          <i v-else class="fas fa-save"></i>
          {{ saving ? '保存中...' : '保存配置' }}
        </button>
      </div>
    </template>
  </BaseModal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import BaseModal from '@/components/common/BaseModal.vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  config: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:visible', 'save-config'])

// 响应式数据
const localVisible = ref(props.visible)
const saving = ref(false)

const formData = reactive({
  config_name: '默认预警配置',
  daily_drop_threshold: 5.0,
  amount_drop_threshold: 200,
  consecutive_days: 3,
  volatility_threshold: 15.0,
  is_active: true
})

// 计算属性
const isFormValid = computed(() => {
  return formData.config_name.trim() !== '' &&
         formData.daily_drop_threshold >= 0 &&
         formData.amount_drop_threshold >= 0 &&
         formData.consecutive_days >= 2 &&
         formData.volatility_threshold >= 0
})

// 方法
const handleClose = () => {
  localVisible.value = false
  emit('update:visible', false)
}

const handleSave = async () => {
  if (!isFormValid.value) return

  saving.value = true
  
  try {
    // 转换数据格式
    const configData = {
      ...formData,
      daily_drop_threshold: formData.daily_drop_threshold / 100, // 转换为小数
      volatility_threshold: formData.volatility_threshold / 100
    }

    emit('save-config', configData)
    
    // 等待一点时间显示保存状态
    await new Promise(resolve => setTimeout(resolve, 500))
    
    handleClose()
  } catch (error) {
    console.error('保存配置失败:', error)
  } finally {
    saving.value = false
  }
}

const resetForm = () => {
  Object.assign(formData, {
    config_name: '默认预警配置',
    daily_drop_threshold: 5.0,
    amount_drop_threshold: 200,
    consecutive_days: 3,
    volatility_threshold: 15.0,
    is_active: true
  })
}

const loadConfig = (config) => {
  if (config && Object.keys(config).length > 0) {
    Object.assign(formData, {
      config_name: config.config_name || '默认预警配置',
      daily_drop_threshold: (config.daily_drop_threshold || 0.05) * 100, // 转换为百分比
      amount_drop_threshold: config.amount_drop_threshold || 200,
      consecutive_days: config.consecutive_days || 3,
      volatility_threshold: (config.volatility_threshold || 0.15) * 100,
      is_active: config.is_active !== undefined ? Boolean(config.is_active) : true
    })
  } else {
    resetForm()
  }
}

// 监听器
watch(() => props.visible, (newVal) => {
  localVisible.value = newVal
  if (newVal) {
    loadConfig(props.config)
  }
})

watch(() => props.config, (newVal) => {
  if (props.visible) {
    loadConfig(newVal)
  }
}, { deep: true })
</script>

<style scoped>
.alert-config-form {
  max-height: 70vh;
  overflow-y: auto;
  padding: 4px;
}

.form-group {
  margin-bottom: 20px;
}

.form-section {
  margin-bottom: 32px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #005BAC;
}

.form-section h4 {
  margin: 0 0 16px 0;
  color: #005BAC;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #005BAC;
  box-shadow: 0 0 0 2px rgba(0, 91, 172, 0.1);
}

.form-help {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  line-height: 1.4;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.form-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.checkmark {
  position: relative;
}

.preview-section {
  background: #e8f4fd;
  padding: 20px;
  border-radius: 6px;
  border: 1px solid #b3d8f0;
}

.preview-section h4 {
  margin: 0 0 16px 0;
  color: #005BAC;
  font-size: 16px;
  font-weight: 600;
}

.preview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}

.preview-label {
  font-size: 13px;
  color: #666;
}

.preview-value {
  font-size: 13px;
  font-weight: 600;
  color: #333;
}

.preview-value.active {
  color: #52c41a;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 0 0 0;
  border-top: 1px solid #e9ecef;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #005BAC;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #004494;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }

  .preview-grid {
    grid-template-columns: 1fr;
  }

  .modal-footer {
    flex-direction: column-reverse;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }
}
</style>