# 数据导入指南

## 流程重构概述

为了提升数据管理的稳定性和准确性，我们对原有的数据导入流程进行了重构。本次优化的核心目标是将产品基础信息管理与价格数据导入彻底分离。

**改进优势**:
- **杜绝分类覆盖风险**：彻底解决了因使用不同版本的Excel文件而意外覆盖正确产品分类的问题。
- **保证数据一致性**：通过集中的产品目录管理，确保了产品信息在系统中的唯一性和准确性。
- **流程清晰化**：将复杂的导入流程拆分为两个独立的、目标明确的步骤，降低了操作失误的可能性。

---

## 新数据管理流程

新的流程分为两个核心步骤：产品目录管理和价格数据导入。

### 第一步：产品目录管理

产品的所有基础信息（包括ID、名称、分类等）现在统一由一个文件管理，作为系统内产品信息的“唯一事实来源”。

- **核心文件**: [`backend/scripts/products.json`](backend/scripts/products.json)

- **操作指南**:
  1.  **添加、修改或删除产品**：直接编辑 [`backend/scripts/products.json`](backend/scripts/products.json) 文件。请确保每个产品的 `id`, `name`, 和 `category` 字段都正确无误。
  2.  **同步到数据库**：完成编辑后，必须运行以下命令将更改同步到数据库中。此操作会更新产品目录，确保后续的价格导入能正确匹配。

      ```bash
      npx ts-node backend/scripts/import_products.ts
      ```

### 第二步：价格数据导入

价格数据导入现在是一个独立流程，仅负责处理价格信息。

- **核心脚本**: [`backend/scripts/import_price_data.ts`](backend/scripts/import_price_data.ts)
- **数据源**: `Excel文件夹/调价表.xlsx`

- **操作指南**:
  1.  **前置条件**: 在运行价格导入之前，请务必确认所有涉及的产品都已经在 [`products.json`](backend/scripts/products.json) 中定义，并且已经通过第一步的脚本同步到了数据库。如果脚本在《调价表.xlsx》中遇到一个不存在于数据库的产品ID，该产品的价格记录将被忽略。
  2.  **脚本功能**: 此脚本会读取《调价表.xlsx》中的产品ID和对应的价格，并更新到数据库。它会**完全忽略**Excel文件中的任何产品分类信息。
  3.  **运行命令**:

      ```bash
      npx ts-node backend/scripts/import_price_data.ts
      ```

---


---
**文档版本**: v1.0
**最后更新**: 2025-07-30
**负责人**: AI Assistant
