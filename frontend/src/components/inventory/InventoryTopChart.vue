<template>
  <BaseCard title="库存TOP15产品" :loading="loading" :error="error">
    <template #extra>
      <div class="chart-controls">
        <select v-model="displayLimit" @change="updateLimit" class="limit-selector">
          <option value="10">TOP10</option>
          <option value="15">TOP15</option>
          <option value="20">TOP20</option>
        </select>

        <select v-model="sortOrder" @change="updateSort" class="sort-selector">
          <option value="desc">降序</option>
          <option value="asc">升序</option>
        </select>
      </div>
    </template>

    <div v-if="!loading && chartData.length > 0" class="chart-content">
      <BarChart
        :data="chartData"
        x-field="name"
        y-field="value"
        :loading="loading"
        :height="height"
        :show-label="true"
        :label-formatter="labelFormatter"
        :colors="[chartColor]"
        @chart-ready="onChartReady"
        @chart-click="onChartClick"
      />

      <div class="inventory-summary">
        <div class="summary-item">
          <span class="label">TOP{{ displayLimit }}总量：</span>
          <span class="value">{{ formatInventory(topTotal) }}</span>
        </div>
        <div class="summary-item">
          <span class="label">占总库存：</span>
          <span class="value">{{ formatPercentage(topPercentage) }}</span>
        </div>
        <div class="summary-item">
          <span class="label">最高库存：</span>
          <span class="value">{{ formatInventory(maxInventory) }}</span>
        </div>
        <div class="summary-item">
          <span class="label">最低库存：</span>
          <span class="value">{{ formatInventory(minInventory) }}</span>
        </div>
      </div>
    </div>
    <div v-else-if="!loading && chartData.length === 0" class="empty-state">
      <div class="empty-icon">📊</div>
      <p>在选定日期内没有可用的库存数据。</p>
    </div>
  </BaseCard>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useInventoryStore } from '@/stores/inventory'
import { formatInventory, formatPercentage } from '@/utils/formatters'
import { CHART_COLORS } from '@/utils/constants'
import BaseCard from '@/components/common/BaseCard.vue'
import BarChart from '@/components/charts/BarChart.vue'

const props = defineProps({
  date: {
    type: String,
    default: '2025-07-26'
  },
  height: {
    type: String,
    default: '400px'
  }
})

const emit = defineEmits(['chart-ready', 'chart-click', 'data-updated'])

const inventoryStore = useInventoryStore()
const loading = ref(false)
const error = ref('')
const displayLimit = ref(15)
const sortOrder = ref('desc')

// 计算属性
const chartData = computed(() => {
  let data = [...inventoryStore.top15Data]

  // 排序
  data.sort((a, b) => {
    return sortOrder.value === 'desc' ? b.value - a.value : a.value - b.value
  })

  // 限制显示数量
  data = data.slice(0, displayLimit.value)

  return data.map(item => ({
    name: item.name,
    value: item.value
  }))
})

const topTotal = computed(() => {
  return chartData.value.reduce((sum, item) => sum + item.value, 0)
})

const topPercentage = computed(() => {
  if (inventoryStore.totalInventory === 0) return 0
  return (topTotal.value / inventoryStore.totalInventory) * 100
})

const maxInventory = computed(() => {
  if (chartData.value.length === 0) return 0
  return Math.max(...chartData.value.map(item => item.value))
})

const minInventory = computed(() => {
  if (chartData.value.length === 0) return 0
  return Math.min(...chartData.value.map(item => item.value))
})

const chartColor = computed(() => {
  return CHART_COLORS.PRIMARY
})

// 标签格式化
const labelFormatter = (params) => {
  return formatInventory(params.value)
}

// 方法
const fetchData = async () => {
  loading.value = true
  error.value = ''

  try {
    await inventoryStore.fetchInventoryTop15(props.date, Math.max(20, displayLimit.value)) // 确保获取足够的数据
    emit('data-updated', inventoryStore.top15Data)
  } catch (err) {
    error.value = '数据加载失败'
    console.error('Failed to load inventory top15 data:', err)
  } finally {
    loading.value = false
  }
}

const updateLimit = async () => {
  console.log('Display limit updated to:', displayLimit.value)
  // 当切换到更大的限制时，重新获取数据以确保有足够的项目
  if (displayLimit.value > inventoryStore.top15Data.length) {
    await fetchData()
  }
}

const updateSort = () => {
  console.log('Sort order updated to:', sortOrder.value)
}

const onChartReady = (chart) => {
  emit('chart-ready', chart)
}

const onChartClick = (params) => {
  emit('chart-click', params)
}

// 监听日期变化
watch(() => props.date, () => {
  fetchData()
})

onMounted(() => {
  fetchData()
})

// 暴露方法
defineExpose({
  refresh: fetchData
})
</script>

<style scoped>
.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400px;
  color: var(--text-secondary);
  background-color: var(--background-light);
  border-radius: 8px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.chart-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.limit-selector,
.sort-selector {
  padding: 6px 12px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.9em;
  background: white;
  cursor: pointer;
}

.limit-selector:focus,
.sort-selector:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.inventory-summary {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  padding: 16px;
  background: var(--background-light);
  border-radius: 8px;
  border-top: 1px solid #f0f0f0;
}

.summary-item {
  text-align: center;
  flex: 1;
}

.label {
  display: block;
  color: var(--text-secondary);
  font-size: 0.85em;
  margin-bottom: 4px;
}

.value {
  display: block;
  color: var(--primary-blue);
  font-weight: 600;
  font-size: 1em;
}

@media (max-width: 768px) {
  .chart-controls {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .inventory-summary {
    flex-direction: column;
    gap: 12px;
  }

  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .label, .value {
    display: inline;
    margin: 0;
  }
}
</style>
