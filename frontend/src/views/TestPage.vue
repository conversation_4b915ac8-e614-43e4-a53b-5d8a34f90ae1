<template>
  <div class="test-page">
    <BaseCard title="系统功能测试" :loading="false">
      <div class="test-sections">
        <!-- 组件测试 -->
        <section class="test-section">
          <h3>🧩 组件测试</h3>
          <div class="test-grid">
            <div class="test-item">
              <h4>按钮组件</h4>
              <div class="button-tests">
                <BaseButton variant="primary" size="small">主要按钮</BaseButton>
                <BaseButton variant="secondary" size="medium">次要按钮</BaseButton>
                <BaseButton variant="ghost" size="large" :loading="true">加载中</BaseButton>
              </div>
            </div>
            
            <div class="test-item">
              <h4>指标卡片</h4>
              <MetricCard
                title="测试指标"
                :value="1234.56"
                unit="T"
                icon="📊"
                variant="primary"
                :format="formatNumber"
              />
            </div>
          </div>
        </section>

        <!-- 图表测试 -->
        <section class="test-section">
          <h3>📊 图表测试</h3>
          <div class="chart-tests">
            <div class="chart-test">
              <h4>折线图</h4>
              <LineChart
                :data="mockLineData"
                x-field="date"
                y-field="value"
                height="200px"
              />
            </div>
            
            <div class="chart-test">
              <h4>柱状图</h4>
              <BarChart
                :data="mockBarData"
                x-field="name"
                y-field="value"
                height="200px"
              />
            </div>
            
            <div class="chart-test">
              <h4>饼图</h4>
              <PieChart
                :data="mockPieData"
                name-field="name"
                value-field="value"
                height="200px"
              />
            </div>
          </div>
        </section>

        <!-- 状态管理测试 -->
        <section class="test-section">
          <h3>🗃️ 状态管理测试</h3>
          <div class="state-tests">
            <div class="state-test">
              <h4>认证状态</h4>
              <p>登录状态: {{ authStore.isAuthenticated ? '已登录' : '未登录' }}</p>
              <p>用户信息: {{ authStore.user?.username || '无' }}</p>
            </div>
            
            <div class="state-test">
              <h4>仪表板状态</h4>
              <p>数据加载: {{ dashboardStore.isLoading ? '加载中' : '已完成' }}</p>
              <p>总产品数: {{ dashboardStore.totalProducts }}</p>
            </div>
          </div>
        </section>

        <!-- API测试 -->
        <section class="test-section">
          <h3>🌐 API测试</h3>
          <div class="api-tests">
            <BaseButton @click="testApiCall" :loading="apiTesting">
              测试API调用
            </BaseButton>
            <div v-if="apiResult" class="api-result">
              <h4>API响应:</h4>
              <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
            </div>
          </div>
        </section>

        <!-- 性能测试 -->
        <section class="test-section">
          <h3>⚡ 性能测试</h3>
          <div class="performance-tests">
            <div class="perf-metric">
              <span class="label">页面加载时间:</span>
              <span class="value">{{ loadTime }}ms</span>
            </div>
            <div class="perf-metric">
              <span class="label">内存使用:</span>
              <span class="value">{{ memoryUsage }}MB</span>
            </div>
            <div class="perf-metric">
              <span class="label">组件数量:</span>
              <span class="value">{{ componentCount }}</span>
            </div>
          </div>
        </section>

        <!-- 响应式测试 -->
        <section class="test-section">
          <h3>📱 响应式测试</h3>
          <div class="responsive-tests">
            <div class="viewport-info">
              <p>当前视口: {{ viewportSize.width }}x{{ viewportSize.height }}</p>
              <p>设备类型: {{ deviceType }}</p>
            </div>
          </div>
        </section>
      </div>
    </BaseCard>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useDashboardStore } from '@/stores/dashboard'
import { fetchData } from '@/utils/api'
import { formatNumber } from '@/utils/formatters'
import BaseCard from '@/components/common/BaseCard.vue'
import BaseButton from '@/components/common/BaseButton.vue'
import MetricCard from '@/components/dashboard/MetricCard.vue'
import LineChart from '@/components/charts/LineChart.vue'
import BarChart from '@/components/charts/BarChart.vue'
import PieChart from '@/components/charts/PieChart.vue'

const authStore = useAuthStore()
const dashboardStore = useDashboardStore()

// 测试数据
const apiTesting = ref(false)
const apiResult = ref(null)
const loadTime = ref(0)
const memoryUsage = ref(0)
const componentCount = ref(0)
const viewportSize = ref({ width: 0, height: 0 })

// 模拟图表数据
const mockLineData = ref([
  { date: '2025-01-01', value: 100 },
  { date: '2025-01-02', value: 120 },
  { date: '2025-01-03', value: 110 },
  { date: '2025-01-04', value: 140 },
  { date: '2025-01-05', value: 130 }
])

const mockBarData = ref([
  { name: '产品A', value: 300 },
  { name: '产品B', value: 250 },
  { name: '产品C', value: 400 },
  { name: '产品D', value: 180 }
])

const mockPieData = ref([
  { name: '类别1', value: 35 },
  { name: '类别2', value: 25 },
  { name: '类别3', value: 20 },
  { name: '类别4', value: 20 }
])

// 计算属性
const deviceType = computed(() => {
  const width = viewportSize.value.width
  if (width < 768) return '移动设备'
  if (width < 1024) return '平板设备'
  return '桌面设备'
})

// 方法
const testApiCall = async () => {
  apiTesting.value = true
  try {
    const result = await fetchData('/api/summary')
    apiResult.value = result
  } catch (error) {
    apiResult.value = { error: error.message }
  } finally {
    apiTesting.value = false
  }
}

const updateViewportSize = () => {
  viewportSize.value = {
    width: window.innerWidth,
    height: window.innerHeight
  }
}

const measurePerformance = () => {
  // 页面加载时间
  if (performance.timing) {
    loadTime.value = performance.timing.loadEventEnd - performance.timing.navigationStart
  }
  
  // 内存使用（如果支持）
  if (performance.memory) {
    memoryUsage.value = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)
  }
  
  // 组件数量（估算）
  componentCount.value = document.querySelectorAll('[data-v-]').length
}

onMounted(() => {
  updateViewportSize()
  measurePerformance()
  
  window.addEventListener('resize', updateViewportSize)
  
  // 清理
  return () => {
    window.removeEventListener('resize', updateViewportSize)
  }
})
</script>

<style scoped>
.test-page {
  padding: 0;
}

.test-sections {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.test-section {
  border: 1px solid var(--border-light);
  border-radius: 12px;
  padding: 24px;
  background: white;
}

.test-section h3 {
  color: var(--text-primary);
  margin-bottom: 20px;
  font-size: 1.2em;
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.test-item {
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background: #fafafa;
}

.test-item h4 {
  margin-bottom: 12px;
  color: var(--text-primary);
}

.button-tests {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.chart-tests {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.chart-test {
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background: white;
}

.chart-test h4 {
  margin-bottom: 12px;
  color: var(--text-primary);
}

.state-tests {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.state-test {
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background: #fafafa;
}

.state-test h4 {
  margin-bottom: 12px;
  color: var(--text-primary);
}

.api-tests {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.api-result {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.api-result pre {
  font-size: 0.8em;
  color: var(--text-secondary);
  white-space: pre-wrap;
  word-break: break-all;
}

.performance-tests {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.perf-metric {
  display: flex;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.label {
  color: var(--text-secondary);
  font-weight: 500;
}

.value {
  color: var(--text-primary);
  font-weight: 600;
}

.responsive-tests {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.viewport-info p {
  margin-bottom: 8px;
  color: var(--text-secondary);
}

@media (max-width: 768px) {
  .test-grid,
  .chart-tests,
  .state-tests {
    grid-template-columns: 1fr;
  }
  
  .button-tests {
    flex-direction: column;
  }
  
  .performance-tests {
    grid-template-columns: 1fr;
  }
}
</style>
