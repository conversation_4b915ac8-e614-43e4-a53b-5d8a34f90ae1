import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'

// 创建 axios 实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 增加到30秒
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'application/json',
  }
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    console.log('🌐 API Request:', config.method?.toUpperCase(), config.url)
    return config
  },
  (error) => {
    console.error('❌ Request Error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    console.log('✅ API Response:', response.status, response.config.url)
    return response
  },
  async (error) => {
    const config = error.config;

    // 重试逻辑
    if (error.code === 'ECONNABORTED' && config && !config._retry) {
      config._retry = true;
      config.timeout = 45000; // 重试时增加超时时间

      console.warn('⚠️ Request timeout, retrying with extended timeout...');
      return apiClient.request(config);
    }

    console.error('❌ Response Error:', error.response?.status, error.message);
    return Promise.reject(error);
  }
)

// API 方法
export const api = {
  // 获取仪表板数据
  getDashboardData: () => apiClient.get('/api/dashboard'),
  getSummaryData: (startDate, endDate) => 
    apiClient.get(`/api/dashboard/summary?start_date=${startDate}&end_date=${endDate}`),

  // 获取销售数据
  getSalesData: () => apiClient.get('/api/sales'),
  getSalesDetails: () => apiClient.get('/api/sales/details'),
  getSalesPriceTrends: (startDate, endDate) => 
    apiClient.get(`/api/trends/sales-price?start_date=${startDate}&end_date=${endDate}`),

  // 获取库存数据
  getInventoryData: () => apiClient.get('/api/inventory'),
  getInventoryTop15: (date, limit = 15) => 
    apiClient.get(`/api/inventory/top?date=${date}&limit=${limit}`),
  getInventorySummary: (date) => 
    apiClient.get(`/api/inventory/summary?date=${date}`),

  // 获取产销率数据
  getProductionData: () => apiClient.get('/api/production'),
  getProductionRatio: (startDate, endDate) => 
    apiClient.get(`/api/trends/ratio?start_date=${startDate}&end_date=${endDate}`),

  // 获取价格数据
  getPriceData: () => apiClient.get('/api/price'),
  getPriceChanges: () => apiClient.get('/api/price/changes'),
  getPricesHistory: (params) => apiClient.get('/api/prices/history', { params }),
  getPricingTrends: (params) => apiClient.get('/api/prices/trends', { params }),
  getPricingAlerts: (params) => apiClient.get('/api/prices/alerts', { params }),

  // 价格监控
  getKeyProducts: () => apiClient.get('/api/prices/key-products'),
  getPriceTrends: (productName, period) => apiClient.get(`/api/prices/trends`, { params: { productName, period } }),
  getSystemAlerts: (params) => apiClient.get('/api/prices/system-alerts', { params }),

  // 价格预警
  getAlerts: () => apiClient.get('/api/alerts'),
  createAlert: (data) => apiClient.post('/api/alerts', data),
  updateAlert: (id, data) => apiClient.put(`/api/alerts/${id}`, data),
  deleteAlert: (id) => apiClient.delete(`/api/alerts/${id}`),
 
   // 用户认证
   login: (credentials) => apiClient.post('/api/login', credentials),
  register: (userData) => apiClient.post('/api/register', userData),
  logout: () => apiClient.post('/api/logout')
}

// 通用请求函数 - 带重试机制
export const fetchData = async (endpoint, options = {}) => {
  const maxRetries = options.retries || 2
  const retryDelay = options.retryDelay || 1000

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const fullUrl = `${API_BASE_URL}${endpoint}`
      console.log(`🌐 Fetching data from: ${fullUrl} (attempt ${attempt + 1}/${maxRetries + 1})`)

    const response = await fetch(fullUrl, {
      method: options.method || 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        ...(options.headers || {})
      },
      mode: 'cors',
      credentials: 'omit',
      ...(options.body && { body: options.body })
    })

    console.log('📡 Response status:', response.status, response.statusText)

    if (!response.ok) {
      // Enhanced error information
      let errorMessage = `HTTP error! status: ${response.status}`
      try {
        const errorData = await response.json()
        if (errorData.error) {
          errorMessage += ` - ${errorData.error}`
        }
      } catch (e) {
        // If response is not JSON, use status text
        errorMessage += ` - ${response.statusText}`
      }
      throw new Error(errorMessage)
    }

      const data = await response.json()
      console.log('✅ Data fetched successfully:', endpoint)
      return data
    } catch (error) {
      console.error(`❌ Fetch error (attempt ${attempt + 1}):`, endpoint, error)

      // If this is the last attempt, throw the error
      if (attempt === maxRetries) {
        throw error
      }

      // Wait before retrying (only for network errors, not 4xx errors)
      if (error.message.includes('status: 4')) {
        throw error // Don't retry client errors
      }

      console.log(`⏳ Retrying in ${retryDelay}ms...`)
      await new Promise(resolve => setTimeout(resolve, retryDelay))
    }
  }
}

// API请求函数 - 供 priceDataManager 使用
export const apiRequest = async (endpoint, method = 'GET', data = null, params = null) => {
  try {
    const config = {
      method,
      params,
    }

    if (data && method !== 'GET') {
      config.data = data
    }

    const response = await apiClient.request({
      url: endpoint,
      ...config
    })

    return response.data
  } catch (error) {
    console.error('API Request Error:', error)
    throw error
  }
}

export default apiClient
