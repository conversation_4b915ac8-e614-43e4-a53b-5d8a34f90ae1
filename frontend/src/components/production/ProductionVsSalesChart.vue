<template>
  <BaseCard title="产量与销量对比分析" :loading="loading" :error="error">
    <template #extra>
      <div class="chart-controls">
        <select v-model="chartType" @change="updateChartType" class="chart-type-selector">
          <option value="line">折线图</option>
          <option value="bar">柱状图</option>
          <option value="area">面积图</option>
        </select>
        
        <BaseButton
          @click="toggleDualAxis"
          variant="ghost"
          size="small"
        >
          {{ showDualAxis ? '单轴显示' : '双轴显示' }}
        </BaseButton>
      </div>
    </template>

    <EChartsWrapper
      :options="chartOptions"
      :height="height"
      :loading="loading"
      @chart-ready="onChartReady"
      @chart-click="onChartClick"
    />

    <div class="comparison-summary">
      <div class="summary-item">
        <span class="label">总产量：</span>
        <span class="value">{{ formatSalesVolume(totalProduction) }}</span>
      </div>
      <div class="summary-item">
        <span class="label">总销量：</span>
        <span class="value">{{ formatSalesVolume(totalSales) }}</span>
      </div>
      <div class="summary-item">
        <span class="label">产销差额：</span>
        <span class="value" :class="getDifferenceColorClass(productionSalesDifference)">
          {{ formatSalesVolume(Math.abs(productionSalesDifference)) }}
          {{ productionSalesDifference > 0 ? '(积压)' : '(短缺)' }}
        </span>
      </div>
      <div class="summary-item">
        <span class="label">平衡状态：</span>
        <span class="value" :class="getBalanceColorClass(averageRatio)">
          {{ getBalanceStatus(averageRatio) }}
        </span>
      </div>
    </div>
  </BaseCard>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useProductionStore } from '@/stores/production'
import { formatSalesVolume } from '@/utils/formatters'
import { getBaseChartOption, getTooltipConfig, getXAxisConfig, getYAxisConfig, getLineSeriesConfig, getBarSeriesConfig, getGridConfig } from '@/utils/charts'
import { CHART_COLORS } from '@/utils/constants'
import BaseCard from '@/components/common/BaseCard.vue'
import BaseButton from '@/components/common/BaseButton.vue'
import EChartsWrapper from '@/components/charts/EChartsWrapper.vue'

const props = defineProps({
  startDate: {
    type: String,
    default: '2025-06-01' // 备用默认值，实际值由父组件传入
  },
  endDate: {
    type: String,
    default: '2025-07-29' // 备用默认值，实际值由父组件传入
  },
  height: {
    type: String,
    default: '400px'
  }
})

const emit = defineEmits(['chart-ready', 'chart-click', 'data-updated'])

const productionStore = useProductionStore()
const loading = ref(false)
const error = ref('')
const chartType = ref('bar')
const showDualAxis = ref(true)
const chartInstance = ref(null)

// 计算属性
const chartData = computed(() => {
  return productionStore.ratioData.map(item => ({
    date: item.date,
    production: item.production_volume || 0,
    sales: item.sales_volume || 0,
    ratio: item.ratio || 0
  }))
})

const totalProduction = computed(() => {
  return chartData.value.reduce((sum, item) => sum + item.production, 0)
})

const totalSales = computed(() => {
  return chartData.value.reduce((sum, item) => sum + item.sales, 0)
})

const productionSalesDifference = computed(() => {
  return totalProduction.value - totalSales.value
})

const averageRatio = computed(() => {
  if (totalProduction.value === 0) return 0
  return (totalSales.value / totalProduction.value) * 100
})

// 图表配置
const chartOptions = computed(() => {
  if (chartData.value.length === 0) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#999',
          fontSize: 14
        }
      }
    }
  }

  const dates = chartData.value.map(item => item.date)
  const productions = chartData.value.map(item => item.production)
  const sales = chartData.value.map(item => item.sales)

  const baseConfig = {
    ...getBaseChartOption(),
    title: {
      text: '产量与销量对比分析',
      left: 'center',
      textStyle: {
        color: CHART_COLORS.PRIMARY,
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: getTooltipConfig((params) => {
      if (Array.isArray(params)) {
        const date = params[0].axisValue
        let tooltip = `<div style="font-weight: 600; margin-bottom: 8px;">${date}</div>`
        params.forEach(param => {
          const value = formatSalesVolume(param.value)
          tooltip += `<div style="margin: 4px 0;">
            <span style="display: inline-block; width: 10px; height: 10px; background: ${param.color}; border-radius: 50%; margin-right: 8px;"></span>
            ${param.seriesName}: <strong>${value}</strong>
          </div>`
        })
        return tooltip
      }
      return `${params.axisValue}<br/>${params.seriesName}: ${formatSalesVolume(params.value)}`
    }),
    legend: {
      data: ['产量', '销量'],
      top: 30,
      textStyle: {
        color: CHART_COLORS.TEXT_SECONDARY
      }
    },
    xAxis: getXAxisConfig(dates, { rotate: 45 }),
    grid: getGridConfig({ top: 80 })
  }

  // 配置Y轴
  if (showDualAxis.value) {
    baseConfig.yAxis = [
      getYAxisConfig({
        name: '产量(T)',
        formatter: (value) => formatSalesVolume(value),
        nameTextStyle: { color: CHART_COLORS.PRIMARY }
      }),
      getYAxisConfig({
        name: '销量(T)',
        formatter: (value) => formatSalesVolume(value),
        nameTextStyle: { color: CHART_COLORS.SECONDARY }
      })
    ]
  } else {
    baseConfig.yAxis = getYAxisConfig({
      name: '数量(T)',
      formatter: (value) => formatSalesVolume(value)
    })
  }

  // 配置系列
  const productionSeries = chartType.value === 'line' 
    ? getLineSeriesConfig('产量', productions, {
        color: CHART_COLORS.PRIMARY,
        smooth: true,
        yAxisIndex: showDualAxis.value ? 0 : undefined
      })
    : getBarSeriesConfig('产量', productions, {
        color: CHART_COLORS.PRIMARY,
        yAxisIndex: showDualAxis.value ? 0 : undefined
      })

  const salesSeries = chartType.value === 'line'
    ? getLineSeriesConfig('销量', sales, {
        color: CHART_COLORS.SECONDARY,
        smooth: true,
        yAxisIndex: showDualAxis.value ? 1 : undefined
      })
    : getBarSeriesConfig('销量', sales, {
        color: CHART_COLORS.SECONDARY,
        yAxisIndex: showDualAxis.value ? 1 : undefined
      })

  baseConfig.series = [productionSeries, salesSeries]

  return baseConfig
})

// 方法
const fetchData = async () => {
  // 检查日期参数是否有效
  if (!props.startDate || !props.endDate) {
    console.log('⏳ [ProductionVsSalesChart] Waiting for valid date range...', { start: props.startDate, end: props.endDate })
    return
  }
  
  loading.value = true
  error.value = ''
  
  console.log('🔄 [ProductionVsSalesChart] Starting data fetch...')
  console.log('📅 [ProductionVsSalesChart] Date range:', { start: props.startDate, end: props.endDate })
  
  try {
    console.log('🌐 [ProductionVsSalesChart] Calling productionStore.fetchProductionRatioData...')
    await productionStore.fetchProductionRatioData(props.startDate, props.endDate)
    console.log('✅ [ProductionVsSalesChart] Data fetched successfully:', productionStore.ratioData.length, 'records')
    emit('data-updated', productionStore.ratioData)
  } catch (err) {
    error.value = '数据加载失败'
    console.error('❌ [ProductionVsSalesChart] Failed to load production vs sales data:', err)
    console.error('❌ [ProductionVsSalesChart] Error details:', {
      message: err.message,
      stack: err.stack,
      startDate: props.startDate,
      endDate: props.endDate
    })
  } finally {
    loading.value = false
  }
}

const updateChartType = () => {
  console.log('Chart type updated to:', chartType.value)
}

const toggleDualAxis = () => {
  showDualAxis.value = !showDualAxis.value
  console.log('Dual axis toggled:', showDualAxis.value)
}

const getDifferenceColorClass = (difference) => {
  if (difference > 0) return 'trend-negative' // 积压：产量>销量
  if (difference < 0) return 'trend-positive' // 消费：销量>产量
  return 'trend-neutral'
}

const getBalanceColorClass = (ratio) => {
  // 按照产销率逻辑：>=100%为消费型（绿色），<100%为积压型（红色）
  if (ratio >= 100) return 'trend-positive' // 绿色：消费型
  if (ratio < 100) return 'trend-negative'  // 红色：积压型
  return 'trend-neutral'
}

const getBalanceStatus = (ratio) => {
  // 按照产销率逻辑：>=100%为消费型，<100%为积压型
  if (ratio >= 100) return '消费型' // 销量>=产量，库存消耗
  if (ratio < 100) return '积压型'  // 销量<产量，产品积压
  return '产销平衡'
}

const onChartReady = (chart) => {
  chartInstance.value = chart
  emit('chart-ready', chart)
}

const onChartClick = (params) => {
  emit('chart-click', params)
}

// 监听props变化
watch([() => props.startDate, () => props.endDate], ([newStart, newEnd]) => {
  // 只有当两个日期都有效时才获取数据
  if (newStart && newEnd) {
    console.log('📅 [ProductionVsSalesChart] Date props changed:', { newStart, newEnd })
    fetchData()
  }
})

onMounted(() => {
  console.log('🚀 [ProductionVsSalesChart] Component mounted')
  // 尝试初始加载，但如果日期无效会被fetchData内部的检查拦截
  fetchData()
})

// 暴露方法
defineExpose({
  refresh: fetchData,
  getChart: () => chartInstance.value
})
</script>

<style scoped>
.chart-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.chart-type-selector {
  padding: 6px 12px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.9em;
  background: white;
  cursor: pointer;
}

.chart-type-selector:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.comparison-summary {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  padding: 16px;
  background: var(--background-light);
  border-radius: 8px;
  border-top: 1px solid #f0f0f0;
}

.summary-item {
  text-align: center;
  flex: 1;
}

.label {
  display: block;
  color: var(--text-secondary);
  font-size: 0.85em;
  margin-bottom: 4px;
}

.value {
  display: block;
  color: var(--primary-blue);
  font-weight: 600;
  font-size: 1em;
}

.trend-positive {
  color: var(--success-green);
}

.trend-negative {
  color: var(--signal-red);
}

.trend-neutral {
  color: var(--neutral-gray);
}

@media (max-width: 768px) {
  .chart-controls {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .comparison-summary {
    flex-direction: column;
    gap: 12px;
  }
  
  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .label, .value {
    display: inline;
    margin: 0;
  }
}
</style>
