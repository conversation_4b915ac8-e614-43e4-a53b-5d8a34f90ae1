<template>
  <div class="auth-overlay">
    <div class="auth-modal">
      <h2 class="auth-title">春雪食品分析系统</h2>
      <p class="auth-subtitle">请登录或注册以查看详细报告</p>

      <!-- 认证标签页 -->
      <div class="auth-tabs">
        <button 
          class="auth-tab" 
          :class="{ active: activeTab === 'login' }"
          @click="switchTab('login')"
        >
          登录
        </button>
        <button 
          class="auth-tab" 
          :class="{ active: activeTab === 'register' }"
          @click="switchTab('register')"
        >
          注册
        </button>
      </div>

      <!-- 登录表单 -->
      <LoginForm 
        v-if="activeTab === 'login'"
        @success="handleAuthSuccess"
        @switch-to-register="switchTab('register')"
      />

      <!-- 注册表单 -->
      <RegisterForm 
        v-if="activeTab === 'register'"
        @success="handleRegisterSuccess"
        @switch-to-login="switchTab('login')"
      />

      <!-- 错误消息 -->
      <div v-if="authStore.error" class="auth-message error">
        {{ authStore.error }}
        <button @click="authStore.clearError" class="close-btn">&times;</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import LoginForm from './LoginForm.vue'
import RegisterForm from './RegisterForm.vue'

const authStore = useAuthStore()
const activeTab = ref('login')

const switchTab = (tab) => {
  activeTab.value = tab
  authStore.clearError()
}

const handleAuthSuccess = () => {
  // 登录成功后的处理在App.vue中进行
  console.log('✅ Authentication successful')
}

const handleRegisterSuccess = () => {
  // 注册成功后切换到登录页面
  switchTab('login')
}

onMounted(() => {
  // 尝试开发模式自动登录
  authStore.autoLoginDemo()
})
</script>

<style scoped>
.auth-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.auth-modal {
  background: white;
  padding: 40px 30px;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 400px;
  text-align: center;
}

.auth-title {
  color: var(--primary-blue);
  font-size: 1.8em;
  font-weight: bold;
  margin-bottom: 8px;
}

.auth-subtitle {
  color: #666;
  font-size: 0.9em;
  margin-bottom: 30px;
}

.auth-tabs {
  display: flex;
  margin-bottom: 30px;
  border-bottom: 2px solid #f0f0f0;
}

.auth-tab {
  flex: 1;
  padding: 12px 20px;
  background: none;
  border: none;
  font-size: 1em;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.auth-tab.active {
  color: var(--primary-blue);
  font-weight: bold;
}

.auth-tab.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--primary-blue);
}

.auth-message {
  margin-top: 20px;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 0.9em;
  position: relative;
}

.auth-message.error {
  background: #fee;
  color: #d92e2e;
  border: 1px solid #fcc;
}

.close-btn {
  position: absolute;
  top: 8px;
  right: 12px;
  background: none;
  border: none;
  font-size: 1.2em;
  cursor: pointer;
  color: inherit;
  opacity: 0.7;
}

.close-btn:hover {
  opacity: 1;
}

@media (max-width: 768px) {
  .auth-modal {
    padding: 30px 20px;
    margin: 20px;
  }
  
  .auth-title {
    font-size: 1.5em;
  }
}
</style>
