<template>
  <div class="inventory-turnover-page">
    <BaseCard title="库存周转" :loading="false">
      <div class="coming-soon">
        <div class="icon">🔄</div>
        <h3>库存周转模块</h3>
        <p>库存周转功能正在重构中，敬请期待...</p>
      </div>
    </BaseCard>
  </div>
</template>

<script setup>
import BaseCard from '@/components/common/BaseCard.vue'
</script>

<style scoped>
.inventory-turnover-page {
  padding: 0;
}

.coming-soon {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-secondary);
}

.icon {
  font-size: 4em;
  margin-bottom: 20px;
}

.coming-soon h3 {
  font-size: 1.5em;
  margin-bottom: 12px;
  color: var(--text-primary);
}

.coming-soon p {
  font-size: 1em;
  line-height: 1.6;
}
</style>
