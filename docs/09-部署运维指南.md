# 春雪食品产销分析系统 - 部署运维指南 (Vue.js 3版本)

## 1. 概述

本文档详细说明了基于Vue.js 3的春雪食品产销分析系统的部署流程、运维管理和故障排除方法。

## 2. 本地开发环境部署

### 2.1 环境要求

**系统要求**:
- Node.js 18+ 
- pnpm 8+ (推荐) 或 npm
- Python 3.8+
- Git

**开发工具**:
- Vue.js 3 + Vite
- Cloudflare Wrangler CLI
- VS Code (推荐)

### 2.2 项目初始化

#### 2.2.1 克隆项目并安装依赖
```bash
# 克隆项目
git clone [repository-url]
cd my-fullstack-project

# 安装前端依赖
cd frontend
pnpm install

# 安装后端依赖
cd ../backend
npm install

# 安装Python依赖 (数据导入)
pip install pandas openpyxl sqlalchemy

# 安装Cloudflare CLI
npm install -g wrangler
```

#### 2.2.2 环境配置
```bash
# 前端环境配置
cd frontend
# 从我们刚刚创建的 .env.example 模板复制配置
cp .env.example .env.development
# 编辑.env.development配置开发环境变量
 
# 后端环境配置
cd ../backend
# 从我们刚刚创建的 wrangler.toml.example 模板复制配置
cp wrangler.toml.example wrangler.toml
# 配置Cloudflare账户信息和数据库绑定
```

### 2.3 本地启动流程

#### 2.3.1 启动后端服务
```bash
cd backend
npm run dev
# 服务运行在 http://localhost:8787
```

#### 2.3.2 启动前端开发服务
```bash
cd frontend  
pnpm run dev
# 服务运行在 http://localhost:5173
# 支持热重载、组件热更新
```

#### 2.3.3 数据库初始化
```bash
# 创建本地数据库表结构
cd backend
npx wrangler d1 execute chunxue-prod-db --local --file=schema.sql

# 导入数据 (如果有)
npx wrangler d1 execute chunxue-prod-db --local --file=../import_data.sql
```

### 2.3 本地测试验证
```bash
# 验证后端API
curl "http://localhost:8787/api/products?limit=5"

# 验证前端访问
open http://localhost:3000
```

## 3. 生产环境部署

### 3.1 Cloudflare账户准备

**必需配置**:
1. 注册Cloudflare账户
2. 获取API Token (Workers:Edit权限)
3. 配置域名 (可选)

**Wrangler认证**:
```bash
wrangler login
# 或使用API Token
export CLOUDFLARE_API_TOKEN=your_token_here
```

### 3.2 数据库部署

#### 3.2.1 创建D1数据库
```bash
cd backend
npx wrangler d1 create chunxue-prod-db
# 记录返回的database_id
```

#### 3.2.2 更新wrangler.toml
```toml
name = "chunxue-backend"
main = "src/index.ts"
compatibility_date = "2024-01-01"

[[d1_databases]]
binding = "DB"
database_name = "chunxue-prod-db"
database_id = "your_database_id_here"
```

#### 3.2.3 初始化数据库结构
```bash
# 创建表结构
npx wrangler d1 execute chunxue-prod-db --remote --file=schema.sql

# 导入数据
npx wrangler d1 execute chunxue-prod-db --remote --file=../import_data.sql
```

### 3.3 后端部署

```bash
cd backend
npx wrangler deploy
# 记录返回的Worker URL
```

### 3.4 前端部署

#### 3.4.1 使用Cloudflare Pages
```bash
# 方法1: 通过Git集成
# 1. 将代码推送到GitHub
# 2. 在Cloudflare Pages中连接仓库
# 3. 设置构建配置:
#    - 构建命令: 无 (静态文件)
#    - 输出目录: frontend
#    - 环境变量: API_BASE_URL=https://your-worker.workers.dev

# 方法2: 直接上传
npx wrangler pages deploy frontend --project-name=chunxue-frontend
```

#### 3.4.2 更新API地址
修改前端代码中的API基础地址:
```javascript
// frontend/script.js
const API_BASE_URL = 'https://your-worker.your-subdomain.workers.dev';
```

## 4. 数据管理

### 4.1 数据导入流程

#### 4.1.1 准备Excel文件
确保以下文件存在且格式正确:
- `Excel文件夹/产成品入库列表.xlsx`
- `Excel文件夹/收发存汇总表查询.xlsx`
- `Excel文件夹/销售发票执行查询.xlsx`

#### 4.1.2 执行数据导入
```bash
# 1. 运行导入脚本
# 运行批量导入主程序
python3 bulk_import_main.py

# 如有需要，可运行日常导入主程序
# python3 daily_import_main.py

# 2. 检查生成的SQL文件
head -20 import_data.sql

# 3. 导入到本地数据库 (测试)
cd backend
npx wrangler d1 execute chunxue-prod-db --local --file=../import_data.sql

# 4. 验证数据质量
curl "http://localhost:8787/api/summary?start_date=2025-06-01&end_date=2025-06-26"

# 5. 导入到生产数据库
npx wrangler d1 execute chunxue-prod-db --remote --file=../import_data.sql
```

### 4.2 数据备份

#### 4.2.1 导出数据
```bash
# 导出所有数据
npx wrangler d1 export chunxue-prod-db --remote --output=backup_$(date +%Y%m%d).sql

# 导出特定表
npx wrangler d1 execute chunxue-prod-db --remote --command="SELECT * FROM Products" --output=products_backup.csv
```

#### 4.2.2 定期备份策略
- **每日备份**: 自动化脚本，保留7天
- **每周备份**: 保留4周
- **每月备份**: 保留12个月

### 4.3 数据迁移

#### 4.3.1 版本升级迁移
```bash
# 1. 备份现有数据
npx wrangler d1 export chunxue-prod-db --remote --output=pre_migration_backup.sql

# 2. 执行迁移脚本
npx wrangler d1 execute chunxue-prod-db --remote --file=migration_v2.sql

# 3. 验证迁移结果
npx wrangler d1 execute chunxue-prod-db --remote --command="SELECT COUNT(*) FROM Products"
```

## 5. 监控和维护

### 5.1 性能监控

#### 5.1.1 关键指标
- **API响应时间**: 目标 < 500ms
- **数据库查询时间**: 目标 < 200ms
- **错误率**: 目标 < 1%
- **可用性**: 目标 > 99.9%

#### 5.1.2 监控工具
```bash
# Cloudflare Analytics
# 在Cloudflare Dashboard中查看:
# - 请求数量和响应时间
# - 错误率统计
# - 地理分布

# 自定义监控脚本
#!/bin/bash
# health_check.sh
response=$(curl -s -o /dev/null -w "%{http_code}" "https://your-worker.workers.dev/health")
if [ $response -ne 200 ]; then
    echo "API健康检查失败: HTTP $response"
    # 发送告警通知
fi
```

### 5.2 日志管理

#### 5.2.1 查看实时日志
```bash
# Worker日志
npx wrangler tail chunxue-backend

# 过滤错误日志
npx wrangler tail chunxue-backend --format=pretty | grep ERROR
```

#### 5.2.2 日志分析
- 定期检查错误日志
- 分析性能瓶颈
- 监控异常访问模式

### 5.3 安全维护

#### 5.3.1 访问控制
- 定期更新API密钥
- 监控异常访问模式
- 实施速率限制

#### 5.3.2 数据安全
- 定期备份验证
- 敏感数据加密
- 访问日志审计

## 6. 故障排除

### 6.1 常见问题

#### 6.1.1 API无响应
**症状**: 前端无法加载数据
**排查步骤**:
```bash
# 1. 检查Worker状态
npx wrangler tail chunxue-backend

# 2. 测试API端点
curl -v "https://your-worker.workers.dev/api/products"

# 3. 检查数据库连接
npx wrangler d1 execute chunxue-prod-db --remote --command="SELECT 1"
```

#### 6.1.2 数据显示异常
**症状**: 图表显示空数据或错误数据
**排查步骤**:
```bash
# 1. 验证数据库数据
npx wrangler d1 execute chunxue-prod-db --remote --command="SELECT COUNT(*) FROM DailyMetrics"

# 2. 检查API返回
curl "https://your-worker.workers.dev/api/summary?start_date=2025-06-01&end_date=2025-06-26"

# 3. 验证前端API调用
# 在浏览器开发者工具中检查网络请求
```

#### 6.1.3 部署失败
**症状**: wrangler deploy命令失败
**解决方案**:
```bash
# 1. 检查认证状态
wrangler whoami

# 2. 验证配置文件
cat wrangler.toml

# 3. 清理缓存重试
rm -rf node_modules/.cache
npm run build
npx wrangler deploy
```

### 6.2 紧急恢复

#### 6.2.1 数据恢复
```bash
# 从备份恢复数据库
npx wrangler d1 execute chunxue-prod-db --remote --file=backup_20250101.sql
```

#### 6.2.2 服务回滚
```bash
# 回滚到上一个版本
npx wrangler rollback chunxue-backend
```

## 7. 性能优化

### 7.1 数据库优化

#### 7.1.1 索引优化
```sql
-- 确保关键索引存在
CREATE INDEX IF NOT EXISTS idx_dailymetrics_date ON DailyMetrics(record_date);
CREATE INDEX IF NOT EXISTS idx_dailymetrics_product_id ON DailyMetrics(product_id);
CREATE INDEX IF NOT EXISTS idx_dailymetrics_date_product ON DailyMetrics(record_date, product_id);
```

#### 7.1.2 查询优化
- 使用适当的WHERE条件
- 避免SELECT *
- 合理使用聚合函数

### 7.2 缓存策略

#### 7.2.1 Cloudflare缓存
```javascript
// 在Worker中设置缓存头
response.headers.set('Cache-Control', 'public, max-age=300'); // 5分钟缓存
```

#### 7.2.2 前端缓存
```javascript
// 实施前端数据缓存
const cache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟
```

## 8. 扩展和升级

### 8.1 功能扩展

#### 8.1.1 新增API端点
1. 在`backend/src/index.ts`中添加路由
2. 实现业务逻辑
3. 更新API文档
4. 添加测试用例
5. 部署和验证

#### 8.1.2 前端功能扩展
1. 添加新的图表组件
2. 更新用户界面
3. 集成新的API调用
4. 测试兼容性
5. 部署更新

### 8.2 版本管理

#### 8.2.1 版本控制策略
- 使用语义化版本号 (v1.0.0)
- 维护CHANGELOG.md
- 标记重要版本的Git标签

#### 8.2.2 升级流程
1. 代码审查和测试
2. 备份生产数据
3. 部署到预发布环境
4. 执行完整测试
5. 部署到生产环境
6. 监控和验证

## 9. CI/CD 部署保护

为确保生产环境的稳定性和数据准确性，CI/CD 流程在部署前必须满足以下要求：

- **强制运行所有测试**: 在执行任何生产部署之前，CI/CD 流水线必须自动运行所有常规单元测试和集成测试。
- **关键集成测试 - 库存总量验证**:
  - **必须执行**: CI/CD 流程**必须**成功执行位于 `backend/test/inventory.spec.ts` 的库存总量验证测试。
  - **失败后果**: 如果此测试失败（例如，库存总量低于预设的关键阈值），部署流程必须**立即中止**。
  - **通知机制**: 部署失败后，系统应自动通知相关技术负责人和业务负责人进行紧急调查。此机制旨在防止因数据异常或逻辑错误导致的灾难性部署。
- **通用部署中止策略**: 如果任何一个测试（包括上述关键测试）失败，部署流程都将立即中止。不允许将未通过测试的代码部署到生产环境。

此策略旨在防止有缺陷的代码进入生产，保障系统的核心功能不受影响。

---

**注意**: 本文档基于实际部署经验编写，建议根据具体环境和需求进行调整。定期更新文档以反映最新的部署实践。


---
**文档版本**: v1.0
**最后更新**: 2025-07-30
**负责人**: AI Assistant
