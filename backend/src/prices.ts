import { Hono } from 'hono';
import { Bindings } from './index'; // Assuming Bindings are exported from index.ts

const prices = new Hono<{ Bindings: Bindings }>();

// Define interfaces for database query results to ensure type safety
interface DailyDropRow {
  product_id: number;
  product_name: string;
  current_price: number;
  previous_price: number;
  price_change: number; // Changed from price_change
  adjustment_date: string;
  change_percentage: number;
}

// --- API Endpoints for Price Monitoring ---

/**
 * Get a list of key products for monitoring.
 * Query parameters:
 * - days: Time period in days (default: 90)
 * - limit: Maximum number of products to return (default: 20)
 * - includeSpecs: If 'true', groups by product+specification (default: false)
 */
prices.get('/key-products', async (c) => {
  const { days = '90', limit = '300', includeSpecs = 'false' } = c.req.query();

  try {
    const query = includeSpecs === 'true' ? `
      SELECT
        product_name,
        specification,
        COUNT(DISTINCT adjustment_date) as adjustment_days,
        COUNT(id) as total_adjustments,
        AVG(ABS(price_change)) as avg_price_change,
        MAX(current_price) as max_price,
        MIN(current_price) as min_price,
        MAX(adjustment_date) as last_adjustment_date
      FROM PriceAdjustments
      WHERE adjustment_date >= date('now', '-${parseInt(days)} days')
        AND current_price > 0
        AND product_name NOT LIKE '鲜%'
      GROUP BY product_name, specification
      ORDER BY adjustment_days DESC, total_adjustments DESC
      LIMIT ${parseInt(limit)}
    ` : `
      SELECT
        product_name,
        COUNT(DISTINCT adjustment_date) as adjustment_days,
        COUNT(id) as total_adjustments,
        COUNT(DISTINCT specification) as spec_count,
        AVG(ABS(price_change)) as avg_price_change,
        MAX(current_price) as max_price,
        MIN(current_price) as min_price,
        MAX(adjustment_date) as last_adjustment_date
      FROM PriceAdjustments
      WHERE adjustment_date >= date('now', '-${parseInt(days)} days')
        AND current_price > 0
        AND product_name NOT LIKE '鲜%'
      GROUP BY product_name
      ORDER BY adjustment_days DESC, total_adjustments DESC
      LIMIT ${parseInt(limit)}
    `;

    const ps = c.env.DB.prepare(query);
    const { results } = await ps.all();

    // Process results to ensure proper data types
    const processedResults = results.map((item: any) => ({
      ...item,
      avg_price_change: parseFloat(item.avg_price_change || '0').toFixed(2),
      price_volatility: ((item.max_price - item.min_price) / item.min_price * 100).toFixed(2)
    }));

    // Define the most important products (top 20 by business importance)
    const recommendedProducts = [
      '鸡脖', '膝软骨', '琵琶腿', '鸡大胸', '鸡小胸', 
      '去皮鸡胸肉', '鸡心', '鸡肫', '凤爪35g以上', '凤爪30/35',
      '中40g以上A', '中50g以上', '爪40/50', '根60g以上', '根50/60',
      '小腿140/180', '单冻大胸', '鸡排腿300/350', '腿肉块25/35', '全翅'
    ];

    // Separate recommended products and others
    const recommendedItems = [];
    const otherItems = [];
    
    processedResults.forEach((item: any) => {
      const recommendIndex = recommendedProducts.indexOf(item.product_name);
      if (recommendIndex !== -1) {
        recommendedItems.push({
          ...item,
          is_recommended: true,
          recommendation_rank: recommendIndex + 1
        });
      } else {
        otherItems.push({
          ...item,
          is_recommended: false
        });
      }
    });

    // Sort recommended items by their predefined order
    recommendedItems.sort((a, b) => a.recommendation_rank - b.recommendation_rank);
    
    // Combine: recommended products first, then others
    const finalResults = [...recommendedItems, ...otherItems];

    return c.json({
      success: true,
      data: finalResults,
      metadata: {
        period_days: parseInt(days),
        limit: parseInt(limit),
        include_specifications: includeSpecs === 'true',
        total_products: finalResults.length,
        recommended_count: recommendedItems.length
      }
    });
  } catch (e: any) {
    console.error('Failed to fetch key products:', e);
    return c.json({ success: false, message: 'Database query failed', details: e.message }, 500);
  }
});

/**
 * Get price trend data for a specific product.
 * Query parameters:
 * - productName: The name of the product to query.
 * - period: Time period in days (e.g., '30', '90', '180'). Defaults to 90.
 * - includeHistory: If 'true', returns all price adjustments including multiple daily changes.
 * - specification: Optional product specification filter.
 */
prices.get('/trends', async (c) => {
  const queryParams = c.req.query();
  console.log('Received /trends query params:', queryParams);
  let { productName, period, includeHistory, specification } = queryParams;

  if (!productName) {
    console.error('productName is missing for /trends');
    return c.json({ success: false, message: 'productName is required' }, 400);
  }

  // 处理URL编码问题
  productName = decodeURIComponent(productName);
  console.log('Decoded productName:', productName);

  const days = parseInt(period || '90', 10);
  const showHistory = includeHistory === 'true';

  try {
    // 检查产品是否存在
    const checkQuery = `
      SELECT DISTINCT product_name 
      FROM PriceAdjustments 
      WHERE product_name = ?1
      LIMIT 1
    `;
    const checkPs = c.env.DB.prepare(checkQuery).bind(productName);
    const { results: checkResults } = await checkPs.all();
    
    if (!checkResults || checkResults.length === 0) {
      console.error(`Product not found: ${productName}`);
      
      // 尝试模糊匹配找到相似产品
      const similarQuery = `
        SELECT DISTINCT product_name,
          CASE 
            WHEN product_name LIKE ?1 THEN 1
            WHEN product_name LIKE ?2 THEN 2
            ELSE 3
          END as match_score
        FROM PriceAdjustments
        WHERE product_name LIKE ?1 OR product_name LIKE ?2
        ORDER BY match_score, product_name
        LIMIT 5
      `;
      const similarPs = c.env.DB.prepare(similarQuery).bind(
        `%${productName}%`,
        `%${productName.replace(/\s+/g, '%')}%`
      );
      const { results: similarProducts } = await similarPs.all();
      
      return c.json({
        success: false,
        message: `产品 "${productName}" 不存在`,
        suggestions: similarProducts?.map((p: any) => p.product_name) || [],
        metadata: {
          searched_name: productName,
          found_similar: similarProducts?.length || 0
        }
      }, 404);
    }
    let query: string;
    const params: any[] = [productName, `-${days} days`];

    if (showHistory) {
      // Return complete adjustment history
      query = `
        SELECT
          adjustment_date,
          current_price,
          previous_price,
          price_change,
          adjustment_count,
          specification
        FROM PriceAdjustments
        WHERE product_name = ?1
          AND adjustment_date >= date('now', ?2)
          ${specification ? 'AND specification = ?3' : ''}
        ORDER BY adjustment_date ASC, adjustment_count ASC
      `;
      if (specification) params.push(specification);
    } else {
      // Return only final daily prices with proper aggregation
      // When no specification is provided, aggregate across all specifications
      query = specification ? `
        WITH DailyFinalPrices AS (
          SELECT
            adjustment_date,
            current_price,
            previous_price,
            price_change,
            specification,
            ROW_NUMBER() OVER (
              PARTITION BY adjustment_date
              ORDER BY adjustment_count DESC
            ) as rn
          FROM PriceAdjustments
          WHERE product_name = ?1
            AND adjustment_date >= date('now', ?2)
            AND specification = ?3
        )
        SELECT
          adjustment_date,
          current_price,
          previous_price,
          price_change,
          specification
        FROM DailyFinalPrices
        WHERE rn = 1
        ORDER BY adjustment_date ASC
      ` : `
        WITH SpecDailyPrices AS (
          -- Get final price for each specification on each day
          SELECT
            adjustment_date,
            specification,
            current_price,
            previous_price,
            price_change,
            adjustment_count,
            ROW_NUMBER() OVER (
              PARTITION BY adjustment_date, specification
              ORDER BY adjustment_count DESC
            ) as rn
          FROM PriceAdjustments
          WHERE product_name = ?1
            AND adjustment_date >= date('now', ?2)
        ),
        DailyAggregated AS (
          -- Aggregate across specifications using weighted average
          SELECT
            adjustment_date,
            AVG(current_price) as current_price,
            AVG(previous_price) as previous_price,
            AVG(price_change) as price_change,
            GROUP_CONCAT(DISTINCT specification) as specification
          FROM SpecDailyPrices
          WHERE rn = 1
          GROUP BY adjustment_date
        )
        SELECT
          adjustment_date,
          current_price,
          previous_price,
          price_change,
          specification
        FROM DailyAggregated
        ORDER BY adjustment_date ASC
      `;
      if (specification) params.push(specification);
    }

    const ps = c.env.DB.prepare(query).bind(...params);
    const { results } = await ps.all();

    // 检查是否有数据返回
    if (!results || results.length === 0) {
      console.warn(`No price data found for product: ${productName}`);
      return c.json({
        success: true,
        data: [],
        metadata: {
          product_name: productName,
          specification: specification || null,
          period_days: days,
          include_history: showHistory,
          record_count: 0,
          message: '该产品在指定时间段内没有价格数据'
        }
      });
    }

    // Calculate proper price differences if needed
    const processedResults = results.map((record: any, index: number) => {
      // 确保所有数值字段都是有效的
      const processedRecord = {
        ...record,
        current_price: parseFloat(record.current_price || '0'),
        previous_price: parseFloat(record.previous_price || '0'),
        price_change: parseFloat(record.price_change || '0')
      };
      
      // 重新计算价格差异如果需要
      if (processedRecord.price_change === 0 && processedRecord.previous_price > 0) {
        processedRecord.price_change = processedRecord.current_price - processedRecord.previous_price;
      }
      
      return processedRecord;
    });

    return c.json({
      success: true,
      data: processedResults,
      metadata: {
        product_name: productName,
        specification: specification || null,
        period_days: days,
        include_history: showHistory,
        record_count: processedResults.length
      }
    });

  } catch (e: any) {
    console.error(`Failed to fetch price trends for ${productName}:`, e);
    console.error('Stack trace:', e.stack);
    
    // 更详细的错误响应
    return c.json({
      success: false,
      message: '查询价格趋势时发生错误',
      error: {
        type: e.name || 'DatabaseError',
        message: e.message,
        query_params: {
          product_name: productName,
          period: days,
          specification: specification || null
        }
      }
    }, 500);
  }
});

/**
 * Get aggregated price data for multiple products with daily summaries.
 * Query parameters:
 * - products: Comma-separated list of product names
 * - startDate: Start date (YYYY-MM-DD format)
 * - endDate: End date (YYYY-MM-DD format)
 * - groupBySpec: If 'true', groups by specification
 */
prices.get('/aggregate', async (c) => {
  const queryParams = c.req.query();
  console.log('Received /aggregate query params:', queryParams);
  const { products, startDate, endDate, groupBySpec = 'false' } = queryParams;

  if (!products) {
    console.error('products parameter is missing for /aggregate');
    return c.json({ success: false, message: 'products parameter is required' }, 400);
  }

  const productList = products.split(',').map(p => p.trim());
  const placeholders = productList.map((_, i) => `?${i + 1}`).join(',');

  try {
    // When groupBySpec is false, we need to aggregate WITHIN each specification first,
    // then aggregate across specifications to avoid mixing prices from different specs
    const baseQuery = groupBySpec === 'true' ? `
      WITH DailyAggregates AS (
        SELECT
          product_name,
          specification,
          adjustment_date,
          MAX(current_price) as daily_high,
          MIN(current_price) as daily_low,
          AVG(current_price) as daily_avg,
          -- Get the final price of the day
          FIRST_VALUE(current_price) OVER (
            PARTITION BY product_name, specification, adjustment_date
            ORDER BY adjustment_count DESC
          ) as daily_close,
          -- Get the opening price of the day
          FIRST_VALUE(current_price) OVER (
            PARTITION BY product_name, specification, adjustment_date
            ORDER BY adjustment_count ASC
          ) as daily_open,
          COUNT(*) as adjustment_count
        FROM PriceAdjustments
        WHERE product_name IN (${placeholders})
          ${startDate ? `AND adjustment_date >= ?${productList.length + 1}` : ''}
          ${endDate ? `AND adjustment_date <= ?${productList.length + (startDate ? 2 : 1)}` : ''}
          AND current_price > 0
        GROUP BY product_name, specification, adjustment_date
      )
      SELECT DISTINCT
        product_name,
        specification,
        adjustment_date,
        daily_open,
        daily_high,
        daily_low,
        daily_close,
        daily_avg,
        adjustment_count,
        (daily_close - daily_open) as daily_change,
        CASE 
          WHEN daily_open > 0 THEN ((daily_close - daily_open) / daily_open * 100)
          ELSE 0
        END as daily_change_percent
      FROM DailyAggregates
      ORDER BY adjustment_date DESC, product_name ASC, specification ASC
    ` : `
      WITH SpecDailyPrices AS (
        -- First, get daily prices for each specification
        SELECT
          product_name,
          specification,
          adjustment_date,
          MAX(current_price) as spec_high,
          MIN(current_price) as spec_low,
          AVG(current_price) as spec_avg,
          FIRST_VALUE(current_price) OVER (
            PARTITION BY product_name, specification, adjustment_date
            ORDER BY adjustment_count DESC
          ) as spec_close,
          FIRST_VALUE(current_price) OVER (
            PARTITION BY product_name, specification, adjustment_date
            ORDER BY adjustment_count ASC
          ) as spec_open,
          COUNT(*) as spec_count
        FROM PriceAdjustments
        WHERE product_name IN (${placeholders})
          ${startDate ? `AND adjustment_date >= ?${productList.length + 1}` : ''}
          ${endDate ? `AND adjustment_date <= ?${productList.length + (startDate ? 2 : 1)}` : ''}
          AND current_price > 0
        GROUP BY product_name, specification, adjustment_date
      ),
      DailyAggregates AS (
        -- Then aggregate across specifications using weighted averages
        SELECT
          product_name,
          adjustment_date,
          -- Use weighted average for open/close prices
          SUM(spec_open * spec_count) / SUM(spec_count) as daily_open,
          SUM(spec_close * spec_count) / SUM(spec_count) as daily_close,
          -- For high/low, use weighted average as well to avoid illogical jumps
          SUM(spec_high * spec_count) / SUM(spec_count) as daily_high,
          SUM(spec_low * spec_count) / SUM(spec_count) as daily_low,
          -- Weighted average price
          SUM(spec_avg * spec_count) / SUM(spec_count) as daily_avg,
          SUM(spec_count) as adjustment_count
        FROM SpecDailyPrices
        GROUP BY product_name, adjustment_date
      )
      SELECT
        product_name,
        adjustment_date,
        daily_open,
        daily_high,
        daily_low,
        daily_close,
        daily_avg,
        adjustment_count,
        (daily_close - daily_open) as daily_change,
        CASE 
          WHEN daily_open > 0 THEN ((daily_close - daily_open) / daily_open * 100)
          ELSE 0
        END as daily_change_percent
      FROM DailyAggregates
      ORDER BY adjustment_date DESC, product_name ASC
    `;

    const params = [...productList];
    if (startDate) params.push(startDate);
    if (endDate) params.push(endDate);

    const ps = c.env.DB.prepare(baseQuery).bind(...params);
    const { results } = await ps.all();

    // Group results by product for easier frontend consumption
    const groupedResults: { [key: string]: any[] } = {};
    results.forEach((row: any) => {
      const key = groupBySpec === 'true' ? `${row.product_name}-${row.specification}` : row.product_name;
      if (!groupedResults[key]) {
        groupedResults[key] = [];
      }
      groupedResults[key].push({
        ...row,
        daily_avg: parseFloat(row.daily_avg || '0').toFixed(2),
        daily_change_percent: parseFloat(row.daily_change_percent || '0').toFixed(2)
      });
    });

    return c.json({
      success: true,
      data: groupedResults,
      metadata: {
        products: productList,
        start_date: startDate || null,
        end_date: endDate || null,
        group_by_specification: groupBySpec === 'true',
        total_records: results.length
      }
    });

  } catch (e: any) {
    console.error('Failed to fetch aggregated price data:', e);
    return c.json({
      success: false,
      message: 'Database query failed',
      details: e.message
    }, 500);
  }
});

/**
 * Get price statistics for analysis.
 * Query parameters:
 * - productName: Product name (required)
 * - days: Analysis period in days (default: 30)
 */
prices.get('/statistics', async (c) => {
  const queryParams = c.req.query();
  console.log('Received /statistics query params:', queryParams);
  const { productName, days = '30' } = queryParams;

  if (!productName) {
    console.error('productName is missing for /statistics');
    return c.json({ success: false, message: 'productName is required' }, 400);
  }

  try {
    const query = `
      WITH PriceStats AS (
        SELECT
          product_name,
          specification,
          COUNT(DISTINCT adjustment_date) as trading_days,
          COUNT(*) as total_adjustments,
          AVG(current_price) as avg_price,
          MAX(current_price) as max_price,
          MIN(current_price) as min_price,
          AVG(ABS(price_change)) as avg_change,
          MAX(ABS(price_change)) as max_change,
          SUM(CASE WHEN price_change > 0 THEN 1 ELSE 0 END) as price_increases,
          SUM(CASE WHEN price_change < 0 THEN 1 ELSE 0 END) as price_decreases,
          SUM(CASE WHEN price_change = 0 THEN 1 ELSE 0 END) as price_unchanged
        FROM PriceAdjustments
        WHERE product_name = ?1
          AND adjustment_date >= date('now', ?2)
          AND current_price > 0
        GROUP BY specification
      )
      SELECT
        *,
        (max_price - min_price) as price_range,
        CASE
          WHEN min_price > 0 THEN ((max_price - min_price) / min_price * 100)
          ELSE 0
        END as price_range_percent
      FROM PriceStats
      ORDER BY total_adjustments DESC
    `;

    const ps = c.env.DB.prepare(query).bind(productName, `-${parseInt(days)} days`);
    const { results } = await ps.all();

    // Calculate summary statistics across all specifications
    const summary = {
      total_specifications: results.length,
      overall_avg_price: results.reduce((sum: number, r: any) => sum + parseFloat(r.avg_price), 0) / results.length,
      overall_max_price: Math.max(...results.map((r: any) => r.max_price)),
      overall_min_price: Math.min(...results.map((r: any) => r.min_price)),
      most_volatile_spec: results.sort((a: any, b: any) => b.cv_percent - a.cv_percent)[0]?.specification || null
    };

    return c.json({
      success: true,
      data: results.map((r: any) => ({
        ...r,
        avg_price: parseFloat(r.avg_price || '0').toFixed(2),
        avg_change: parseFloat(r.avg_change || '0').toFixed(2),
        price_range_percent: parseFloat(r.price_range_percent || '0').toFixed(2)
      })),
      summary,
      metadata: {
        product_name: productName,
        analysis_days: parseInt(days)
      }
    });

  } catch (e: any) {
    console.error('Failed to fetch price statistics:', e);
    return c.json({
      success: false,
      message: 'Database query failed',
      details: e.message
    }, 500);
  }
});

/**
 * Checks for system-wide price alerts based on predefined rules.
 * Rules:
 * 1. Consecutive Drop: Price drops for N consecutive days.
 * 2. Significant Drop: Single-day drop > X% or > Y yuan/ton.
 * 3. Multiple Daily Adjustments: Products with multiple price changes in a single day.
 * Query parameters:
 * - consecutiveDays: (number) Defaults to 3.
 * - percentageDrop: (number) Defaults to 5.
 * - absoluteDrop: (number) Defaults to 200.
 * - checkMultipleAdjustments: (boolean) Defaults to true.
 */
prices.get('/system-alerts', async (c) => {
  const consecutiveDays = parseInt(c.req.query('consecutiveDays') || '3', 10);
  const percentageDrop = parseFloat(c.req.query('percentageDrop') || '5');
  const absoluteDrop = parseFloat(c.req.query('absoluteDrop') || '200');
  const checkMultipleAdjustments = c.req.query('checkMultipleAdjustments') !== 'false';

  try {
    const triggeredAlerts: any[] = [];

    // 优化1: 使用单个查询获取所有需要的数据
    const recentDataQuery = `
      WITH RankedPrices AS (
        SELECT
          product_name,
          adjustment_date,
          current_price,
          previous_price,
          price_change,
          adjustment_count,
          ROW_NUMBER() OVER (
            PARTITION BY product_name
            ORDER BY adjustment_date DESC, adjustment_count DESC
          ) as rn
        FROM PriceAdjustments
        WHERE adjustment_date >= date('now', '-${Math.max(consecutiveDays + 5, 30)} days')
          AND current_price > 0
      )
      SELECT * FROM RankedPrices
      WHERE rn <= ${consecutiveDays + 2}
      ORDER BY product_name, adjustment_date DESC
    `;

    const ps = c.env.DB.prepare(recentDataQuery);
    const { results } = await ps.all();

    if (!results || results.length === 0) {
      return c.json({
        success: true,
        data: [],
        metadata: { message: 'No recent price data found' }
      });
    }

    // 优化2: 内存中处理数据，避免N+1查询
    const productData: { [key: string]: any[] } = {};
    results.forEach((row: any) => {
      if (!productData[row.product_name]) {
        productData[row.product_name] = [];
      }
      productData[row.product_name].push(row);
    });

    // 优化3: 并行处理所有产品的预警检测
    const alertPromises = Object.entries(productData).map(([productName, priceHistory]) =>
      checkProductAlerts(productName, priceHistory, {
        consecutiveDays,
        percentageDrop,
        absoluteDrop,
        checkMultipleAdjustments
      })
    );

    const allAlerts = await Promise.all(alertPromises);
    const flattenedAlerts = allAlerts.flat();

    return c.json({
      success: true,
      data: flattenedAlerts,
      metadata: {
        total_alerts: flattenedAlerts.length,
        products_checked: Object.keys(productData).length,
        alert_types: {
          multiple_adjustments: flattenedAlerts.filter(a => a.type === 'MULTIPLE_DAILY_ADJUSTMENTS').length,
          significant_drops: flattenedAlerts.filter(a => a.type.startsWith('SIGNIFICANT_DROP')).length,
          consecutive_drops: flattenedAlerts.filter(a => a.type === 'CONSECUTIVE_DROP').length
        },
        parameters: {
          consecutive_days: consecutiveDays,
          percentage_drop: percentageDrop,
          absolute_drop: absoluteDrop,
          check_multiple_adjustments: checkMultipleAdjustments
        }
      }
    });

  } catch (e: any) {
    console.error('Failed to check system alerts:', e);
    return c.json({
      success: false,
      message: 'Database query failed',
      details: e.message
    }, 500);
  }
});

// 辅助函数：检测单个产品的预警
async function checkProductAlerts(
  productName: string,
  priceHistory: any[],
  options: {
    consecutiveDays: number;
    percentageDrop: number;
    absoluteDrop: number;
    checkMultipleAdjustments: boolean;
  }
): Promise<any[]> {
  const alerts: any[] = [];
  const { consecutiveDays, percentageDrop, absoluteDrop, checkMultipleAdjustments } = options;

  if (priceHistory.length < 2) return alerts;

  // 检测显著下降
  const latest = priceHistory[0];
  const previous = priceHistory[1];

  if (latest && previous) {
    const priceDiff = previous.current_price - latest.current_price;

    // 绝对金额下降检测
    if (priceDiff > absoluteDrop) {
      alerts.push({
        productName,
        type: 'SIGNIFICANT_DROP_ABSOLUTE',
        date: latest.adjustment_date,
        message: `单日降价超过 ${absoluteDrop}元/吨 (降价 ${priceDiff.toFixed(2)}元)`,
        severity: 'high',
        current_price: latest.current_price,
        previous_price: previous.current_price,
        price_change: -priceDiff
      });
    }

    // 百分比下降检测
    if (previous.current_price > 0) {
      const percentageDiff = (priceDiff / previous.current_price) * 100;
      if (percentageDiff > percentageDrop) {
        alerts.push({
          productName,
          type: 'SIGNIFICANT_DROP_PERCENTAGE',
          date: latest.adjustment_date,
          message: `单日降价超过 ${percentageDrop}% (降幅 ${percentageDiff.toFixed(2)}%)`,
          severity: 'high',
          current_price: latest.current_price,
          previous_price: previous.current_price,
          price_change_percent: -percentageDiff
        });
      }
    }
  }

  // 检测连续下降
  if (priceHistory.length >= consecutiveDays) {
    let isConsecutiveDrop = true;
    for (let i = 0; i < consecutiveDays - 1; i++) {
      if (priceHistory[i].current_price >= priceHistory[i + 1].current_price) {
        isConsecutiveDrop = false;
        break;
      }
    }

    if (isConsecutiveDrop) {
      alerts.push({
        productName,
        type: 'CONSECUTIVE_DROP',
        date: priceHistory[0].adjustment_date,
        message: `连续 ${consecutiveDays} 天价格下降`,
        severity: 'medium',
        consecutive_days: consecutiveDays,
        total_drop: priceHistory[consecutiveDays - 1].current_price - priceHistory[0].current_price
      });
    }
  }

  // 检测同日多次调价
  if (checkMultipleAdjustments) {
    const dailyAdjustments: { [key: string]: number } = {};
    priceHistory.forEach(record => {
      const date = record.adjustment_date;
      dailyAdjustments[date] = (dailyAdjustments[date] || 0) + 1;
    });

    Object.entries(dailyAdjustments).forEach(([date, count]) => {
      if (count > 3) { // 一天超过3次调价
        alerts.push({
          productName,
          type: 'MULTIPLE_DAILY_ADJUSTMENTS',
          date,
          message: `单日调价 ${count} 次，可能存在价格异常`,
          severity: 'low',
          adjustment_count: count
        });
      }
    });
  }

  return alerts;
}

/**
 * Create a new user-defined price alert.
 */
prices.post('/alerts', async (c) => {
  try {
    const { userId = 1, productId, specifications, alertCondition, priceThreshold } = await c.req.json(); // Default userId to 1 for now

    if (!productId || !alertCondition || !priceThreshold) {
      return c.json({ success: false, message: 'Missing required fields' }, 400);
    }

    const specString = specifications ? JSON.stringify(specifications) : null;

    const ps = c.env.DB.prepare(
      `INSERT INTO UserPriceAlerts (user_id, product_id, specifications, alert_condition, price_threshold)
       VALUES (?, ?, ?, ?, ?)`
    ).bind(userId, productId, specString, alertCondition, priceThreshold);

    const { meta } = await ps.run();
    
    return c.json({ success: true, alert_id: meta.last_row_id }, 201);
  } catch (e: any) {
    return c.json({ success: false, message: 'Failed to create alert', details: e.message }, 500);
  }
});

/**
 * Get all price alerts for the current user.
 */
prices.get('/alerts', async (c) => {
  try {
    const userIdParam = c.req.query('userId') || '1'; // Default userId to 1 for now

    const ps = c.env.DB.prepare(
      `SELECT * FROM UserPriceAlerts WHERE user_id = ? ORDER BY created_at DESC`
    ).bind(parseInt(userIdParam, 10));

    const { results } = await ps.all();
    return c.json({ success: true, data: results });
  } catch (e: any) {
    return c.json({ success: false, message: 'Failed to fetch alerts', details: e.message }, 500);
  }
});

/**
 * Update an existing price alert.
 */
prices.put('/alerts/:id', async (c) => {
  try {
    const alertId = c.req.param('id');
    const { specifications, alertCondition, priceThreshold, isActive } = await c.req.json();

    if (!alertId) {
      return c.json({ success: false, message: 'Alert ID is required' }, 400);
    }

    const specString = specifications ? JSON.stringify(specifications) : undefined;

    // Build query dynamically based on provided fields
    const fields: string[] = [];
    const params: any[] = [];

    if (specString !== undefined) { fields.push('specifications = ?'); params.push(specString); }
    if (alertCondition) { fields.push('alert_condition = ?'); params.push(alertCondition); }
    if (priceThreshold) { fields.push('price_threshold = ?'); params.push(priceThreshold); }
    if (isActive !== undefined) { fields.push('is_active = ?'); params.push(isActive ? 1 : 0); }
    
    fields.push('updated_at = CURRENT_TIMESTAMP');

    if (fields.length <= 1) {
        return c.json({ success: false, message: 'No fields to update' }, 400);
    }

    params.push(parseInt(alertId, 10));

    const ps = c.env.DB.prepare(
      `UPDATE UserPriceAlerts SET ${fields.join(', ')} WHERE alert_id = ?`
    ).bind(...params);

    await ps.run();

    return c.json({ success: true, message: `Alert ${alertId} updated successfully` });
  } catch (e: any) {
    return c.json({ success: false, message: 'Failed to update alert', details: e.message }, 500);
  }
});

/**
 * Delete a price alert.
 */
prices.delete('/alerts/:id', async (c) => {
  try {
    const alertId = c.req.param('id');
    if (!alertId) {
      return c.json({ success: false, message: 'Alert ID is required' }, 400);
    }

    const ps = c.env.DB.prepare('DELETE FROM UserPriceAlerts WHERE alert_id = ?').bind(parseInt(alertId, 10));
    await ps.run();

    return c.json({ success: true, message: `Alert ${alertId} deleted successfully` });
  } catch (e: any) {
    return c.json({ success: false, message: 'Failed to delete alert', details: e.message }, 500);
  }
});

// --- Alert Trigger Logic (Simulation) ---

/**
 * Checks all active user alerts against the latest prices.
 * This is a simulation and returns potential notifications instead of sending them.
 */
async function checkAlerts(db: D1Database) {
  const triggeredAlerts = [];

  // 1. Get all active user-defined alerts
  const activeAlerts = await db.prepare(
    `SELECT * FROM UserPriceAlerts WHERE is_active = 1`
  ).all();

  if (!activeAlerts.results || activeAlerts.results.length === 0) {
    return [];
  }

  // 2. For each alert, get the latest price and check the condition
  for (const alert of activeAlerts.results) {
    const alertRule = alert as { product_id: number; specifications: string | null; alert_condition: 'BELOW' | 'ABOVE'; price_threshold: number };

    // Get the most recent price for the product and specification
    const priceQuery = db.prepare(
      `SELECT current_price FROM PriceAdjustments
       WHERE product_id = ?
         AND (? IS NULL OR specification = ?)
       ORDER BY adjustment_date DESC
       LIMIT 1`
    ).bind(
        alertRule.product_id,
        alertRule.specifications ? JSON.parse(alertRule.specifications).volume : null,
        alertRule.specifications ? JSON.parse(alertRule.specifications).volume : null
    );

    const latestPriceRecord = await priceQuery.first<{ current_price: number }>();

    if (latestPriceRecord) {
      const currentPrice = latestPriceRecord.current_price;
      let shouldTrigger = false;

      if (alertRule.alert_condition === 'BELOW' && currentPrice < alertRule.price_threshold) {
        shouldTrigger = true;
      } else if (alertRule.alert_condition === 'ABOVE' && currentPrice > alertRule.price_threshold) {
        shouldTrigger = true;
      }

      if (shouldTrigger) {
        triggeredAlerts.push({
          alertRule,
          currentPrice,
          message: `Alert: Product ${alertRule.product_id} price is ${currentPrice}, which is ${alertRule.alert_condition.toLowerCase()} the threshold of ${alertRule.price_threshold}.`
        });
      }
    }
  }

  return triggeredAlerts;
}

/**
 * API endpoint to manually trigger the alert check simulation.
 */
prices.post('/alerts/check', async (c) => {
    try {
        const triggeredAlerts = await checkAlerts(c.env.DB);
        return c.json({
            success: true,
            message: `Alert check simulation complete. Found ${triggeredAlerts.length} triggered alerts.`,
            triggeredAlerts
        });
    } catch (e: any) {
        return c.json({ success: false, message: 'Failed to run alert check', details: e.message }, 500);
    }
});

/**
 * Export price data in various formats (CSV, JSON, Excel).
 * Query parameters:
 * - format: Export format ('csv', 'json', 'xlsx')
 * - products: Comma-separated list of product names
 * - startDate: Start date (YYYY-MM-DD)
 * - endDate: End date (YYYY-MM-DD)
 * - includeStats: Include statistics summary
 */
prices.get('/export', async (c) => {
  const { format = 'csv', products, startDate, endDate, includeStats = 'false' } = c.req.query();

  if (!products) {
    return c.json({ success: false, message: 'products parameter is required' }, 400);
  }

  const productList = products.split(',').map(p => p.trim());
  const placeholders = productList.map((_, i) => `?${i + 1}`).join(',');

  try {
    // Build query
    let query = `
      SELECT
        product_name,
        specification,
        adjustment_date,
        current_price,
        previous_price,
        price_change,
        adjustment_count,
        template_source
      FROM PriceAdjustments
      WHERE product_name IN (${placeholders})
    `;

    const params = [...productList];
    
    if (startDate) {
      query += ` AND adjustment_date >= ?${params.length + 1}`;
      params.push(startDate);
    }
    
    if (endDate) {
      query += ` AND adjustment_date <= ?${params.length + 1}`;
      params.push(endDate);
    }
    
    query += ` ORDER BY product_name, adjustment_date DESC`;

    const ps = c.env.DB.prepare(query).bind(...params);
    const { results } = await ps.all();

    if (!results || results.length === 0) {
      return c.json({ success: false, message: 'No data found for export' }, 404);
    }

    // Handle different export formats
    if (format === 'json') {
      const exportData: any = {
        export_date: new Date().toISOString(),
        parameters: {
          products: productList,
          start_date: startDate || null,
          end_date: endDate || null
        },
        data: results
      };

      if (includeStats === 'true') {
        exportData.statistics = await calculateExportStatistics(results);
      }

      return c.json(exportData, 200, {
        'Content-Type': 'application/json',
        'Content-Disposition': `attachment; filename="price-export-${new Date().toISOString().split('T')[0]}.json"`
      });
    } else if (format === 'csv') {
      const csv = convertToCSV(results);
      return c.text(csv, 200, {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="price-export-${new Date().toISOString().split('T')[0]}.csv"`
      });
    } else if (format === 'xlsx') {
      // For Excel export, we'll use XLSX library
      const XLSX = await import('xlsx');
      
      // Prepare workbook
      const wb = XLSX.utils.book_new();
      
      // Main data sheet
      const ws = XLSX.utils.json_to_sheet(results.map((row: any) => ({
        '产品名称': row.product_name,
        '规格': row.specification || '',
        '调价日期': row.adjustment_date,
        '当前价格': row.current_price,
        '原价格': row.previous_price || '',
        '价格差异': row.price_change || 0,
        '调价次数': row.adjustment_count || 1,
        '数据来源': row.template_source || ''
      })));
      
      XLSX.utils.book_append_sheet(wb, ws, '价格数据');
      
      // Statistics sheet if requested
      if (includeStats === 'true') {
        const stats = await calculateExportStatistics(results);
        const statsWs = XLSX.utils.json_to_sheet(stats);
        XLSX.utils.book_append_sheet(wb, statsWs, '统计摘要');
      }
      
      // Generate buffer
      const buf = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });
      
      return c.body(buf, 200, {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="price-export-${new Date().toISOString().split('T')[0]}.xlsx"`
      });
    } else {
      return c.json({ success: false, message: 'Invalid format. Supported formats: json, csv, xlsx' }, 400);
    }

  } catch (e: any) {
    console.error('Failed to export price data:', e);
    return c.json({
      success: false,
      message: 'Export failed',
      details: e.message
    }, 500);
  }
});

// Helper function to convert data to CSV
function convertToCSV(data: any[]): string {
  if (!data || data.length === 0) return '';
  
  // Headers
  const headers = [
    '产品名称',
    '规格',
    '调价日期',
    '当前价格',
    '原价格',
    '价格差异',
    '调价次数',
    '数据来源'
  ];
  
  // Rows
  const rows = data.map(row => [
    row.product_name,
    row.specification || '',
    row.adjustment_date,
    row.current_price,
    row.previous_price || '',
    row.price_change || 0,
    row.adjustment_count || 1,
    row.template_source || ''
  ]);
  
  // Combine headers and rows
  const csvContent = [
    headers.join(','),
    ...rows.map(row => row.map(cell => {
      // Escape values containing commas or quotes
      const value = String(cell);
      if (value.includes(',') || value.includes('"') || value.includes('\n')) {
        return `"${value.replace(/"/g, '""')}"`;
      }
      return value;
    }).join(','))
  ].join('\n');
  
  // Add BOM for Excel compatibility with Chinese characters
  return '\ufeff' + csvContent;
}

// Helper function to calculate statistics for export
async function calculateExportStatistics(data: any[]): Promise<any[]> {
  const productStats: { [key: string]: any } = {};
  
  data.forEach(row => {
    const key = row.product_name;
    
    if (!productStats[key]) {
      productStats[key] = {
        product_name: row.product_name,
        total_records: 0,
        date_range: {
          start: row.adjustment_date,
          end: row.adjustment_date
        },
        price_range: {
          min: row.current_price,
          max: row.current_price
        },
        total_changes: 0,
        avg_price: 0,
        prices: []
      };
    }
    
    const stats = productStats[key];
    stats.total_records++;
    stats.prices.push(row.current_price);
    
    // Update date range
    if (row.adjustment_date < stats.date_range.start) {
      stats.date_range.start = row.adjustment_date;
    }
    if (row.adjustment_date > stats.date_range.end) {
      stats.date_range.end = row.adjustment_date;
    }
    
    // Update price range
    if (row.current_price < stats.price_range.min) {
      stats.price_range.min = row.current_price;
    }
    if (row.current_price > stats.price_range.max) {
      stats.price_range.max = row.current_price;
    }
    
    // Count changes
    if (row.price_change && row.price_change !== 0) {
      stats.total_changes++;
    }
  });
  
  // Calculate averages and format results
  return Object.values(productStats).map(stats => {
    const avgPrice = stats.prices.reduce((a: number, b: number) => a + b, 0) / stats.prices.length;
    
    return {
      '产品名称': stats.product_name,
      '记录数': stats.total_records,
      '日期范围': `${stats.date_range.start} 至 ${stats.date_range.end}`,
      '最低价格': stats.price_range.min,
      '最高价格': stats.price_range.max,
      '平均价格': avgPrice.toFixed(2),
      '价格变动次数': stats.total_changes,
      '价格波动幅度': ((stats.price_range.max - stats.price_range.min) / stats.price_range.min * 100).toFixed(2) + '%'
    };
  });
}


export default prices;
