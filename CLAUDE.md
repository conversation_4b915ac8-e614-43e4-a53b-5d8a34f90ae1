# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a full-stack food production analytics system (春雪食品产销分析系统) built with Vue.js 3 frontend, Cloudflare Workers backend, and D1 database. The system analyzes inventory, sales, production, and pricing data for a food manufacturing company.

**Key Features:**
- Real-time dashboard with production/sales/inventory metrics
- Advanced price monitoring with automated alerts
- Excel-based data import pipeline with dual-engine architecture
- Inventory turnover analysis with 7-step filtering algorithm
- Production ratio tracking and anomaly detection

## Architecture

### Three-Tier Serverless Architecture
- **Frontend**: Vue.js 3 SPA with Vite, deployed on Cloudflare Pages
- **Backend**: Hono TypeScript API on Cloudflare Workers
- **Database**: Cloudflare D1 (SQLite-compatible) with optimized schemas

### Key Design Patterns
- Component-based frontend with domain-specific Pinia stores
- RESTful API design organized by business domains
- Modular data processing pipeline for Excel imports
- Edge computing for global performance

## Common Development Commands

### Frontend Development
```bash
cd frontend
npm run dev          # Development server with hot reload (localhost:3000)
npm run build        # Production build with code splitting
npm run build:staging        # Staging build
npm run build:analyze       # Build with bundle analysis
npm run test                 # Run Vitest unit tests
npm run test:ui             # Run tests with UI
npm run test:coverage       # Run tests with coverage report
npm run lint                # ESLint with auto-fix
npm run format              # Prettier formatting
npm run clean               # Clean build artifacts and cache
```

### Backend Development
```bash
cd backend
npm run dev          # Local Cloudflare Workers development (localhost:8787)
npm run start        # Alternative dev server on port 8787
npm run deploy       # Deploy to production Cloudflare Workers
npm run test         # Run backend tests with Vitest
npm run cf-typegen   # Generate Cloudflare Workers types
wrangler d1 execute chunxue-prod-db --remote --command="SELECT COUNT(*) FROM Products"
```

### Database Operations
```bash
# Schema operations
cd backend
npx wrangler d1 execute chunxue-prod-db --local --file=schema.sql    # Local D1 schema
npx wrangler d1 execute chunxue-prod-db --remote --file=schema.sql   # Production D1 schema

# Query operations
npx wrangler d1 execute chunxue-prod-db --remote --command="SELECT record_date, COUNT(*) FROM DailyMetrics GROUP BY record_date"
npx wrangler d1 execute chunxue-prod-db --remote --command="SELECT COUNT(*) FROM Products"
npx wrangler d1 execute chunxue-prod-db --remote --command="SELECT adjustment_date, COUNT(*) FROM PriceAdjustments GROUP BY adjustment_date ORDER BY adjustment_date DESC LIMIT 10"

# Backup operations
npx wrangler d1 backup list chunxue-prod-db
npx wrangler d1 backup create chunxue-prod-db
```

### Data Import (v2.0 - 重大架构升级)
```bash
cd data_import

# 日常使用 - 每日增量导入（推荐）
python3 daily_import_main.py
python3 daily_import_main.py --inventory-date 2025-07-28  # 指定库存业务日期

# 批量导入 - 历史数据/月度导入
python3 bulk_import_main.py --mode sql    # SQL文件方式（推荐）
python3 bulk_import_main.py --mode api    # API方式（备选）
python3 bulk_import_main.py --mode sales-only  # 紧急恢复
python3 bulk_import_main.py --mode sql --inventory-date 2025-07-26  # 指定库存日期

# 性能监控和调试
export ENABLE_PERFORMANCE_MONITORING=true
export SHOW_BATCH_SIZE_ESTIMATES=true
```

## Critical Business Rules & Data Processing (v2.0)

**⚠️ CRITICAL: These business rules must be preserved and never modified without explicit approval**

### 7-Step Inventory Filtering Algorithm
This is the core business logic in `data_processors.py:filter_products_for_inventory()`:

1. **Invalid Data Removal**: Remove completely empty rows and records with null product names
2. **Special Product Extraction**: Temporarily extract all "凤肠" products (protected from filtering)
3. **Customer Column Filtering**: Exclude records where customer is null, "副产品", or "鲜品"  
4. **Category Filtering**: Exclude records where material category is null, "副产品", or "生鲜品其他"
5. **Fresh Product Filtering**: Remove all products containing "鲜" character in product name
6. **Protected Product Restoration**: Re-add previously extracted "凤肠" products
7. **Deduplication**: Remove duplicate entries based on (product_id, date) combination

**Data Quality Validation Ranges:**
- Daily sales volume: 200-600 tons (flags >1000 tons as anomaly)
- Individual product sales: 0.5-6 tons per day (flags >35 tons as anomaly)
- Total inventory: 5,000-8,000 tons (current baseline: ~6,440 tons)
- Production ratios: 0.3%-99.6% range is normal

### Unit Consistency Rules
- **Database Storage**: All weights stored in kilograms (kg)
- **Frontend Display**: All weights displayed in tons (automatically converted)
- **Excel Import**: Automatically detect and convert various units to kg before storage

### Sales Data Processing (数据质量保障)
**v2.0解决的关键问题:**
- ❌ **v1.0问题**: 重复聚合导致销量放大1000+倍 (单日>1000吨，单品35吨)
- ✅ **v2.0修复**: 正确聚合逻辑，销量恢复正常范围 (200-600吨/日，单品0.5-6吨)

**优化后的处理逻辑:**
1. **数据聚合**: 只按日期和产品聚合，避免分类维度重复
2. **单位统一**: 数据库统一存储kg，前端显示转换为吨
3. **重复消除**: 使用字典结构确保(product_id, date)唯一性
4. **业务规则**: 保持鲜品包含，副产品排除的核心逻辑

### Production Data Processing (日期提取优化)
**v2.0新增自动日期检测:**
```python
# 智能日期字段检测
date_columns = ['入库日期', '生产日期', '单据日期', '过账日期']
# 自动提取Excel中实际生产日期，替代固定配置日期
```

**解决的问题:**
- ❌ **v1.0问题**: 产量数据集中在单一日期(7月29日)
- ✅ **v2.0修复**: 按实际生产日期分布，产销率计算准确

### Inventory Data Filtering (精准库存计算 + 智能日期)
**库存业务日期智能管理 (v2.0新增):**
- **自动检测模式**: `INVENTORY_BUSINESS_DATE = None` 时自动使用Excel文件修改时间
- **智能优先级**: 命令行参数 → 固定配置 → 文件时间 → 系统日期
- **日期验证**: 自动验证合理性，拒绝未来日期和过于久远的日期
- **前端查询优化**: 避免动态日期导致的数据缺失
- **库存总量修复**: 从错误的19吨恢复到正确的6,440吨

**使用方式:**
```bash
# 自动检测Excel文件修改时间作为库存日期
python3 daily_import_main.py

# 临时指定特定库存业务日期
python3 daily_import_main.py --inventory-date 2025-07-28
```

**严格筛选规则维持:**
- 排除鲜品(凤肠例外)和副产品
- 7步筛选算法确保数据准确性

### Database Schema & Performance

#### Core Tables Structure
```sql
Products (product_id, product_name, sku, category)
├── Primary Key: product_id (auto-increment)
├── Unique Constraints: product_name, sku
└── Used for: Product master data and categorization

DailyMetrics (record_id, record_date, product_id, production_volume, sales_volume, sales_amount, inventory_level, average_price, inventory_turnover_days)
├── Primary Key: record_id (auto-increment)  
├── Foreign Key: product_id → Products.product_id
├── Unique Constraint: (record_date, product_id) - prevents duplicate daily records
├── Indexes: record_date, product_id for fast queries
└── Used for: Time-series production/sales/inventory data

PriceAdjustments (adjustment_id, adjustment_date, product_id, product_name, previous_price, current_price, price_difference, category)
├── Primary Key: adjustment_id (auto-increment)
├── Foreign Key: product_id → Products.product_id  
├── Indexes: adjustment_date, product_id, price_difference
└── Used for: Price change history and monitoring

Users (user_id, username, password, created_at)
├── Primary Key: user_id (auto-increment)
├── Unique Constraint: username  
└── Used for: Authentication with JWT tokens
```

#### D1 Performance Configuration (v2.0 重大优化)
**双导入引擎架构:**
- **SQL文件导入**: 直接发送SQL文件到D1后端，绕过API限制，稳定性100%
- **API批次导入**: 灵活的批次处理，智能重试机制，适合增量数据

**性能突破:**
- SQL文件方式: ~850条记录/秒，22秒导入19,218条记录
- API方式: 表特定优化，DailyMetrics(10条/批次), PriceAdjustments(200条/批次)
- 智能策略: 自动检测数据量，推荐最优导入方式
- 网络容错: 指数退避+随机抖动，解决所有网络稳定性问题

**技术创新与数据质量保障:**
- INSERT OR REPLACE语法确保数据完整性
- 字典去重机制: 使用(product_id, date)键确保唯一性
- 智能日期检测: 自动识别多种Excel日期字段
- 单位统一管理: 数据库kg存储，前端吨显示
- 动态超时调整适配命令大小
- 实时批次大小估算防止API限制
- 错误分类重试机制

**数据质量验证:**
- 销量范围: 200-600吨/日 (修复前1000+吨异常)
- 产量分布: 按实际日期分布 (修复前集中单日)
- 库存准确: 6,440吨总量 (修复前仅19吨)
- 无重复记录: 10,024条去重后记录

## Frontend Architecture & Component Organization

### Component Hierarchy
```
src/components/
├── auth/                    # Authentication components
│   ├── AuthModal.vue       # Login/register modal
│   ├── LoginForm.vue       # Login form component  
│   └── RegisterForm.vue    # Registration form component
├── charts/                 # Reusable chart components (ECharts wrappers)
│   ├── EChartsWrapper.vue  # Base chart wrapper with performance optimization
│   ├── BarChart.vue        # Generic bar chart component
│   ├── LineChart.vue       # Generic line chart component
│   ├── PieChart.vue        # Generic pie chart component
│   └── SparklineChart.vue  # Mini trend charts for dashboard
├── common/                 # Base UI components
│   ├── BaseButton.vue      # Standardized button component
│   ├── BaseCard.vue       # Card layout wrapper
│   ├── BaseModal.vue      # Modal dialog wrapper
│   └── MonthSelector.vue  # Date range selection component
├── dashboard/             # Dashboard-specific components
├── inventory/             # Inventory analysis components
├── production/            # Production metrics components
├── sales/                # Sales analytics components
├── pricing/              # Price analysis components
└── priceMonitoring/      # Advanced price monitoring & alerts
```

### State Management (Pinia Stores)
```
src/stores/
├── auth.js        # User authentication state
├── dashboard.js   # Dashboard metrics and KPIs
├── dateRange.js   # Global date range selection
├── inventory.js   # Inventory data and analysis
├── production.js  # Production metrics and ratios
├── sales.js       # Sales data and trends
└── pricing.js     # Price data and monitoring alerts
```

### Utility Functions & Testing
```
src/utils/
├── api.js              # Axios HTTP client with interceptors
├── charts.js           # Chart configuration helpers
├── chartOptimizer.js   # Performance optimization for large datasets  
├── constants.js        # Application constants and enums
├── date.js            # Date formatting and manipulation utilities
├── formatters.js      # Number and currency formatting
├── validators.js      # Form validation helpers
├── performance.js     # Performance monitoring utilities
└── __tests__/         # Unit tests for utility functions
    ├── constants.test.js
    └── date.test.js
```

## Backend Architecture & API Design

### API Endpoint Organization
```
/api/dashboard/*     - Summary metrics and KPIs
├── /summary        - Overall system metrics
├── /trends         - Time-series trend data
└── /kpis          - Key performance indicators

/api/inventory/*     - Stock analysis endpoints  
├── /summary        - Inventory overview
├── /turnover       - Turnover analysis
├── /details        - Detailed inventory data
└── /trends         - Inventory trend analysis

/api/production/*    - Manufacturing metrics
├── /summary        - Production overview  
├── /ratios         - Production efficiency ratios
├── /trends         - Production trend analysis
└── /alerts         - Production anomaly detection

/api/sales/*         - Sales analytics
├── /summary        - Sales overview
├── /trends         - Sales trend analysis
├── /details        - Detailed sales data
└── /volume         - Sales volume analysis

/api/pricing/*       - Price monitoring
├── /key-products   - Price data for 254 non-fresh products
├── /trends         - Price trend analysis
├── /alerts         - Price drop alerts and notifications
└── /adjustments    - Price adjustment history
```

### Middleware & Security
- **CORS Handling**: Configured for cross-origin requests
- **JWT Authentication**: Token-based user authentication
- **Request Validation**: Input sanitization and validation
- **Error Handling**: Standardized error responses
- **Rate Limiting**: Built-in Cloudflare Workers rate limiting
## Data Import System Architecture (v2.0 革命性重构)

### Import Strategy Overview
```
data_import/
├── bulk_import_main.py      # 🚀 SQL文件导入 (推荐) - 大批量数据
├── daily_import_main.py     # ⚙️ API批次导入 (备选) - 增量数据  
├── bulk_sql_import.py       # SQL文件生成与执行引擎
├── data_processors.py       # 核心业务逻辑与数据质量保障
├── data_loader.py           # Excel文件统一加载
├── db_handler.py            # API数据库交互 (D1/SQLite)
├── config.py                # 智能配置管理
└── requirements.txt         # Python依赖
```

### Import Engines Comparison
| Feature | SQL文件导入 | API批次导入 |
|---------|------------|------------|
| **Performance** | ~850 records/sec | ~50-100 records/sec |
| **Reliability** | 100% success rate | 95-98% success rate |
| **Data Integrity** | Atomic transactions | Batch-level consistency |
| **API Limitations** | Bypasses 1MB limit | Subject to size limits |
| **Best For** | Bulk imports >5K records | Daily updates <1K records |
| **Rollback** | Full transaction rollback | Manual cleanup required |

### Core Business Logic Modules
**`data_processors.py` - 数据质量保障引擎:**
- `filter_products_for_inventory()` - 7步库存筛选算法 (核心业务逻辑)
- `process_sales_data()` - 销量聚合去重，修复数据放大问题
- `extract_production_dates()` - 智能日期检测，支持多种Excel日期字段
- `standardize_units()` - 单位统一转换 (kg存储，吨显示)

**`bulk_sql_import.py` - SQL直传引擎:**
- `generate_precision_delete_statements()` - 精确删除策略，防止误删
- `create_sql_file()` - 动态SQL文件生成
- `execute_sql_with_wrangler()` - 通过wrangler CLI执行

**`config.py` - 智能配置系统:**
- `get_inventory_business_date()` - 智能日期检测，支持文件时间自动推断
- `TABLE_BATCH_CONFIGS` - 表特定性能优化配置
- `validate_config()` - 配置完整性验证

## Troubleshooting & Common Issues

### Data Import Issues
1. **"wrangler command not found"**
   ```bash
   cd backend
   npm install    # Install wrangler locally
   npx wrangler login    # Authenticate
   npx wrangler d1 list  # Verify connection
   ```

2. **"配置验证失败" (Configuration validation failed)**
   ```bash
   cd data_import
   python3 config.py    # Check configuration
   # Verify Excel files exist in Excel文件夹/
   ```

3. **"数据质量异常" (Data quality anomaly)**
   - Sales volume >1000 tons/day → Check for duplicate aggregation
   - Inventory total <1000 tons → Verify 7-step filtering algorithm
   - Production concentrated on single date → Check date extraction logic

4. **"SQL文件导入失败" (SQL import failure)**
   ```bash
   # Check if database exists and is accessible
   npx wrangler d1 execute chunxue-prod-db --remote --command="SELECT 1"
   
   # Verify SQL file syntax
   cat generated_import_YYYYMMDD_HHMMSS.sql | head -20
   ```

### Frontend Development Issues
1. **Build failures with "Module not found"**
   ```bash
   cd frontend
   rm -rf node_modules package-lock.json
   npm install
   npm run build
   ```

2. **Chart rendering issues**
   - Check EChartsWrapper.vue for performance optimization
   - Verify chartOptimizer.js is handling large datasets
   - Monitor browser console for ECharts errors

3. **API connection issues**
   - Verify backend is running: `cd backend && npm run dev`
   - Check CORS configuration in backend/src/index.ts
   - Validate API base URL in frontend/src/utils/api.js

### Database Performance Issues
1. **Slow queries on DailyMetrics**
   ```sql
   -- Verify indexes exist
   PRAGMA index_list('DailyMetrics');
   
   -- Check query execution plan  
   EXPLAIN QUERY PLAN SELECT * FROM DailyMetrics WHERE record_date = '2025-07-28';
   ```

2. **D1 rate limiting**
   - Reduce batch sizes in config.py
   - Increase delays between API calls
   - Use SQL file import for large datasets

## Security Considerations

### Authentication & Authorization
- JWT tokens expire after 24 hours (configurable in backend)
- Passwords hashed with bcrypt (salt rounds: 10)
- No sensitive data logged in production

### Data Protection  
- Excel files contain business-sensitive data - never commit to git
- Database credentials stored in wrangler.toml (use secrets for production)
- API endpoints validate user permissions before data access

### Production Deployment Security
- Change JWT_SECRET in production wrangler.toml
- Enable Cloudflare security features (Bot Fight Mode, etc.)
- Monitor unusual API usage patterns via Cloudflare Analytics

## Development Best Practices

### Code Quality Standards
- **Frontend**: ESLint + Prettier for consistent formatting
- **Backend**: TypeScript strict mode with proper typing
- **Python**: PEP 8 compliance for data import scripts
- **Testing**: Unit tests required for utility functions and critical business logic

### Git Workflow
- Never commit Excel files or database files to git
- Use meaningful commit messages following conventional commits
- Test locally before pushing changes
- Always run linting and type checking before commits

### Performance Guidelines
- **Frontend**: Lazy load components, optimize chart rendering with chartOptimizer.js
- **Backend**: Use D1 indexes effectively, batch API calls when possible
- **Data Import**: Prefer SQL file import for bulk operations, API for incremental updates
- **Monitoring**: Enable performance monitoring for production deployments

### Critical Preservation Rules
⚠️ **NEVER modify without explicit approval:**
- 7-step inventory filtering algorithm in `data_processors.py`
- Unit consistency rules (kg storage, tons display)
- Database schema unique constraints and indexes
- Price monitoring business logic for 254 non-fresh products

### Development Environment Setup
1. **Prerequisites**: Node.js 18+, Python 3.8+, wrangler CLI
2. **Backend**: `cd backend && npm install && npm run dev`
3. **Frontend**: `cd frontend && npm install && npm run dev`  
4. **Data Import**: `cd data_import && pip install -r requirements.txt`
5. **Database**: Ensure wrangler is authenticated and D1 database exists

## Excel Data Sources

Required Excel files in `/Excel文件夹/`:
- `收发存汇总表查询.xlsx` - Inventory data
- `产成品入库列表.xlsx` - Production data  
- `销售发票执行查询.xlsx` - Sales data
- `调价表.xlsx` - Price adjustment data (multiple sheets by date)

## Configuration Management

### Environment Switching
In `data_import/config.py`:
- `USE_D1_DATABASE = True` - Use D1 vs local SQLite
- `USE_REMOTE_D1 = True` - Production vs development D1

### Key Configuration Parameters
```python
# Core performance configuration (config.py)
D1_BATCH_SIZE = 500                    # Default batch size
D1_MAX_RETRIES = 3                     # Retry count
D1_BATCH_DELAY = 0.5                   # Batch delay (seconds)
INVENTORY_BUSINESS_DATE = None         # Auto-detect or fixed date

# Table-specific optimization
TABLE_BATCH_CONFIGS = {
    'DailyMetrics': {'batch_size': 10, 'delay': 0.5},
    'PriceAdjustments': {'batch_size': 200, 'delay': 0.1}
}
```

## Testing Strategy

### Frontend Testing
- Vitest with jsdom environment
- Component testing with Vue Test Utils  
- Utility function unit tests in `src/utils/__tests__/`

### Backend Testing
- Vitest with Cloudflare Workers testing pool
- API endpoint testing in Workers environment

## Deployment Checklist

1. **Local Testing**: `npm run test` in both frontend and backend
2. **Backend Deployment**: `cd backend && npm run deploy`  
3. **Frontend Build**: `cd frontend && npm run build`
4. **API Verification**: Test endpoints with curl or Postman
5. **End-to-End Testing**: Verify all functionality works
6. **Monitoring**: Check Cloudflare dashboard for errors

## Production Environment

### API Base URL
`https://spring-snow-api.qu18354531302.workers.dev`

### Key Production Endpoints
- `/api/dashboard/summary` - Dashboard overview metrics
- `/api/inventory/summary` - Inventory analysis data  
- `/api/production/summary` - Production metrics & ratios
- `/api/sales/summary` - Sales trends and volume data
- `/api/prices/key-products` - Price monitoring (254 non-fresh products)
- `/api/pricing/alerts` - Price drop alerts and notifications

## Critical Production Notes

⚠️ **Data Import Rules:**
- **Never use seed scripts** like `import_inventory_data.ts` in production
- **Always use Python importers** (`bulk_import_main.py` or `daily_import_main.py`)
- Fresh products are **included** in sales but **excluded** from inventory calculations
- Price monitoring focuses on 254 non-fresh products for system stability

⚠️ **Business Logic Preservation:**
- 7-step inventory filtering algorithm must never be modified without approval
- Unit consistency (kg storage, tons display) is critical for data accuracy
- Database schema constraints ensure data integrity - preserve all indexes and foreign keys