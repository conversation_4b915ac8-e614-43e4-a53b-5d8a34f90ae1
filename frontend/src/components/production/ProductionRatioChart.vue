<template>
  <BaseCard title="产销率趋势分析" :loading="loading" :error="error">
    <template #extra>
      <div class="chart-controls">
        <select v-model="timeRange" @change="updateTimeRange" class="time-selector">
          <option value="7">近7天</option>
          <option value="30">近30天</option>
          <option value="90">近90天</option>
        </select>

        <BaseButton
          @click="toggleBaseline"
          variant="ghost"
          size="small"
        >
          {{ showBaseline ? '隐藏基准线' : '显示基准线' }}
        </BaseButton>
      </div>
    </template>

    <EChartsWrapper
      :options="chartOptions"
      :height="height"
      :loading="loading"
      @chart-ready="onChartReady"
      @chart-click="onChartClick"
    />

    <div class="ratio-summary">
      <div class="summary-item">
        <span class="label">平均产销率：</span>
        <span class="value" :class="getRatioColorClass(averageRatio)">
          {{ formatPercentage(averageRatio) }}
        </span>
        <!-- 临时调试信息 -->
        <span v-if="averageRatio !== 0" style="font-size: 0.8em; color: #666; margin-left: 8px;">
          ({{ averageRatio.toFixed(1) }}% - {{ new Date().toLocaleTimeString() }})
        </span>
      </div>
      <div class="summary-item">
        <span class="label">最高产销率：</span>
        <span class="value">{{ formatPercentage(maxRatio) }}</span>
      </div>
      <div class="summary-item">
        <span class="label">最低产销率：</span>
        <span class="value">{{ formatPercentage(minRatio) }}</span>
      </div>
      <div class="summary-item">
        <span class="label">趋势方向：</span>
        <span class="value" :class="trendColorClass">
          {{ trendIcon }} {{ trendText }}
        </span>
      </div>
    </div>
  </BaseCard>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useProductionStore } from '@/stores/production'
import { useDashboardStore } from '@/stores/dashboard'
import { storeToRefs } from 'pinia'
import { formatPercentage } from '@/utils/formatters'
import { calculateDateRange } from '@/utils/date'
import { getBaseChartOption, getTooltipConfig, getXAxisConfig, getYAxisConfig, getLineSeriesConfig, getGridConfig } from '@/utils/charts'
import { CHART_COLORS } from '@/utils/constants'
import BaseCard from '@/components/common/BaseCard.vue'
import BaseButton from '@/components/common/BaseButton.vue'
import EChartsWrapper from '@/components/charts/EChartsWrapper.vue'

const props = defineProps({
  startDate: {
    type: String
  },
  endDate: {
    type: String
  },
  height: {
    type: String,
    default: '400px'
  }
})

const emit = defineEmits(['chart-ready', 'chart-click', 'data-updated'])

const productionStore = useProductionStore()
const dashboardStore = useDashboardStore()
const loading = ref(false)
const error = ref('')
const timeRange = ref('30')
const showBaseline = ref(true)
const chartInstance = ref(null)

// 从 Store 获取响应式状态和计算属性
const { ratioData: chartData, ratioStats } = storeToRefs(productionStore)

// Use authoritative average from store instead of local calculation
const averageRatio = computed(() => {
  return productionStore.averageRatio
})
// Use store's computed properties for max and min ratios
const maxRatio = computed(() => {
  // If ratioStats is available, use it, otherwise calculate from chartData
  if (productionStore.ratioStats?.max_ratio) {
    return productionStore.ratioStats.max_ratio
  }
  return chartData.value.length > 0 ? Math.max(...chartData.value.map(item => item.ratio)) : 0
})

const minRatio = computed(() => {
  // If ratioStats is available, use it, otherwise calculate from chartData
  if (productionStore.ratioStats?.min_ratio) {
    return productionStore.ratioStats.min_ratio
  }
  return chartData.value.length > 0 ? Math.min(...chartData.value.map(item => item.ratio)) : 0
})

const trendDirection = computed(() => {
  if (chartData.value.length < 2) return 'stable'

  const first = chartData.value[0]?.ratio || 0
  const last = chartData.value[chartData.value.length - 1]?.ratio || 0

  if (last > first * 1.05) return 'increasing'
  if (last < first * 0.95) return 'decreasing'
  return 'stable'
})

const trendIcon = computed(() => {
  const icons = {
    increasing: '📈',
    decreasing: '📉',
    stable: '➡️'
  }
  return icons[trendDirection.value]
})

const trendText = computed(() => {
  const texts = {
    increasing: '上升趋势',
    decreasing: '下降趋势',
    stable: '平稳趋势'
  }
  return texts[trendDirection.value]
})

const trendColorClass = computed(() => {
  const classes = {
    increasing: 'trend-positive',
    decreasing: 'trend-negative',
    stable: 'trend-neutral'
  }
  return classes[trendDirection.value]
})

// 图表配置
const chartOptions = computed(() => {
  if (chartData.value.length === 0) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#999',
          fontSize: 14
        }
      }
    }
  }

  const dates = chartData.value.map(item => item.date)
  const ratios = chartData.value.map(item => item.ratio)

  const option = {
    ...getBaseChartOption(),
    title: {
      text: '产销率趋势分析',
      left: 'center',
      textStyle: {
        color: CHART_COLORS.PRIMARY,
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: getTooltipConfig((params) => {
      const point = params[0]
      return `${point.axisValue}<br/>产销率: ${formatPercentage(point.value)}`
    }),
    xAxis: getXAxisConfig(dates, { rotate: 45 }),
    yAxis: getYAxisConfig({
      name: '产销率(%)',
      formatter: (value) => value.toFixed(1) + '%',
      min: Math.max(0, Math.min(...ratios) - 10),
      max: Math.max(150, Math.max(...ratios) + 10)
    }),
    series: [
      getLineSeriesConfig('产销率', ratios, {
        color: CHART_COLORS.PRIMARY,
        smooth: true,
        area: true
      })
    ],
    grid: getGridConfig()
  }

  // 添加100%基准线
  if (showBaseline.value) {
    option.series[0].markLine = {
      data: [{
        yAxis: 100,
        lineStyle: {
          color: CHART_COLORS.ACCENT,
          type: 'dashed',
          width: 2
        },
        label: {
          formatter: '100%基准线',
          position: 'end',
          color: CHART_COLORS.ACCENT
        }
      }]
    }

    // 添加预警区域
    option.series[0].markArea = {
      data: [
        [
          { yAxis: 0, itemStyle: { color: 'rgba(217, 46, 46, 0.1)' } },
          { yAxis: 90, itemStyle: { color: 'rgba(217, 46, 46, 0.1)' } }
        ],
        [
          { yAxis: 110, itemStyle: { color: 'rgba(255, 149, 0, 0.1)' } },
          { yAxis: 200, itemStyle: { color: 'rgba(255, 149, 0, 0.1)' } }
        ]
      ]
    }
  }

  return option
})

// 方法
const fetchDataForRange = async (days) => {
  const dateRange = calculateDateRange(days)
  await fetchData(dateRange.start, dateRange.end)
}

const fetchData = async (startDate, endDate) => {
  loading.value = true
  error.value = ''

  try {
    // 强制清除缓存并重新获取数据
    console.log('🔄 [ProductionRatioChart] 开始获取数据...')

    // 获取production数据
    await productionStore.fetchAllProductionData(startDate, endDate)

    // 详细的诊断日志
    console.log('📊 [ProductionRatioChart] 数据获取完成:', {
      averageRatio: productionStore.averageRatio,
      chartDataLength: chartData.value?.length,
      timestamp: new Date().toISOString()
    })

    // 数据验证：如果数据异常，强制刷新
    if (averageRatio.value === 0) {
      console.warn('⚠️ [ProductionRatioChart] 检测到异常数据，尝试重新获取...')
      await new Promise(resolve => setTimeout(resolve, 1000)) // 等待1秒
      await productionStore.fetchAllProductionData(startDate, endDate)
    }

    emit('data-updated', productionStore.ratioData)
  } catch (err) {
    error.value = '数据加载失败'
    console.error('❌ [ProductionRatioChart] 数据加载失败:', err)
  } finally {
    loading.value = false
  }
}

const updateTimeRange = () => {
  console.log('Time range updated to:', timeRange.value)
  fetchDataForRange(parseInt(timeRange.value))
}

const toggleBaseline = () => {
  showBaseline.value = !showBaseline.value
  console.log('Baseline toggled:', showBaseline.value)
}

const getRatioColorClass = (ratio) => {
  if (ratio > 100) return 'trend-positive'
  if (ratio < 90) return 'trend-negative'
  return 'trend-neutral'
}

const onChartReady = (chart) => {
  chartInstance.value = chart
  emit('chart-ready', chart)
}

const onChartClick = (params) => {
  emit('chart-click', params)
}

// 监听props变化
watch([() => props.startDate, () => props.endDate], () => {
  if (props.startDate && props.endDate) {
    fetchData(props.startDate, props.endDate)
  }
})

onMounted(() => {
  // 如果父组件提供了日期，使用提供的日期
  if (props.startDate && props.endDate) {
    fetchData(props.startDate, props.endDate)
  } else {
    // 否则使用默认的30天范围
    fetchDataForRange(30)
  }
})

// 暴露方法
defineExpose({
  refresh: () => {
    if (props.startDate && props.endDate) {
      return fetchData(props.startDate, props.endDate)
    } else {
      return fetchDataForRange(parseInt(timeRange.value))
    }
  },
  getChart: () => chartInstance.value
})
</script>

<style scoped>
.chart-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.time-selector {
  padding: 6px 12px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.9em;
  background: white;
  cursor: pointer;
}

.time-selector:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.ratio-summary {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  padding: 16px;
  background: var(--background-light);
  border-radius: 8px;
  border-top: 1px solid #f0f0f0;
}

.summary-item {
  text-align: center;
  flex: 1;
}

.label {
  display: block;
  color: var(--text-secondary);
  font-size: 0.85em;
  margin-bottom: 4px;
}

.value {
  display: block;
  color: var(--primary-blue);
  font-weight: 600;
  font-size: 1em;
}

.trend-positive {
  color: var(--success-green);
}

.trend-negative {
  color: var(--signal-red);
}

.trend-neutral {
  color: var(--neutral-gray);
}

@media (max-width: 768px) {
  .chart-controls {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .ratio-summary {
    flex-direction: column;
    gap: 12px;
  }

  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .label, .value {
    display: inline;
    margin: 0;
  }
}
</style>
