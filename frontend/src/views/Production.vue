<template>
  <div class="production-page">
    <!-- 数据一致性警告 -->
    <ProductionAlerts />
    
    <!-- 产销率概览卡片 -->
    <div class="production-overview">
      <div class="overview-cards">
        <MetricCard
          title="平均产销率"
          :value="productionStore.averageRatio"
          unit="%"
          icon="📊"
          variant="primary"
          :loading="productionStore.isLoading"
          :format="formatPercentageValue"
          :class="getRatioColorClass(productionStore.averageRatio)"
        />
        <MetricCard
          title="总生产量"
          :value="productionStore.totalProduction"
          unit="T"
          icon="🏭"
          variant="secondary"
          :loading="productionStore.isLoading"
          :format="formatVolumeValue"
        />
        <MetricCard
          title="总销售量"
          :value="productionStore.totalSales"
          unit="T"
          icon="📦"
          variant="tertiary"
          :loading="productionStore.isLoading"
          :format="formatVolumeValue"
        />
        <MetricCard
          title="产销平衡"
          :value="productionStore.balanceStatus"
          icon="⚖️"
          variant="accent"
          :loading="productionStore.isLoading"
          :format="formatBalanceStatus"
        />
      </div>
    </div>

    <!-- 控制面板 -->
    <div class="production-controls">
      <!-- 快捷日期选择按钮 -->
      <div class="quick-date-buttons">
        <BaseButton
          v-for="option in quickDateOptions"
          :key="option.value"
          @click="selectQuickDate(option.value)"
          :variant="selectedQuickDate === option.value ? 'primary' : 'ghost'"
          size="small"
        >
          {{ option.label }}
        </BaseButton>
      </div>

      <div class="date-selectors">
        <div class="date-range-selector">
          <label>数据范围：</label>
          <input
            v-model="dateRange.start"
            type="date"
            @change="updateDateRange"
          />
          <span>至</span>
          <input
            v-model="dateRange.end"
            type="date"
            @change="updateDateRange"
          />
        </div>

        <div class="month-selector">
          <label>按月选择：</label>
          <MonthSelector
            v-model="monthRange"
            @change="handleMonthChange"
          />
        </div>

        <div class="threshold-selector">
          <label>预警阈值：</label>
          <input
            v-model="alertThreshold"
            type="number"
            min="50"
            max="150"
            step="5"
            @change="updateThreshold"
          />
          <span>%</span>
        </div>
      </div>

      <div class="control-buttons">
        <BaseButton
          @click="refreshData"
          :loading="productionStore.isLoading"
          variant="primary"
          size="small"
          icon="🔄"
        >
          刷新数据
        </BaseButton>

        <BaseButton
          @click="exportData"
          variant="secondary"
          size="small"
          icon="📊"
        >
          导出数据
        </BaseButton>
      </div>
    </div>

    <!-- 产销率趋势图表 -->
    <div class="production-charts">
      <div class="chart-section">
        <ProductionRatioTrendChart
          :start-date="dateRange.start"
          :end-date="dateRange.end"
          @data-updated="handleRatioDataUpdate"
          @date-range-changed="handleDateRangeChanged"
        />
      </div>

      <div class="chart-section">
        <ProductionVsSalesChart
          :start-date="dateRange.start"
          :end-date="dateRange.end"
        />
      </div>
    </div>

    <!-- 产销率分析表格 -->
    <div class="production-analysis">
      <ProductionAnalysisTable
        :start-date="dateRange.start"
        :end-date="dateRange.end"
        @export-data="handleExportData"
      />
    </div>

    <!-- 产销率预警 -->
    <div class="production-alerts">
      <ProductionAlerts
        :ratio-data="ratioData"
        :threshold="alertThreshold"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useProductionStore } from '@/stores/production'
import { formatPercentage, formatSalesVolume, formatInventory } from '@/utils/formatters'
import { useDateRange } from '@/composables/useDateRange'
import { calculateDateRange, getMonthRange } from '@/utils/date'
import MetricCard from '@/components/dashboard/MetricCard.vue'
import ProductionRatioTrendChart from '@/components/production/ProductionRatioTrendChart.vue'
import ProductionVsSalesChart from '@/components/production/ProductionVsSalesChart.vue'
import ProductionAnalysisTable from '@/components/production/ProductionAnalysisTable.vue'
import ProductionAlerts from '@/components/production/ProductionAlerts.vue'
import BaseButton from '@/components/common/BaseButton.vue'
import MonthSelector from '@/components/common/MonthSelector.vue'

const productionStore = useProductionStore()
const { dateRange: dynamicDateRange, ensureDateRangeLoaded } = useDateRange()

// 本地状态 - 使用动态计算的默认日期范围
const dateRange = ref({
  start: '',
  end: ''
})
const alertThreshold = ref(100)
const ratioData = ref([])
const monthRange = ref({ start: '', end: '' })

// 跟踪组件是否已完成初始化
const isInitialized = ref(false)

// 快捷日期选择选项
const quickDateOptions = ref([
  { label: '近7天', value: 7 },
  { label: '近30天', value: 30 },
  { label: '近90天', value: 90 }
])
const selectedQuickDate = ref(null)

// 格式化函数 - 无单位版本，避免重复显示
const formatPercentageValue = (value) => {
  return formatPercentage(value, 1, false) // 不显示单位
}

const formatVolumeValue = (value) => {
  return formatInventory(value, 'T', false) // 不显示单位
}

const formatBalanceStatus = (value) => {
  if (typeof value === 'string') return value
  // 按照产销率逻辑：>=100%为消费型，<100%为积压型
  if (value >= 100) return '消费型' // 销量>=产量，库存消耗
  if (value < 100) return '积压型'  // 销量<产量，产品积压
  return '产销平衡'
}

const getRatioColorClass = (ratio) => {
  // 按照用户要求：绿色>=100%表示消费，红色<100%表示积压
  if (ratio >= 100) return 'trend-positive' // 绿色：消费型，销量>=产量
  if (ratio < 100) return 'trend-negative'  // 红色：积压型，销量<产量
  return 'trend-neutral'
}

// 方法
const updateDateRange = async () => {
  try {
    await productionStore.updateDateRange(dateRange.value.start, dateRange.value.end)
  } catch (error) {
    console.error('Failed to update date range:', error)
  }
}

const updateThreshold = () => {
  console.log('Alert threshold updated to:', alertThreshold.value)
}

const refreshData = async () => {
  try {
    await productionStore.refreshData()
  } catch (error) {
    console.error('Failed to refresh production data:', error)
  }
}

const exportData = () => {
  // TODO: 实现数据导出功能
  console.log('Export production data')
}

const handleRatioDataUpdate = (data) => {
  ratioData.value = data
  console.log('Production ratio data updated:', data.length, 'records')
}

const handleExportData = (data) => {
  console.log('Export data:', data.length, 'records')
}

const handleDateRangeChanged = (newDateRange) => {
  // 更新日期范围
  dateRange.value = newDateRange
  // 刷新数据
  updateDateRange()
}

const handleMonthChange = (range) => {
  // 使用月份选择器的日期范围
  dateRange.value = range
  
  // 清除快捷日期选择
  selectedQuickDate.value = null
  
  // 触发数据更新（仅在初始化完成后）
  if (isInitialized.value) {
    updateDateRange()
  }
}

const selectQuickDate = (days) => {
  // 设置选中状态
  selectedQuickDate.value = days
  
  // 使用日期工具计算日期范围
  dateRange.value = calculateDateRange(days)
  
  // 触发数据更新（仅在初始化完成后）
  if (isInitialized.value) {
    updateDateRange()
  }
}

// 生命周期
onMounted(async () => {
  try {
    console.log('🚀 Production page mounted, initializing...')
    
    // 确保日期范围已加载
    await ensureDateRangeLoaded()
    
    // 设置初始日期范围（一次性）
    dateRange.value = {
      start: dynamicDateRange.value.start,
      end: dynamicDateRange.value.end
    }
    
    console.log('📅 Initial date range set:', dateRange.value)
    
    // 标记为已初始化，允许后续更新
    isInitialized.value = true
    
    // 初始化生产数据（如果还没有数据的话）
    if (!productionStore.hasData) {
      console.log('📊 Fetching initial production data...')
      await productionStore.fetchAllProductionData(dateRange.value.start, dateRange.value.end)
    }
    
    console.log('✅ Production page initialization complete')
  } catch (error) {
    console.error('❌ Failed to load production data:', error)
    // 即使出错也要标记为已初始化，避免卡住
    isInitialized.value = true
  }
})
</script>

<style scoped>
.production-page {
  padding: 0;
}

.production-overview {
  margin-bottom: 32px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.production-charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.chart-section {
  min-height: 400px;
}

.production-analysis {
  margin-bottom: 32px;
}

.production-alerts {
  margin-bottom: 32px;
}

.production-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
  background: var(--background-light);
  border-radius: 12px;
}

.quick-date-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.date-selectors {
  display: flex;
  gap: 24px;
  align-items: center;
  flex-wrap: wrap;
}

.date-range-selector,
.threshold-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.9em;
}

.date-range-selector label,
.threshold-selector label {
  color: var(--text-secondary);
  font-weight: 500;
  white-space: nowrap;
}

.date-range-selector input,
.threshold-selector input {
  padding: 6px 10px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.9em;
}

.date-range-selector input:focus,
.threshold-selector input:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.month-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.9em;
}

.month-selector label {
  color: var(--text-secondary);
  font-weight: 500;
}

.year-select,
.month-select {
  padding: 6px 10px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.9em;
  background: white;
  cursor: pointer;
}

.year-select:focus,
.month-select:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.control-buttons {
  display: flex;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .production-charts {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .overview-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .production-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .date-range-selector,
  .threshold-selector {
    justify-content: center;
    flex-wrap: wrap;
  }

  .control-buttons {
    justify-content: center;
  }
}
</style>
