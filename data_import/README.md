# 数据导入系统 - V3.0

## 1. 概述

本系统是对原始数据导入脚本的全面重构和优化，旨在提供一个模块化、可维护、可扩展且高效的数据导入解决方案。系统核心设计围绕两种导入策略：**SQL文件导入**（推荐，适用于大批量数据）和 **API批次导入**（备选，适用于小批量或实时性要求高的场景），以应对不同的业务需求和技术限制。

本次更新在保留了原有的核心数据处理逻辑（如库存“7步筛选法”）的基础上，全面增强了系统的健壮性、灵活性和用户友好性。

## 2. 核心导入策略对比

| 特性 | SQL文件导入 (`bulk_import_main.py`) | API批次导入 (`daily_import_main.py`) |
| :--- | :--- | :--- |
| **推荐度** | ⭐⭐⭐⭐⭐ **(推荐)** | ⭐⭐⭐ (备选) |
| **原理** | 生成完整的SQL脚本，通过 `wrangler d1 execute --file` 命令执行。 | 通过HTTP API，分批次向Cloudflare Worker发送JSON数据。 |
| **优点** | - **高稳定性**: 无API请求大小限制，不受网络波动影响。<br>- **原子性**: 整个SQL文件作为一个事务执行，保证数据一致性。<br>- **高性能**: 适合处理数万至数百万条记录的大批量数据。 | - **高灵活性**: 可以在代码中实现更复杂的实时处理逻辑。<br>- **实时性**: 数据可以更快地反映在系统中。 |
| **缺点** | - **实时性稍差**: 需要先生成文件再执行。<br>- **依赖 `wrangler` CLI**: 需要本地环境正确配置。 | - **有API限制**: 受Cloudflare Worker的1MB请求体大小限制。<br>- **网络依赖**: 需要处理网络重试和超时。<br>- **性能较低**: 大量API请求开销较大。 |
| **适用场景** | **首次全量导入、月度/季度数据迁移、数据恢复。** | **每日增量更新、小批量数据修正。** |

## 3. 架构概述

系统采用了更精细化的模块分工，并提供了两个主入口脚本以支持不同的导入策略。

```
data_import/
├── __init__.py              # 包初始化文件
├── bulk_import_main.py      # 🚀 主入口：SQL文件导入 (推荐)
├── daily_import_main.py     # ⚙️  备选入口：API批次导入
├── bulk_sql_import.py       # SQL文件生成与执行模块
|
├── config.py                # 集中化配置管理
├── data_loader.py           # Excel数据加载模块
├── data_processors.py       # 核心数据处理与清洗模块
└── db_handler.py            # 数据库交互模块 (API方式)
```

## 4. 模块说明

### 4.1. `config.py` - 配置管理
集中管理所有配置，包括文件路径、数据库设置、功能开关 (`USE_D1_DATABASE`) 和性能参数。

### 4.2. `data_loader.py` - 数据加载
负责从指定的Excel文件（如 `收发存汇总表查询.xlsx`, `产成品入库列表.xlsx` 等）加载原始数据到Pandas DataFrame。

### 4.3. `data_processors.py` - 数据处理
包含所有核心业务逻辑，负责数据清洗、转换和格式化。

#### **核心处理逻辑：库存“7步筛选法”**
为了确保库存数据的准确性，系统在 `filter_products_for_inventory` 函数中实现了一套严格的7步筛选法，该方法是系统的关键业务逻辑之一，**必须保留**：

1.  **(准备)** 统计原始库存总量，用于前后对比。
2.  **移除无效行**: 删除所有完全为空的行，以及“物料名称”为空的记录。
3.  **特殊保留“凤肠”**: 暂时提取所有包含“凤肠”的产品数据，防止在后续步骤中被错误过滤。
4.  **按“客户”列筛选**: 移除“客户”列为空、或为“副产品”、“鲜品”的记录。
5.  **按“物料分类名称”筛选**: 移除“物料分类名称”为空、或为“副产品”、“生鲜品其他”的记录。
6.  **筛选“鲜”产品**: 从剩余数据中，移除所有物料名称包含“鲜”字的产品。
7.  **重新添加“凤肠”**: 将第3步中提取的“凤肠”产品数据重新合并回结果集，并去重。

此流程确保了最终导入的库存数据是经过精确清洗的。

### 4.4. `db_handler.py` - 数据库操作 (API方式)
封装了通过API与数据库（Cloudflare D1 或本地 SQLite）交互的所有功能。**此模块主要由 `daily_import_main.py` 使用。**

### 4.5. `bulk_sql_import.py` - SQL文件导入模块
此模块是 **SQL文件导入** 策略的核心。
- **职责**:
    - 根据处理好的数据，动态生成包含 `DELETE` 和 `INSERT` 语句的 `.sql` 文件。
    - 调用 `wrangler d1 execute` 命令来执行生成的SQL文件。
- **核心数据完整性策略：精确删除后插入 (Precision-Delete-Then-Insert)**
    - **动态检测数据类型**: 自动识别本次导入涉及的数据类型（如销售、生产、库存）。
    - **生成靶向`DELETE`语句**: 根据检测到的数据类型和日期范围，生成高度精确的`DELETE`语句。例如，更新销售数据时，绝不会触及同一时期的生产或库存数据。
      ```sql
      -- 示例: 只删除2025-07-01至2025-07-31的销售和生产数据
      DELETE FROM DailyMetrics WHERE record_date BETWEEN '2025-07-01' AND '2025-07-31' AND data_type = '销售';
      DELETE FROM DailyMetrics WHERE record_date BETWEEN '2025-07-01' AND '2025-07-31' AND data_type = '生产';
      ```
    - **原子性执行**: 整个SQL文件被D1作为一个事务处理，保证了操作的原子性。

### 4.6. 主入口脚本

- **`bulk_import_main.py` (推荐)**: 协调所有模块，执行“加载 -> 处理 -> 生成SQL -> 执行SQL”的完整流程。这是最稳定、最高效的导入方式。
- **`daily_import_main.py` (备选)**: 协调执行“加载 -> 处理 -> API分批上传”的流程。

## 5. 🚀 使用方法

### 5.1. 推荐：使用SQL文件导入 (全量/大批量)

这是最常用、最稳定的方式。

```bash
# 切换到项目根目录
cd /path/to/your/project

# 执行默认的SQL文件导入模式
python3 data_import/bulk_import_main.py

# 或者显式指定模式
python3 data_import/bulk_import_main.py --mode sql
```

#### **命令行参数**
- `--mode`: 导入模式。可选值为 `sql` (默认), `api`, `sales-only`。
- `--inventory-date`: 指定库存数据的业务日期 (格式: YYYY-MM-DD)。如果未提供，则使用 `config.py` 中的默认值。
  ```bash
  # 为库存数据指定一个特定的日期
  python3 data_import/bulk_import_main.py --inventory-date 2025-06-30
  ```

### 5.2. 备选：使用API导入 (每日增量/小批量)

```bash
# 执行API导入模式
python3 data_import/bulk_import_main.py --mode api
```
*注意：这实际上会调用 `daily_import_main.py` 的逻辑。*

### 5.3. 紧急情况：仅导入销售数据

此模式用于需要快速恢复销售数据而无需处理其他数据的场景。它会跳过库存和生产数据的处理。

```bash
# 仅导入销售数据
python3 data_import/bulk_import_main.py --mode sales-only
```

## 6. ⚙️ 性能与错误处理

### 性能优化
- **SQL文件导入**: 通过将所有操作合并到一个SQL事务中，避免了数千次API调用的网络开销，性能极高。
- **API批次导入**:
    - `D1_BATCH_SIZE`: 可在 `config.py` 中配置，用于控制每次API请求发送的数据量（默认200条）。
    - `D1_MAX_RETRIES`: 失败请求的最大重试次数（默认5次）。
    - `D1_BATCH_DELAY`: 批次请求之间的延迟，防止API速率限制（默认0.3秒）。

### 故障排除
1.  **配置验证失败**: 检查 `config.py` 中的Excel文件路径和数据库名称是否正确。
2.  **`wrangler` 命令失败**:
    - 确认 `wrangler` 已正确安装并登录 (`npx wrangler login`)。
    - 检查 `wrangler.toml` 文件是否存在于 `backend` 目录且配置正确。
    - 如果使用远程模式 (`--remote`)，请确保网络连接正常。
3.  **数据结构验证失败**: 检查源Excel文件的列名是否与预期一致。脚本在加载数据后会进行结构验证。
4.  **内存不足**: 如果处理超大文件时遇到内存问题，请考虑分批处理源文件。

## 7. 维护和扩展

### 添加新的数据源
1.  在 `config.py` 中添加新的文件路径配置。
2.  在 `data_loader.py` 中添加新的加载函数。
3.  在 `data_processors.py` 中添加相应的处理逻辑。
4.  在 `bulk_import_main.py` 和 `daily_import_main.py` 的主流程中集成新数据。

### 修改数据处理逻辑
- 所有数据清洗和转换逻辑都集中在 `data_processors.py` 中。
- 特别是库存相关的逻辑，请谨慎修改 `filter_products_for_inventory` 函数。

## 8. 📈 改进成果

- ✅ **双策略支持**: 提供了SQL和API两种导入方式，灵活应对不同场景。
- ✅ **健壮性**: SQL导入方式保证了大批量数据导入的原子性和稳定性。
- ✅ **可维护性**: 模块化设计，职责清晰，易于维护和扩展。
- ✅ **文档完整性**: 详细记录了核心业务逻辑（如“7步筛选法”）和架构决策。
- ✅ **用户友好性**: 提供了清晰的命令行接口和模式选择，简化了操作。

## 9. 🤝 贡献

- **代码风格**: 遵循 PEP 8 规范。
- **提交规范**:
    - `feat`: 新功能
    - `fix`: 错误修复
    - `docs`: 文档更新
    - `refactor`: 代码重构