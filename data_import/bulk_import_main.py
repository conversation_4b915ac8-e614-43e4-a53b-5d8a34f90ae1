"""
优化的数据导入主程序
支持SQL文件导入和优化的API导入两种方式
优先使用SQL文件导入方式，确保大批量数据的稳定导入
"""
import sys
import os
import argparse
from datetime import datetime

# Add the parent directory to the path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data_import.config import (
    print_config_info, validate_config, get_database_mode,
    get_inventory_business_date, get_production_business_date
)
from data_import.data_loader import load_all_data, validate_data_structure
from data_import.data_processors import (
    process_inventory_data, process_production_data, process_sales_data,
    process_price_adjustments_data, get_all_unique_products
)
from data_import.db_handler import create_products_if_not_exist
from data_import.bulk_sql_import import import_data_via_sql_files, verify_import_results
from data_import.daily_import_main import prepare_daily_metrics_data, prepare_price_adjustments_data


def print_import_strategy_info():
    """打印导入策略信息"""
    print("\n" + "="*60)
    print("数据导入策略说明")
    print("="*60)
    print("方式1: SQL文件导入 (推荐)")
    print("  ✅ 优点: 稳定性高，无API限制，适合大批量数据")
    print("  ✅ 原理: 直接将SQL文件发送给D1后端处理")
    print("  ✅ 适用: 大批量数据导入，网络不稳定环境")
    print()
    print("方式2: API批次导入 (备选)")
    print("  ⚠️  优点: 灵活性高，可自定义处理逻辑")
    print("  ⚠️  缺点: 有1MB请求限制，需要处理网络重试")
    print("  ⚠️  适用: 小批量数据，实时性要求高")
    print("="*60)


def main_sql_file_import():
    """使用SQL文件方式进行导入（推荐）"""
    print("="*60)
    print("优化数据导入系统 - SQL文件模式")
    print("="*60)
    
    # Print configuration and strategy
    print_config_info()
    print_import_strategy_info()
    
    # Validate configuration
    print("\n=== Configuration Validation ===")
    config_errors = validate_config()
    if config_errors:
        print("❌ Configuration validation failed:")
        for error in config_errors:
            print(f"  - {error}")
        return False
    else:
        print("✅ Configuration validation passed")
    
    try:
        # Step 1: Load data
        print("\n" + "="*50)
        print("STEP 1: LOADING DATA")
        print("="*50)
        
        data = load_all_data()
        
        if not validate_data_structure(data):
            print("❌ Data structure validation failed")
            return False
        
        # Step 2: Process data
        print("\n" + "="*50)
        print("STEP 2: PROCESSING DATA")
        print("="*50)
        
        df_inventory = process_inventory_data(data['inventory'])
        df_production = process_production_data(data['production'])
        df_sales = process_sales_data(data['sales'])
        df_price_adjustments = process_price_adjustments_data(data['price_adjustments'])
        
        # 打印数据统计
        print(f"\n数据处理结果:")
        print(f"  库存数据: {len(df_inventory)} 条记录")
        print(f"  生产数据: {len(df_production)} 条记录")
        print(f"  销售数据: {len(df_sales)} 条记录")
        print(f"  调价数据: {len(df_price_adjustments)} 条记录")
        
        # Step 3: Prepare products
        print("\n" + "="*50)
        print("STEP 3: PREPARING PRODUCTS")
        print("="*50)
        
        all_products = get_all_unique_products(
            df_inventory, df_production, df_sales, df_price_adjustments
        )
        
        product_mapping = create_products_if_not_exist(all_products)
        
        if not product_mapping:
            print("❌ Failed to create or retrieve product mappings")
            return False
        
        print(f"✅ Product mapping ready with {len(product_mapping)} products")
        
        # Step 4: Prepare data for insertion
        print("\n" + "="*50)
        print("STEP 4: PREPARING DATA FOR INSERTION")
        print("="*50)
        
        daily_metrics_data = prepare_daily_metrics_data(
            df_inventory, df_production, df_sales, product_mapping
        )
        
        price_adjustments_data = prepare_price_adjustments_data(
            df_price_adjustments, product_mapping
        )
        
        print(f"\n准备插入的数据:")
        print(f"  DailyMetrics: {len(daily_metrics_data)} 条记录")
        print(f"  PriceAdjustments: {len(price_adjustments_data)} 条记录")
        
        # Step 5: Import via SQL files (推荐方式)
        print("\n" + "="*50)
        print("STEP 5: SQL FILE IMPORT")
        print("="*50)
        
        import_success = import_data_via_sql_files(daily_metrics_data, price_adjustments_data)
        
        if not import_success:
            print("❌ SQL文件导入失败")
            return False
        
        # Step 6: Verification
        print("\n" + "="*50)
        print("STEP 6: VERIFICATION")
        print("="*50)
        
        verification_success = verify_import_results()
        
        # Final summary
        print("\n" + "="*60)
        print("IMPORT SUMMARY")
        print("="*60)
        
        if import_success and verification_success:
            print("🎉 数据导入完全成功！")
            print(f"✅ DailyMetrics: {len(daily_metrics_data)} 条记录")
            print(f"✅ PriceAdjustments: {len(price_adjustments_data)} 条记录")
            print(f"✅ Products: {len(product_mapping)} 个产品")
            print()
            print("🚀 现在可以检查前端数据显示是否正常")
        else:
            print("⚠️ 数据导入过程中存在问题，请检查上面的日志")
        
        return import_success and verification_success
        
    except Exception as e:
        print(f"\n❌ 导入过程中发生致命错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def main_api_import():
    """使用原有API方式进行导入（备选方案）"""
    print("使用原有API方式导入，请运行: python3 daily_import_main.py")
    from data_import.daily_import_main import main
    return main()


def quick_sales_only_import():
    """快速导入仅销售数据（用于紧急恢复）"""
    print("="*60)
    print("快速销售数据导入模式")
    print("="*60)
    
    try:
        # 只加载和处理销售数据
        print("加载销售数据...")
        data = load_all_data()
        df_sales = process_sales_data(data['sales'])
        
        print(f"处理了 {len(df_sales)} 条销售记录")
        
        # 获取现有产品映射
        from data_import.db_handler import execute_query
        products_query = "SELECT product_name, id FROM Products"
        products_result = execute_query(products_query)
        
        if not products_result:
            print("❌ 无法获取产品映射，请先运行完整导入")
            return False
        
        # 构建产品映射
        if isinstance(products_result[0], dict):
            product_mapping = {row['product_name']: row['id'] for row in products_result}
        else:
            product_mapping = {row[0]: row[1] for row in products_result}
        
        print(f"获取到 {len(product_mapping)} 个产品映射")
        
        # 准备销售数据
        daily_metrics_data = []
        for _, row in df_sales.iterrows():
            product_name = row['product_name']
            if product_name in product_mapping:
                record = [
                    product_mapping[product_name],  # product_id
                    product_name,                   # product_name
                    row.get('record_date'),        # date
                    0.0,                           # inventory_kg
                    0.0,                           # production_kg
                    float(row.get('sales_volume', 0)),  # sales_kg
                    float(row.get('sales_amount', 0))   # inventory_value
                ]
                daily_metrics_data.append(record)
        
        print(f"准备插入 {len(daily_metrics_data)} 条销售记录")
        
        # 使用SQL文件方式导入
        success = import_data_via_sql_files(daily_metrics_data, [])
        
        if success:
            print("✅ 销售数据导入成功")
            verify_import_results()
        else:
            print("❌ 销售数据导入失败")
        
        return success
        
    except Exception as e:
        print(f"❌ 快速销售数据导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='优化的数据导入系统')
    parser.add_argument('--mode', choices=['sql', 'api', 'sales-only'], 
                       default='sql', help='导入模式选择')
    parser.add_argument('--inventory-date', 
                       help='库存数据的业务日期 (YYYY-MM-DD格式)，如不指定则使用config中的默认值')
    
    args = parser.parse_args()
    
    # 如果指定了库存日期，更新配置
    if args.inventory_date:
        from data_import.config import INVENTORY_BUSINESS_DATE
        import data_import.config as config
        config.INVENTORY_BUSINESS_DATE = args.inventory_date
        print(f"🔧 使用命令行指定的库存业务日期: {args.inventory_date}")
    
    print(f"启动时间: {datetime.now()}")
    print(f"选择的导入模式: {args.mode}")
    
    if args.mode == 'sql':
        success = main_sql_file_import()
    elif args.mode == 'api':
        success = main_api_import()
    elif args.mode == 'sales-only':
        success = quick_sales_only_import()
    else:
        print("❌ 未知的导入模式")
        success = False
    
    if success:
        print(f"\n✅ 导入完成于: {datetime.now()}")
        sys.exit(0)
    else:
        print(f"\n❌ 导入失败于: {datetime.now()}")
        sys.exit(1)