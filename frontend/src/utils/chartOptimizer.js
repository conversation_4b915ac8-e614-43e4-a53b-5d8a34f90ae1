/**
 * ECharts Performance Optimizer - 图表性能优化器
 * 提供图表渲染优化、数据采样、延迟加载等功能
 */

import * as echarts from 'echarts';

class ChartPerformanceOptimizer {
  constructor() {
    // 性能配置
    this.config = {
      sampleThreshold: 500,      // 数据采样阈值
      animationDuration: 300,    // 动画持续时间
      updateDelay: 100,          // 更新延迟
      maxCanvasSize: 16384,      // 最大画布尺寸
      progressiveThreshold: 1000, // 渐进渲染阈值
      renderBatchSize: 100       // 批量渲染大小
    };
    
    // 渲染队列
    this.renderQueue = [];
    this.renderTimer = null;
    
    // 图表实例缓存
    this.chartInstances = new WeakMap();
  }

  /**
   * 创建优化的图表实例
   */
  createChart(container, theme = null, opts = {}) {
    const optimizedOpts = {
      renderer: 'canvas',
      useDirtyRect: true,
      ...opts
    };
    
    const chart = echarts.init(container, theme, optimizedOpts);
    
    // 存储图表配置
    this.chartInstances.set(chart, {
      container,
      lastUpdate: 0,
      dataCount: 0,
      isOptimized: false
    });
    
    return chart;
  }

  /**
   * 优化图表配置
   */
  optimizeOption(option, dataCount) {
    const optimized = { ...option };
    
    // 根据数据量调整配置
    if (dataCount > this.config.sampleThreshold) {
      // 关闭或简化动画
      optimized.animation = false;
      
      // 使用更简单的tooltip
      if (optimized.tooltip) {
        optimized.tooltip = {
          ...optimized.tooltip,
          transitionDuration: 0,
          extraCssText: 'box-shadow: none;'
        };
      }
      
      // 优化数据区域缩放
      if (optimized.dataZoom) {
        optimized.dataZoom = optimized.dataZoom.map(zoom => ({
          ...zoom,
          throttle: 100,
          rangeMode: ['percent', 'percent']
        }));
      }
    } else {
      // 较少数据时使用平滑动画
      optimized.animation = true;
      optimized.animationDuration = this.config.animationDuration;
      optimized.animationEasing = 'cubicOut';
    }
    
    // 设置渐进渲染
    if (dataCount > this.config.progressiveThreshold) {
      optimized.progressive = this.config.progressiveThreshold;
      optimized.progressiveThreshold = this.config.progressiveThreshold;
    }
    
    return optimized;
  }

  /**
   * 数据采样 - LTTB算法实现
   * (Largest Triangle Three Buckets)
   */
  sampleData(data, threshold) {
    if (!data || data.length <= threshold) return data;
    
    const sampled = [];
    const bucketSize = (data.length - 2) / (threshold - 2);
    
    // 始终保留第一个和最后一个点
    sampled.push(data[0]);
    
    let a = 0;
    for (let i = 0; i < threshold - 2; i++) {
      // 计算桶的范围
      const avgRangeStart = Math.floor((i + 1) * bucketSize) + 1;
      const avgRangeEnd = Math.floor((i + 2) * bucketSize) + 1;
      
      // 计算下一个桶的平均点
      let avgX = 0, avgY = 0;
      const avgRangeLength = avgRangeEnd - avgRangeStart;
      
      for (let j = avgRangeStart; j < avgRangeEnd && j < data.length; j++) {
        avgX += data[j][0] || j;
        avgY += data[j][1] || data[j];
      }
      avgX /= avgRangeLength;
      avgY /= avgRangeLength;
      
      // 在当前桶中找到面积最大的点
      const rangeStart = Math.floor(i * bucketSize) + 1;
      const rangeEnd = Math.floor((i + 1) * bucketSize) + 1;
      
      let maxArea = -1;
      let maxAreaIndex = rangeStart;
      
      for (let j = rangeStart; j < rangeEnd && j < data.length; j++) {
        const area = Math.abs(
          (data[a][0] - avgX) * (data[j][1] - data[a][1]) -
          (data[a][0] - data[j][0]) * (avgY - data[a][1])
        );
        
        if (area > maxArea) {
          maxArea = area;
          maxAreaIndex = j;
        }
      }
      
      sampled.push(data[maxAreaIndex]);
      a = maxAreaIndex;
    }
    
    sampled.push(data[data.length - 1]);
    
    return sampled;
  }

  /**
   * 优化系列数据
   */
  optimizeSeries(series, totalDataPoints) {
    return series.map(s => {
      const optimized = { ...s };
      
      // 数据采样
      if (Array.isArray(s.data) && s.data.length > this.config.sampleThreshold) {
        optimized.data = this.sampleData(s.data, this.config.sampleThreshold);
        optimized.sampling = 'lttb';
      }
      
      // 根据数据量调整渲染配置
      if (totalDataPoints > 1000) {
        optimized.symbol = 'none'; // 不显示数据点
        optimized.smooth = false;  // 关闭平滑曲线
        
        if (optimized.lineStyle) {
          optimized.lineStyle = {
            ...optimized.lineStyle,
            width: Math.min(optimized.lineStyle.width || 2, 2)
          };
        }
      }
      
      // 大数据量时简化标记
      if (totalDataPoints > 5000) {
        delete optimized.markPoint;
        delete optimized.markLine;
        delete optimized.markArea;
      }
      
      return optimized;
    });
  }

  /**
   * 批量更新图表
   */
  batchUpdate(chart, option, notMerge = false) {
    const meta = this.chartInstances.get(chart);
    if (!meta) {
      chart.setOption(option, notMerge);
      return;
    }
    
    // 防抖处理
    const now = Date.now();
    if (now - meta.lastUpdate < this.config.updateDelay) {
      this.queueRender(chart, option, notMerge);
      return;
    }
    
    meta.lastUpdate = now;
    
    // 计算总数据点数
    let totalDataPoints = 0;
    if (option.series) {
      option.series.forEach(s => {
        if (Array.isArray(s.data)) {
          totalDataPoints += s.data.length;
        }
      });
    }
    
    meta.dataCount = totalDataPoints;
    
    // 优化配置
    const optimizedOption = this.optimizeOption(option, totalDataPoints);
    
    // 优化系列数据
    if (optimizedOption.series) {
      optimizedOption.series = this.optimizeSeries(optimizedOption.series, totalDataPoints);
    }
    
    // 大数据量时使用静默更新
    if (totalDataPoints > 5000) {
      chart.setOption(optimizedOption, {
        notMerge,
        silent: true,
        replaceMerge: ['series']
      });
    } else {
      chart.setOption(optimizedOption, notMerge);
    }
  }

  /**
   * 队列渲染
   */
  queueRender(chart, option, notMerge) {
    this.renderQueue.push({ chart, option, notMerge });
    
    if (this.renderTimer) {
      clearTimeout(this.renderTimer);
    }
    
    this.renderTimer = setTimeout(() => {
      this.processRenderQueue();
    }, this.config.updateDelay);
  }

  /**
   * 处理渲染队列
   */
  processRenderQueue() {
    const batch = this.renderQueue.splice(0, this.config.renderBatchSize);
    
    batch.forEach(({ chart, option, notMerge }) => {
      const meta = this.chartInstances.get(chart);
      if (meta) {
        meta.lastUpdate = Date.now();
      }
      chart.setOption(option, notMerge);
    });
    
    if (this.renderQueue.length > 0) {
      this.renderTimer = setTimeout(() => {
        this.processRenderQueue();
      }, 16); // 约60fps
    }
  }

  /**
   * 动态调整图表大小
   */
  resizeChart(chart, width, height) {
    const meta = this.chartInstances.get(chart);
    if (!meta) {
      chart.resize({ width, height });
      return;
    }
    
    // 限制最大尺寸
    const maxSize = this.config.maxCanvasSize;
    const actualWidth = Math.min(width || chart.getWidth(), maxSize);
    const actualHeight = Math.min(height || chart.getHeight(), maxSize);
    
    // 大图表使用防抖
    if (meta.dataCount > 1000) {
      if (meta.resizeTimer) {
        clearTimeout(meta.resizeTimer);
      }
      
      meta.resizeTimer = setTimeout(() => {
        chart.resize({ width: actualWidth, height: actualHeight });
      }, 100);
    } else {
      chart.resize({ width: actualWidth, height: actualHeight });
    }
  }

  /**
   * 导出优化的图表图片
   */
  async exportChart(chart, opts = {}) {
    const defaultOpts = {
      type: 'png',
      pixelRatio: window.devicePixelRatio || 1,
      backgroundColor: '#fff'
    };
    
    const exportOpts = { ...defaultOpts, ...opts };
    
    // 大图表降低像素比
    const meta = this.chartInstances.get(chart);
    if (meta && meta.dataCount > 5000) {
      exportOpts.pixelRatio = Math.min(exportOpts.pixelRatio, 2);
    }
    
    return chart.getDataURL(exportOpts);
  }

  /**
   * 销毁图表并清理资源
   */
  disposeChart(chart) {
    const meta = this.chartInstances.get(chart);
    if (meta) {
      if (meta.resizeTimer) {
        clearTimeout(meta.resizeTimer);
      }
      this.chartInstances.delete(chart);
    }
    
    // 从渲染队列中移除
    this.renderQueue = this.renderQueue.filter(item => item.chart !== chart);
    
    chart.dispose();
  }

  /**
   * 获取性能统计
   */
  getPerformanceStats(chart) {
    const meta = this.chartInstances.get(chart);
    if (!meta) return null;
    
    return {
      dataCount: meta.dataCount,
      isOptimized: meta.dataCount > this.config.sampleThreshold,
      lastUpdate: new Date(meta.lastUpdate).toISOString(),
      renderQueueLength: this.renderQueue.length
    };
  }
}

// 创建单例实例
const chartOptimizer = new ChartPerformanceOptimizer();

// 导出优化器和便捷方法
export default chartOptimizer;

export const {
  createChart,
  optimizeOption,
  sampleData,
  batchUpdate,
  resizeChart,
  exportChart,
  disposeChart,
  getPerformanceStats
} = {
  createChart: chartOptimizer.createChart.bind(chartOptimizer),
  optimizeOption: chartOptimizer.optimizeOption.bind(chartOptimizer),
  sampleData: chartOptimizer.sampleData.bind(chartOptimizer),
  batchUpdate: chartOptimizer.batchUpdate.bind(chartOptimizer),
  resizeChart: chartOptimizer.resizeChart.bind(chartOptimizer),
  exportChart: chartOptimizer.exportChart.bind(chartOptimizer),
  disposeChart: chartOptimizer.disposeChart.bind(chartOptimizer),
  getPerformanceStats: chartOptimizer.getPerformanceStats.bind(chartOptimizer)
};