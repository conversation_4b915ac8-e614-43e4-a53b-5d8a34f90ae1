<template>
  <div class="user-bar">
    <div class="user-info">
      <div class="user-avatar">{{ authStore.userAvatar }}</div>
      <span class="user-name">{{ authStore.userName }}</span>
    </div>
    <button @click="handleLogout" class="logout-btn">
      退出登录
    </button>
  </div>
</template>

<script setup>
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

const handleLogout = () => {
  authStore.logout()
}
</script>

<style scoped>
.user-bar {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 25px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 100;
  transition: all 0.3s ease;
}

.user-bar:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary-blue);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 0.9em;
}

.user-name {
  color: #333;
  font-weight: 500;
  font-size: 0.9em;
}

.logout-btn {
  background: none;
  border: 1px solid #ddd;
  color: #666;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 0.8em;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  background: var(--signal-red);
  color: white;
  border-color: var(--signal-red);
}

@media (max-width: 768px) {
  .user-bar {
    position: relative;
    top: auto;
    right: auto;
    margin-bottom: 20px;
    justify-content: center;
  }
}
</style>
