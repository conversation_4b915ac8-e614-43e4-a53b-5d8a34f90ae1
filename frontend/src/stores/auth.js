import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { api } from '@/utils/api'
import { AUTH_CONFIG, STORAGE_KEYS } from '@/utils/constants'
import { validateUsername, validatePassword, validateInviteCode } from '@/utils/validators'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref(null)
  const token = ref(localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN))
  const isLoading = ref(false)
  const error = ref(null)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)
  const userName = computed(() => user.value?.username || '')
  const userAvatar = computed(() => {
    if (!user.value?.username) return ''
    return user.value.avatar || user.value.username.charAt(0).toUpperCase()
  })

  // 方法
  const login = async (credentials) => {
    isLoading.value = true
    error.value = null

    try {
      // 验证输入
      const usernameValidation = validateUsername(credentials.username)
      if (!usernameValidation.valid) {
        throw new Error(usernameValidation.message)
      }

      const passwordValidation = validatePassword(credentials.password)
      if (!passwordValidation.valid) {
        throw new Error(passwordValidation.message)
      }

      // 发送登录请求
      const response = await api.login(credentials)
      
      if (response.data.success) {
        const { token: authToken, user: userData } = response.data
        
        token.value = authToken
        user.value = userData
        localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, authToken)
        localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(userData))

        console.log('✅ Login successful:', userData.username)
        return { success: true }
      } else {
        throw new Error(response.data.message || '登录失败')
      }
    } catch (err) {
      const errorMessage = err.response?.data?.message || err.message || '登录失败'
      error.value = errorMessage
      console.error('❌ Login failed:', errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      isLoading.value = false
    }
  }

  const register = async (userData) => {
    isLoading.value = true
    error.value = null

    try {
      // 验证输入
      const usernameValidation = validateUsername(userData.username)
      if (!usernameValidation.valid) {
        throw new Error(usernameValidation.message)
      }

      const passwordValidation = validatePassword(userData.password)
      if (!passwordValidation.valid) {
        throw new Error(passwordValidation.message)
      }

      const inviteCodeValidation = validateInviteCode(userData.inviteCode)
      if (!inviteCodeValidation.valid) {
        throw new Error(inviteCodeValidation.message)
      }

      // 发送注册请求
      const response = await api.register(userData)
      
      if (response.data.success) {
        console.log('✅ Registration successful')
        return { success: true, message: response.data.message || '注册成功' }
      } else {
        throw new Error(response.data.message || '注册失败')
      }
    } catch (err) {
      const errorMessage = err.response?.data?.message || err.message || '注册失败'
      error.value = errorMessage
      console.error('❌ Registration failed:', errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      isLoading.value = false
    }
  }

  const logout = () => {
    token.value = null
    user.value = null
    localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN)
    localStorage.removeItem(STORAGE_KEYS.USER_INFO)
    console.log('✅ User logged out')
  }

  const checkAuth = () => {
    const savedToken = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN)
    const savedUser = localStorage.getItem(STORAGE_KEYS.USER_INFO)
    
    if (savedToken && savedUser) {
      try {
        token.value = savedToken
        user.value = JSON.parse(savedUser)
        console.log('✅ Auth restored from localStorage:', user.value.username)
        return true
      } catch (err) {
        console.error('❌ Failed to restore auth from localStorage:', err)
        logout()
        return false
      }
    }
    
    return false
  }

  // 开发模式自动登录
  const autoLoginDemo = () => {
    if (AUTH_CONFIG.DEVELOPMENT_MODE) {
      const demoUser = {
        username: 'demo_user',
        avatar: 'D'
      }
      
      token.value = 'demo_token_' + Date.now()
      user.value = demoUser
      localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token.value)
      localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(demoUser))
      
      console.log('🚀 Development mode: auto login as demo user')
      return true
    }
    return false
  }

  // 清除错误
  const clearError = () => {
    error.value = null
  }

  return {
    // 状态
    user,
    token,
    isLoading,
    error,
    // 计算属性
    isAuthenticated,
    userName,
    userAvatar,
    // 方法
    login,
    register,
    logout,
    checkAuth,
    autoLoginDemo,
    clearError
  }
})
