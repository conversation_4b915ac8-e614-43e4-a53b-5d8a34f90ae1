import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { fetchData } from '@/utils/api'
import { getDefaultDateRange } from '@/utils/constants'

export const useDashboardStore = defineStore('dashboard', () => {
  // 状态
  /**
   * @type {import('vue').Ref<object|null>}
   * @description 缓存从 /api/dashboard/summary 获取的仪表盘摘要数据。
   */
  const summaryData = ref(null)
  const isLoading = ref(false)
  const error = ref(null)
  const lastUpdated = ref(null)
  
  // 日期范围 - 使用动态计算的默认日期范围
  const defaultRange = getDefaultDateRange()
  const dateRange = ref({
    start: defaultRange.START,
    end: defaultRange.END
  })

  // 计算属性
  const totalProducts = computed(() => summaryData.value?.total_products || 0)
  /**
   * @description 直接从 summaryData 获取总销售量。
   * @returns {number} 总销售量，单位是“吨”。
   */
  const totalSales = computed(() => summaryData.value?.total_sales || 0)
  /**
   * @description 直接从 summaryData 获取总产量 (库存)。
   * @returns {number} 总产量，单位是“吨”。
   */
  const totalInventory = computed(() => summaryData.value?.total_inventory || 0)
  /**
   * @description 直接从 summaryData 中获取后端计算好的平均单价。
   */
  const averagePrice = computed(() => summaryData.value?.average_price || 0)
  const productionRatio = computed(() => summaryData.value?.production_ratio || 0)
  /**
   * @description 直接从 summaryData 中获取后端计算好的产销率。
   * @returns {number} 产销率，单位是百分比 (例如, 85.5 代表 85.5%)。
   */
  /**
   * @deprecated 改用 `productionStore.averageRatio`。
   */
  const productionSalesRatio = computed(() => summaryData.value?.production_sales_ratio || 0)

  // 是否有数据
  const hasData = computed(() => !!summaryData.value)
  
  // 数据是否过期（超过5分钟）
  const isDataStale = computed(() => {
    if (!lastUpdated.value) return true
    const now = new Date()
    const updated = new Date(lastUpdated.value)
    return (now - updated) > 5 * 60 * 1000 // 5分钟
  })

  // 方法
  const fetchSummaryData = async (startDate = dateRange.value.start, endDate = dateRange.value.end) => {
    isLoading.value = true
    error.value = null

    try {
      console.log('🔄 Fetching dashboard summary data...')
      const response = await fetchData(`/api/dashboard/summary?start_date=${startDate}&end_date=${endDate}`)
      
      if (response) {
        // 在赋值前处理后端返回的-1，将其转换成null
        const processedData = { ...response }
        if (processedData.production_sales_ratio === -1) {
          processedData.production_sales_ratio = null
        }
        summaryData.value = processedData
        lastUpdated.value = new Date().toISOString()
        
        // 更新日期范围
        dateRange.value = { start: startDate, end: endDate }
        
        console.log('✅ Dashboard summary data loaded:', response)
        return response
      } else {
        throw new Error('No data received')
      }
    } catch (err) {
      error.value = err.message || '获取汇总数据失败'
      console.error('❌ Failed to fetch dashboard summary:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const refreshData = async () => {
    return await fetchSummaryData(dateRange.value.start, dateRange.value.end)
  }

  const updateDateRange = async (startDate, endDate) => {
    dateRange.value = { start: startDate, end: endDate }
    return await fetchSummaryData(startDate, endDate)
  }

  const clearData = () => {
    summaryData.value = null
    error.value = null
    lastUpdated.value = null
  }

  const clearError = () => {
    error.value = null
  }

  // 自动刷新数据（如果数据过期）
  const autoRefreshIfStale = async () => {
    if (isDataStale.value && !isLoading.value) {
      console.log('🔄 Data is stale, auto refreshing...')
      await refreshData()
    }
  }

  return {
    // 状态
    summaryData,
    isLoading,
    error,
    lastUpdated,
    dateRange,
    
    // 计算属性
    totalProducts,
    totalSales,
    totalInventory,
    averagePrice,
    productionRatio,
    productionSalesRatio,
    hasData,
    isDataStale,
    
    // 方法
    fetchSummaryData,
    refreshData,
    updateDateRange,
    clearData,
    clearError,
    autoRefreshIfStale
  }
})
