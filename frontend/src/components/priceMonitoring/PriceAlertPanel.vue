<template>
  <div class="alert-panel">
    <div class="panel-header">
      <h3>系统价格预警</h3>
      <div class="refresh-controls">
        <button @click="refreshAlerts" :disabled="loading" class="refresh-btn">
          <span v-if="!loading">刷新</span>
          <span v-else>刷新中...</span>
        </button>
        <span v-if="lastUpdate" class="last-update">
          更新于: {{ formatTime(lastUpdate) }}
        </span>
      </div>
    </div>
    
    <div class="alert-filters">
      <label>
        <input type="checkbox" v-model="filters.multipleAdjustments" @change="filterAlerts">
        多次调价
      </label>
      <label>
        <input type="checkbox" v-model="filters.significantDrops" @change="filterAlerts">
        大幅降价
      </label>
      <label>
        <input type="checkbox" v-model="filters.consecutiveDrops" @change="filterAlerts">
        连续降价
      </label>
    </div>

    <div v-if="loading && !alerts.length" class="loading-container">
      <div class="loader"></div>
      <p>正在加载预警信息...</p>
    </div>
    
    <div v-if="error" class="error-message">
      <p>{{ error }}</p>
      <button @click="refreshAlerts" class="retry-btn">重试</button>
    </div>
    
    <div v-if="!loading && filteredAlerts.length === 0 && !error" class="no-alerts">
      <p>{{ filters.multipleAdjustments || filters.significantDrops || filters.consecutiveDrops ? '没有符合筛选条件的预警' : '暂无系统预警' }}</p>
    </div>
    
    <div v-else-if="filteredAlerts.length > 0" class="alerts-container">
      <div class="alert-summary">
        共 {{ filteredAlerts.length }} 条预警
        <span v-if="alertStats.multiple_adjustments > 0"> | 多次调价: {{ alertStats.multiple_adjustments }}</span>
        <span v-if="alertStats.significant_drops > 0"> | 大幅降价: {{ alertStats.significant_drops }}</span>
        <span v-if="alertStats.consecutive_drops > 0"> | 连续降价: {{ alertStats.consecutive_drops }}</span>
      </div>
      
      <ul class="alert-list">
        <li v-for="(alert, index) in filteredAlerts" 
            :key="`${alert.type}-${alert.productName}-${index}`" 
            class="alert-item" 
            :class="`alert-${alert.type.toLowerCase()}`">
          <div class="alert-header">
            <strong>{{ alert.productName }}</strong>
            <span class="alert-type">{{ getAlertTypeLabel(alert.type) }}</span>
          </div>
          <div class="alert-body">
            <p class="alert-message">{{ alert.message }}</p>
            <div v-if="alert.details" class="alert-details">
              <span v-if="alert.details.previousPrice">
                原价: {{ formatPrice(alert.details.previousPrice) }}
              </span>
              <span v-if="alert.details.currentPrice">
                现价: {{ formatPrice(alert.details.currentPrice) }}
              </span>
              <span v-if="alert.details.priceRange">
                波动: {{ formatPrice(alert.details.priceRange) }}
              </span>
            </div>
          </div>
          <div class="alert-footer">
            <span class="alert-date">{{ formatDate(alert.date) }}</span>
            <button @click="viewDetails(alert)" class="view-btn">查看详情</button>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, onUnmounted } from 'vue';
import { getSystemAlerts } from '@/utils/priceDataManager';

const emit = defineEmits(['select-product']);

const alerts = ref([]);
const loading = ref(false);
const error = ref(null);
const lastUpdate = ref(null);
const refreshInterval = ref(null);

const filters = ref({
  multipleAdjustments: true,
  significantDrops: true,
  consecutiveDrops: true
});

const alertStats = computed(() => {
  if (!alerts.value || !alerts.value.length) {
    return { multiple_adjustments: 0, significant_drops: 0, consecutive_drops: 0 };
  }
  
  return {
    multiple_adjustments: alerts.value.filter(a => a.type === 'MULTIPLE_DAILY_ADJUSTMENTS').length,
    significant_drops: alerts.value.filter(a => a.type.startsWith('SIGNIFICANT_DROP')).length,
    consecutive_drops: alerts.value.filter(a => a.type === 'CONSECUTIVE_DROP').length
  };
});

const filteredAlerts = computed(() => {
  if (!alerts.value || !alerts.value.length) return [];
  
  return alerts.value.filter(alert => {
    if (alert.type === 'MULTIPLE_DAILY_ADJUSTMENTS' && !filters.value.multipleAdjustments) return false;
    if (alert.type.startsWith('SIGNIFICANT_DROP') && !filters.value.significantDrops) return false;
    if (alert.type === 'CONSECUTIVE_DROP' && !filters.value.consecutiveDrops) return false;
    return true;
  });
});

async function fetchSystemAlerts() {
  loading.value = true;
  error.value = null;
  
  try {
    const response = await getSystemAlerts({
      consecutiveDays: 3,
      percentageDrop: 5,
      absoluteDrop: 200,
      checkMultipleAdjustments: true
    });
    
    if (response.success) {
      alerts.value = response.data || [];
      lastUpdate.value = new Date();
    } else {
      throw new Error(response.message || '获取系统预警失败');
    }
  } catch (err) {
    error.value = err.message || '网络错误';
    console.error('Failed to fetch system alerts:', err);
  } finally {
    loading.value = false;
  }
}

function refreshAlerts() {
  fetchSystemAlerts();
}

function filterAlerts() {
  // 触发视图更新
}

function getAlertTypeLabel(type) {
  const labels = {
    'MULTIPLE_DAILY_ADJUSTMENTS': '多次调价',
    'SIGNIFICANT_DROP_ABSOLUTE': '大幅降价(金额)',
    'SIGNIFICANT_DROP_PERCENTAGE': '大幅降价(百分比)',
    'CONSECUTIVE_DROP': '连续降价'
  };
  return labels[type] || type;
}

function formatPrice(price) {
  return `${price.toFixed(2)} 元/吨`;
}

function formatDate(dateStr) {
  const date = new Date(dateStr);
  return `${date.getMonth() + 1}月${date.getDate()}日`;
}

function formatTime(date) {
  return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
}

function viewDetails(alert) {
  emit('select-product', alert.productName);
}

onMounted(() => {
  fetchSystemAlerts();
  // 每5分钟自动刷新一次
  refreshInterval.value = setInterval(fetchSystemAlerts, 5 * 60 * 1000);
});

onUnmounted(() => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value);
  }
});
</script>

<style scoped>
.alert-panel {
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: #fff;
  border: 1px solid #eee;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.panel-header h3 {
  margin: 0;
  color: #D92E2E;
}

.refresh-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.refresh-btn {
  padding: 0.4rem 1rem;
  background-color: #005BAC;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.refresh-btn:hover:not(:disabled) {
  background-color: #004590;
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.last-update {
  font-size: 0.85rem;
  color: #666;
}

.alert-filters {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.alert-filters label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-size: 0.9rem;
}

.loading-container {
  text-align: center;
  padding: 2rem;
}

.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #005BAC;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  text-align: center;
  color: #D92E2E;
  padding: 1rem;
}

.retry-btn {
  margin-top: 0.5rem;
  padding: 0.4rem 1rem;
  background-color: #D92E2E;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.no-alerts {
  color: #666;
  text-align: center;
  padding: 2rem;
}

.alerts-container {
  margin-top: 1rem;
}

.alert-summary {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.alert-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.alert-item {
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  border-left: 5px solid;
  transition: transform 0.2s, box-shadow 0.2s;
}

.alert-item:hover {
  transform: translateX(2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.alert-multiple_daily_adjustments {
  background-color: #fff3cd;
  border-left-color: #ffc107;
  color: #856404;
}

.alert-significant_drop_absolute,
.alert-significant_drop_percentage {
  background-color: #fbeaea;
  border-left-color: #D92E2E;
  color: #D92E2E;
}

.alert-consecutive_drop {
  background-color: #e8f4ff;
  border-left-color: #005BAC;
  color: #004590;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.alert-type {
  font-size: 0.85rem;
  padding: 0.2rem 0.5rem;
  background-color: rgba(0,0,0,0.1);
  border-radius: 3px;
}

.alert-body {
  margin-bottom: 0.5rem;
}

.alert-message {
  margin: 0 0 0.5rem 0;
}

.alert-details {
  font-size: 0.9rem;
  display: flex;
  gap: 1rem;
  opacity: 0.8;
}

.alert-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.85rem;
}

.alert-date {
  color: #666;
}

.view-btn {
  padding: 0.2rem 0.6rem;
  background-color: transparent;
  border: 1px solid currentColor;
  border-radius: 3px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: background-color 0.2s;
}

.view-btn:hover {
  background-color: rgba(0,0,0,0.05);
}
</style>
