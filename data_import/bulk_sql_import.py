"""
SQL文件导入模块
使用wrangler d1 execute --file方式进行大批量数据导入
这种方式将整个SQL文件发送给Cloudflare D1后端处理，避免Workers的请求超时和1MB API限制
"""
import os
import sys
import subprocess
import time
from datetime import datetime
from .config import USE_REMOTE_D1, D1_DB_NAME, SCRIPT_DIR, get_current_date


def generate_daily_metrics_sql(daily_metrics_data, output_file):
    """生成DailyMetrics表的SQL插入文件"""
    print(f"生成DailyMetrics SQL文件: {output_file}")

    if not daily_metrics_data:
        print("⚠️ 没有DailyMetrics数据，不生成SQL文件。")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("-- No DailyMetrics data to import.\n")
        return

    # 将元组列表转换为DataFrame以便获取日期范围
    min_date = None
    max_date = None
    try:
        import pandas as pd
        daily_metrics_df = pd.DataFrame(daily_metrics_data, columns=[
            'product_id', 'product_name', 'record_date', 'inventory_kg',
            'production_kg', 'sales_kg', 'inventory_value'
        ])
        min_date = daily_metrics_df['record_date'].min()
        max_date = daily_metrics_df['record_date'].max()
    except ImportError:
        print("❌ 'pandas' is not installed. Falling back to slower method.")
        dates = [rec[2] for rec in daily_metrics_data if rec[2]]
        if dates:
            min_date = min(dates)
            max_date = max(dates)
    except Exception as e:
        print(f"❌ 处理DailyMetrics数据时出错: {e}")

    # 检测数据类型
    has_production = False
    has_sales = False
    has_inventory = False
    if 'daily_metrics_df' in locals() and not daily_metrics_df.empty:
        if (daily_metrics_df['production_kg'] > 0).any():
            has_production = True
        if (daily_metrics_df['sales_kg'] > 0).any():
            has_sales = True
        if (daily_metrics_df['inventory_kg'] > 0).any():
            has_inventory = True
    else: # Fallback for no pandas
        for rec in daily_metrics_data:
            if rec[4] > 0: has_production = True
            if rec[5] > 0: has_sales = True
            if rec[3] > 0: has_inventory = True
            if has_production and has_sales and has_inventory: break

    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("-- DailyMetrics数据导入 (精确的Delete-Then-Insert策略)\n")
        f.write("-- 生成时间: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + "\n\n")
        
        # 1. 生成并写入精确的DELETE语句
        f.write("-- 步骤1: 根据数据类型删除时间范围内已存在的旧数据\n")
        delete_statements = []
        if min_date and max_date:
            if has_production:
                delete_statements.append(f"DELETE FROM DailyMetrics WHERE record_date BETWEEN '{min_date}' AND '{max_date}' AND production_volume IS NOT NULL AND production_volume != 0;")
            if has_sales:
                delete_statements.append(f"DELETE FROM DailyMetrics WHERE record_date BETWEEN '{min_date}' AND '{max_date}' AND sales_volume IS NOT NULL AND sales_volume != 0;")
            if has_inventory:
                delete_statements.append(f"DELETE FROM DailyMetrics WHERE record_date BETWEEN '{min_date}' AND '{max_date}' AND inventory_level IS NOT NULL AND inventory_level != 0;")
            
            if delete_statements:
                for stmt in delete_statements:
                    f.write(stmt + '\n')
                f.write('\n')
            else:
                f.write("-- 警告: 数据为空，未生成DELETE语句\n\n")
        else:
            f.write("-- 警告: 无法确定日期范围，未生成DELETE语句\n\n")

        # 2. 生成并写入INSERT语句
        f.write("-- 步骤2: 插入新数据\n")
        for i, record in enumerate(daily_metrics_data):
            product_id, product_name, date, inventory_kg, production_kg, sales_kg, inventory_value = record
            
            production_kg = 0.0 if str(production_kg).lower() == 'nan' else production_kg
            sales_kg = 0.0 if str(sales_kg).lower() == 'nan' else sales_kg
            inventory_kg = 0.0 if str(inventory_kg).lower() == 'nan' else inventory_kg
            inventory_value = 0.0 if str(inventory_value).lower() == 'nan' else inventory_value
            
            average_price = inventory_value / sales_kg if sales_kg > 0 else 0.0
            
            # 使用普通的INSERT语句，因为我们已经删除了旧数据
            sql = f"""INSERT INTO DailyMetrics
                     (product_id, record_date, production_volume, sales_volume, inventory_level, average_price, sales_amount)
                     VALUES ({product_id}, '{date}', {production_kg}, {sales_kg}, {inventory_kg},
                             {average_price}, {inventory_value});\n"""
            f.write(sql)
            
            if (i + 1) % 1000 == 0:
                f.write(f"-- 已处理 {i + 1} 条记录\n")
    
    print(f"✅ 生成了 {len(daily_metrics_data)} 条DailyMetrics记录的SQL文件 (含DELETE语句)")


def generate_price_adjustments_sql(price_adjustments_data, output_file):
    """生成PriceAdjustments表的SQL插入文件"""
    print(f"生成PriceAdjustments SQL文件: {output_file}")

    if not price_adjustments_data:
        print("⚠️ 没有PriceAdjustments数据，不生成SQL文件。")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("-- No PriceAdjustments data to import.\n")
        return

    # 将元组列表转换为DataFrame以便获取日期范围
    min_date = None
    max_date = None
    try:
        import pandas as pd
        price_adjustments_df = pd.DataFrame(price_adjustments_data, columns=[
            'product_id', 'product_name', 'specification', 'adjustment_date',
            'current_price', 'previous_price', 'price_difference', 'adjustment_count'
        ])
        min_date = price_adjustments_df['adjustment_date'].min()
        max_date = price_adjustments_df['adjustment_date'].max()
    except ImportError:
        print("❌ 'pandas' is not installed. Falling back to slower method.")
        dates = [rec[3] for rec in price_adjustments_data if rec[3]]
        if dates:
            min_date = min(dates)
            max_date = max(dates)
    except Exception as e:
        print(f"❌ 处理PriceAdjustments数据时出错: {e}")

    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("-- PriceAdjustments数据导入 (Delete-Then-Insert策略)\n")
        f.write("-- 生成时间: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + "\n\n")
        
        # 1. 生成并写入DELETE语句
        if min_date and max_date:
            delete_stmt = f"DELETE FROM PriceAdjustments WHERE adjustment_date BETWEEN '{min_date}' AND '{max_date}';"
            f.write("-- 步骤1: 删除时间范围内已存在的旧数据\n")
            f.write(delete_stmt + '\n\n')
        else:
            f.write("-- 警告: 无法确定日期范围，未生成DELETE语句\n\n")

        # 2. 生成并写入INSERT语句
        f.write("-- 步骤2: 插入新数据\n")
        if price_adjustments_data:
            for i, record in enumerate(price_adjustments_data):
                product_id, product_name, specification, adjustment_date, current_price, previous_price, price_difference, adjustment_count = record
                
                previous_price_str = 'NULL' if previous_price is None or str(previous_price).lower() == 'nan' else str(previous_price)
                current_price = 0.0 if str(current_price).lower() == 'nan' else current_price
                price_difference = 0.0 if str(price_difference).lower() == 'nan' else price_difference
                
                # 使用普通的INSERT语句
                sql = f"""INSERT INTO PriceAdjustments
                         (product_name, specification, adjustment_date, current_price, previous_price, price_change, adjustment_count)
                         VALUES ('{product_name}', '{specification}', '{adjustment_date}', {current_price}, {previous_price_str}, {price_difference}, {adjustment_count});\n"""
                f.write(sql)
                
                if (i + 1) % 1000 == 0:
                    f.write(f"-- 已处理 {i + 1} 条记录\n")
    
    print(f"✅ 生成了 {len(price_adjustments_data)} 条PriceAdjustments记录的SQL文件 (含DELETE语句)")


def execute_sql_file(sql_file_path, max_retries=3):
    """使用wrangler d1 execute --file执行SQL文件"""
    print(f"\n=== 执行SQL文件导入 ===")
    print(f"文件: {sql_file_path}")
    
    if not os.path.exists(sql_file_path):
        print(f"❌ SQL文件不存在: {sql_file_path}")
        return False
    
    # 显示文件大小
    file_size = os.path.getsize(sql_file_path)
    print(f"文件大小: {file_size / 1024 / 1024:.2f} MB")
    
    for attempt in range(max_retries):
        try:
            print(f"\n尝试 {attempt + 1}/{max_retries}")
            
            # 构建wrangler命令
            if USE_REMOTE_D1:
                cmd = ['npx', 'wrangler', 'd1', 'execute', D1_DB_NAME, '--remote', '--file', sql_file_path]
                db_location = "远程D1数据库（生产环境）"
            else:
                cmd = ['npx', 'wrangler', 'd1', 'execute', D1_DB_NAME, '--local', '--file', sql_file_path]
                db_location = "本地D1数据库（开发环境）"
            
            print(f"执行到: {db_location}")
            print(f"命令: {' '.join(cmd)}")
            
            start_time = time.time()
            
            # 执行命令，设置较长的超时时间（因为是大批量数据）
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True,
                cwd=os.path.join(SCRIPT_DIR, 'backend'),
                timeout=600  # 10分钟超时
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            if result.returncode == 0:
                print(f"✅ SQL文件执行成功！耗时: {duration:.2f}秒")
                if result.stdout:
                    print("输出:", result.stdout[:500])  # 显示前500字符
                return True
            else:
                error_msg = result.stderr[:1000] if result.stderr else "未知错误"
                print(f"❌ SQL文件执行失败 (尝试 {attempt + 1}/{max_retries})")
                print(f"错误信息: {error_msg}")
                
                if attempt < max_retries - 1:
                    # 指数退避重试
                    wait_time = (2 ** attempt) * 5  # 5秒, 10秒, 20秒
                    print(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                    continue
                
        except subprocess.TimeoutExpired:
            print(f"❌ SQL文件执行超时 (尝试 {attempt + 1}/{max_retries})")
            if attempt < max_retries - 1:
                wait_time = (2 ** attempt) * 10
                print(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
                continue
        except Exception as e:
            print(f"❌ 执行SQL文件时发生异常: {e}")
            if attempt < max_retries - 1:
                wait_time = (2 ** attempt) * 5
                print(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
                continue
    
    print(f"❌ SQL文件执行失败，已尝试 {max_retries} 次")
    return False


def import_data_via_sql_files(daily_metrics_data, price_adjustments_data):
    """通过SQL文件方式导入数据（推荐方式）"""
    print("\n" + "="*60)
    print("使用SQL文件方式进行数据导入")
    print("="*60)
    
    success_results = []
    
    # 创建临时SQL文件目录
    sql_dir = os.path.join(SCRIPT_DIR, 'temp_sql')
    os.makedirs(sql_dir, exist_ok=True)
    
    try:
        # 1. 导入DailyMetrics数据
        if daily_metrics_data:
            print(f"\n--- 导入DailyMetrics数据 ({len(daily_metrics_data)} 条记录) ---")
            daily_metrics_sql = os.path.join(sql_dir, f'daily_metrics_{get_current_date()}.sql')
            
            generate_daily_metrics_sql(daily_metrics_data, daily_metrics_sql)
            success = execute_sql_file(daily_metrics_sql)
            success_results.append(('DailyMetrics', success))
            
            if success:
                print("✅ DailyMetrics数据导入成功")
            else:
                print("❌ DailyMetrics数据导入失败")
        else:
            print("⚠️ 没有DailyMetrics数据需要导入")
            success_results.append(('DailyMetrics', True))
        
        # 2. 导入PriceAdjustments数据
        if price_adjustments_data:
            print(f"\n--- 导入PriceAdjustments数据 ({len(price_adjustments_data)} 条记录) ---")
            price_adjustments_sql = os.path.join(sql_dir, f'price_adjustments_{get_current_date()}.sql')
            
            generate_price_adjustments_sql(price_adjustments_data, price_adjustments_sql)
            success = execute_sql_file(price_adjustments_sql)
            success_results.append(('PriceAdjustments', success))
            
            if success:
                print("✅ PriceAdjustments数据导入成功")
            else:
                print("❌ PriceAdjustments数据导入失败")
        else:
            print("⚠️ 没有PriceAdjustments数据需要导入")
            success_results.append(('PriceAdjustments', True))
        
        # 汇总结果
        print("\n" + "="*50)
        print("SQL文件导入结果汇总")
        print("="*50)
        
        all_success = True
        for table_name, success in success_results:
            status = "✅ 成功" if success else "❌ 失败"
            print(f"{table_name}: {status}")
            if not success:
                all_success = False
        
        if all_success:
            print("\n🎉 所有数据导入成功！")
        else:
            print("\n⚠️ 部分数据导入失败，请检查上面的错误信息")
        
        return all_success
        
    finally:
        # 清理临时SQL文件（可选）
        # 如果要保留文件用于调试，可以注释掉下面的代码
        import shutil
        if os.path.exists(sql_dir):
            try:
                shutil.rmtree(sql_dir)
                print(f"\n🧹 已清理临时SQL文件目录: {sql_dir}")
            except Exception as e:
                print(f"⚠️ 清理临时文件失败: {e}")


def verify_import_results():
    """验证导入结果"""
    print("\n=== 验证导入结果 ===")
    
    try:
        # 查询DailyMetrics记录数
        if USE_REMOTE_D1:
            cmd = ['npx', 'wrangler', 'd1', 'execute', D1_DB_NAME, '--remote', 
                   '--command', 'SELECT COUNT(*) as count FROM DailyMetrics']
        else:
            cmd = ['npx', 'wrangler', 'd1', 'execute', D1_DB_NAME, '--local',
                   '--command', 'SELECT COUNT(*) as count FROM DailyMetrics']
        
        result = subprocess.run(cmd, capture_output=True, text=True,
                              cwd=os.path.join(SCRIPT_DIR, 'backend'), timeout=30)
        
        if result.returncode == 0 and result.stdout:
            print("DailyMetrics表记录数:", result.stdout)
        
        # 查询PriceAdjustments记录数
        if USE_REMOTE_D1:
            cmd = ['npx', 'wrangler', 'd1', 'execute', D1_DB_NAME, '--remote',
                   '--command', 'SELECT COUNT(*) as count FROM PriceAdjustments']
        else:
            cmd = ['npx', 'wrangler', 'd1', 'execute', D1_DB_NAME, '--local',
                   '--command', 'SELECT COUNT(*) as count FROM PriceAdjustments']
        
        result = subprocess.run(cmd, capture_output=True, text=True,
                              cwd=os.path.join(SCRIPT_DIR, 'backend'), timeout=30)
        
        if result.returncode == 0 and result.stdout:
            print("PriceAdjustments表记录数:", result.stdout)
            
        return True
        
    except Exception as e:
        print(f"验证过程中发生错误: {e}")
        return False