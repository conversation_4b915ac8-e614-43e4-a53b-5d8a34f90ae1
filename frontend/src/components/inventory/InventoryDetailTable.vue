<template>
  <BaseCard title="库存明细数据" :loading="loading" :error="error">
    <template #extra>
      <div class="table-controls">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索产品名称..."
          class="search-input"
        />
        
        
        <select v-model="sortBy" @change="sortData" class="sort-selector">
          <option value="inventory_level">按库存量排序</option>
          <option value="product_name">按产品名称排序</option>
        </select>
        
        <BaseButton
          @click="exportData"
          variant="secondary"
          size="small"
          icon="📊"
        >
          导出
        </BaseButton>
      </div>
    </template>

    <div class="table-container">
      <table class="inventory-table">
        <thead>
          <tr>
            <th @click="setSortBy('product_name')" class="sortable">
              产品名称
              <span v-if="sortBy === 'product_name'" class="sort-icon">{{ sortOrder === 'asc' ? '↑' : '↓' }}</span>
            </th>
            <th @click="setSortBy('inventory_level')" class="sortable">
              库存量(T)
              <span v-if="sortBy === 'inventory_level'" class="sort-icon">{{ sortOrder === 'asc' ? '↑' : '↓' }}</span>
            </th>
            <th @click="setSortBy('percentage')" class="sortable">
              占比(%)
              <span v-if="sortBy === 'percentage'" class="sort-icon">{{ sortOrder === 'asc' ? '↑' : '↓' }}</span>
            </th>
            <th>库存状态</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in paginatedData" :key="item.product_name" class="table-row">
            <td class="product-name">{{ item.product_name }}</td>
            <td class="number">{{ formatInventory(item.inventory_level) }}</td>
            <td class="number">{{ formatPercentage(item.percentage) }}</td>
            <td>
              <span class="status-badge" :class="getStatusClass(item.inventory_level)">
                {{ getStatusText(item.inventory_level) }}
              </span>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- 空状态 -->
      <div v-if="filteredData.length === 0" class="empty-state">
        <div class="empty-icon">📦</div>
        <p>暂无库存数据</p>
      </div>
    </div>

    <!-- 分页控制 -->
    <div v-if="totalPages > 1" class="pagination">
      <BaseButton
        @click="currentPage = 1"
        :disabled="currentPage === 1"
        variant="ghost"
        size="small"
      >
        首页
      </BaseButton>
      
      <BaseButton
        @click="currentPage--"
        :disabled="currentPage === 1"
        variant="ghost"
        size="small"
      >
        上一页
      </BaseButton>
      
      <span class="page-info">
        第 {{ currentPage }} 页，共 {{ totalPages }} 页
      </span>
      
      <BaseButton
        @click="currentPage++"
        :disabled="currentPage === totalPages"
        variant="ghost"
        size="small"
      >
        下一页
      </BaseButton>
      
      <BaseButton
        @click="currentPage = totalPages"
        :disabled="currentPage === totalPages"
        variant="ghost"
        size="small"
      >
        末页
      </BaseButton>
    </div>

    <!-- 数据统计 -->
    <div class="data-summary">
      <span>共 {{ filteredData.length }} 条记录</span>
      <span>总库存量: {{ formatInventory(totalInventory) }}</span>
      <span>平均库存: {{ formatInventory(averageInventory) }}</span>
    </div>
  </BaseCard>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useInventoryStore } from '@/stores/inventory'
import { formatInventory, formatPercentage } from '@/utils/formatters'
import BaseCard from '@/components/common/BaseCard.vue'
import BaseButton from '@/components/common/BaseButton.vue'

const props = defineProps({
  date: {
    type: String,
    default: '2025-07-26'
  }
})

const emit = defineEmits(['export-data'])

const inventoryStore = useInventoryStore()
const loading = ref(false)
const error = ref('')
const searchQuery = ref('')
const sortBy = ref('inventory_level')
const sortOrder = ref('desc')
const currentPage = ref(1)
const pageSize = ref(20)

// 模拟库存详细数据
const inventoryDetailData = ref([])


const filteredData = computed(() => {
  let data = inventoryDetailData.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    data = data.filter(item => 
      item.product_name.toLowerCase().includes(query)
    )
  }


  // 排序
  data = [...data].sort((a, b) => {
    let aVal = a[sortBy.value]
    let bVal = b[sortBy.value]

    // 处理数字类型
    if (typeof aVal === 'string' && !isNaN(parseFloat(aVal))) {
      aVal = parseFloat(aVal)
      bVal = parseFloat(bVal)
    }

    if (sortOrder.value === 'asc') {
      return aVal > bVal ? 1 : -1
    } else {
      return aVal < bVal ? 1 : -1
    }
  })

  return data
})

const totalPages = computed(() => {
  return Math.ceil(filteredData.value.length / pageSize.value)
})

const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredData.value.slice(start, end)
})

const totalInventory = computed(() => {
  return filteredData.value.reduce((sum, item) => sum + (item.inventory_level || 0), 0)
})

const averageInventory = computed(() => {
  if (filteredData.value.length === 0) return 0
  return totalInventory.value / filteredData.value.length
})

// 方法
const fetchData = async () => {
  loading.value = true
  error.value = ''
  
  try {
    // 使用store中的TOP15数据生成详细数据
    await inventoryStore.fetchInventoryTop15(props.date, 50) // 获取更多数据
    
    // 生成模拟的详细数据
    inventoryDetailData.value = inventoryStore.top15Data.map((item, index) => ({
      product_name: item.name,
      inventory_level: item.value,
      percentage: item.percentage || 0
    }))
    
    console.log('✅ Inventory detail data loaded:', inventoryDetailData.value.length, 'records')
  } catch (err) {
    error.value = '数据加载失败'
    console.error('Failed to load inventory detail data:', err)
  } finally {
    loading.value = false
  }
}


const getStatusClass = (inventoryLevel) => {
  // 智能库存状态判定：基于数据分布的动态阈值
  const allLevels = inventoryDetailData.value.map(item => item.inventory_level).sort((a, b) => b - a)
  if (allLevels.length === 0) return 'status-low'
  
  const p75 = allLevels[Math.floor(allLevels.length * 0.25)] || 0  // 前25%
  const p50 = allLevels[Math.floor(allLevels.length * 0.5)] || 0   // 前50%
  
  if (inventoryLevel >= p75) return 'status-high'   // 前25%为充足
  if (inventoryLevel >= p50) return 'status-medium' // 前50%为正常
  return 'status-low'                               // 后50%为偏低
}

const getStatusText = (inventoryLevel) => {
  // 智能库存状态判定：基于数据分布的动态阈值
  const allLevels = inventoryDetailData.value.map(item => item.inventory_level).sort((a, b) => b - a)
  if (allLevels.length === 0) return '偏低'
  
  const p75 = allLevels[Math.floor(allLevels.length * 0.25)] || 0  // 前25%
  const p50 = allLevels[Math.floor(allLevels.length * 0.5)] || 0   // 前50%
  
  if (inventoryLevel >= p75) return '充足'   // 前25%为充足
  if (inventoryLevel >= p50) return '正常'   // 前50%为正常
  return '偏低'                             // 后50%为偏低
}

const setSortBy = (field) => {
  if (sortBy.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortBy.value = field
    sortOrder.value = 'desc'
  }
  currentPage.value = 1
}

const sortData = () => {
  currentPage.value = 1
}

const filterData = () => {
  currentPage.value = 1
}

const exportData = () => {
  emit('export-data', filteredData.value)
  
  // 简单的CSV导出实现
  const headers = ['产品名称', '库存量(T)', '占比(%)', '库存状态']
  const csvContent = [
    headers.join(','),
    ...filteredData.value.map(item => [
      `"${item.product_name}"`,
      item.inventory_level,
      item.percentage.toFixed(2),
      `"${getStatusText(item.inventory_level)}"`
    ].join(','))
  ].join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `库存明细_${props.date}.csv`
  link.click()
}

// 监听搜索查询变化
watch(searchQuery, () => {
  currentPage.value = 1
})

// 监听日期变化
watch(() => props.date, () => {
  fetchData()
})

onMounted(() => {
  fetchData()
})

// 暴露方法
defineExpose({
  refresh: fetchData,
  exportData
})
</script>

<style scoped>
.table-controls {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.search-input {
  padding: 6px 12px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.9em;
  min-width: 200px;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.category-selector,
.sort-selector {
  padding: 6px 12px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.9em;
  background: white;
  cursor: pointer;
}

.category-selector:focus,
.sort-selector:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.table-container {
  overflow-x: auto;
  margin: 20px 0;
}

.inventory-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9em;
}

.inventory-table th,
.inventory-table td {
  padding: 12px 8px;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
}

.inventory-table th {
  background: var(--background-light);
  font-weight: 600;
  color: var(--text-primary);
  position: sticky;
  top: 0;
  z-index: 1;
}

.inventory-table th.sortable {
  cursor: pointer;
  user-select: none;
  position: relative;
}

.inventory-table th.sortable:hover {
  background: #e8f4fd;
}

.sort-icon {
  margin-left: 4px;
  color: var(--primary-blue);
}

.table-row:hover {
  background: #f8f9fa;
}

.product-name {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.number {
  text-align: right;
  font-family: 'Courier New', monospace;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8em;
  font-weight: 500;
}

.status-high {
  background: #e8f5e8;
  color: #2d7d2d;
}

.status-medium {
  background: #fff3cd;
  color: #856404;
}

.status-low {
  background: #f8d7da;
  color: #721c24;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 3em;
  margin-bottom: 16px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  margin: 20px 0;
}

.page-info {
  color: var(--text-secondary);
  font-size: 0.9em;
}

.data-summary {
  display: flex;
  justify-content: space-around;
  padding: 16px;
  background: var(--background-light);
  border-radius: 8px;
  font-size: 0.9em;
  color: var(--text-secondary);
}

@media (max-width: 768px) {
  .table-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .search-input {
    min-width: auto;
  }
  
  .data-summary {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
  
  .pagination {
    flex-wrap: wrap;
    gap: 8px;
  }
}
</style>
