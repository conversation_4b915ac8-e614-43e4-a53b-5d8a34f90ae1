<template>
  <div class="alert-config-section">
    <h3>预警配置</h3>
    <div class="config-form">
      <div class="config-item">
        <label>单日降幅阈值 (%)</label>
        <input 
          type="number" 
          v-model.number="config.daily_drop_threshold" 
          min="1" 
          max="50"
          step="0.5"
        />
      </div>
      <div class="config-item">
        <label>金额降幅阈值 (元/吨)</label>
        <input 
          type="number" 
          v-model.number="config.amount_drop_threshold" 
          min="50" 
          max="1000"
          step="50"
        />
      </div>
      <div class="config-item">
        <label>连续下跌天数</label>
        <input 
          type="number" 
          v-model.number="config.consecutive_days" 
          min="2" 
          max="7"
        />
      </div>
      <div class="config-item">
        <label>波动率阈值 (%)</label>
        <input 
          type="number" 
          v-model.number="config.volatility_threshold" 
          min="5" 
          max="50"
          step="1"
        />
      </div>
      <div class="config-actions">
        <button @click="saveConfig" class="btn btn-primary">
          保存配置
        </button>
        <button @click="resetConfig" class="btn btn-secondary">
          重置默认
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const defaultConfig = {
  daily_drop_threshold: 5,
  amount_drop_threshold: 200,
  consecutive_days: 3,
  volatility_threshold: 15
}

const config = ref({ ...defaultConfig })

const saveConfig = () => {
  console.log('保存配置:', config.value)
  alert('预警配置已保存！')
}

const resetConfig = () => {
  config.value = { ...defaultConfig }
  console.log('重置为默认配置')
}
</script>

<style scoped>
.alert-config-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
}

.alert-config-section h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.config-form {
  display: grid;
  gap: 16px;
}

.config-item {
  display: grid;
  grid-template-columns: 200px 1fr;
  align-items: center;
  gap: 12px;
}

.config-item label {
  font-weight: 500;
  color: #666;
}

.config-item input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  max-width: 150px;
}

.config-item input:focus {
  outline: none;
  border-color: #005BAC;
}

.config-actions {
  margin-top: 20px;
  display: flex;
  gap: 12px;
}

.btn {
  padding: 8px 20px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #005BAC;
  color: white;
}

.btn-primary:hover {
  background: #004494;
}

.btn-secondary {
  background: #f5f5f5;
  color: #666;
}

.btn-secondary:hover {
  background: #e0e0e0;
}
</style>