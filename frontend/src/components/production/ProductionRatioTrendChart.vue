<template>
  <BaseCard title="产销率趋势分析" :loading="loading" :error="error">
    <template #extra>
      <div class="chart-controls">
        <BaseButton
          @click="toggleBaseline"
          variant="ghost"
          size="small"
        >
          {{ showBaseline ? '隐藏基准线' : '显示基准线' }}
        </BaseButton>
      </div>
    </template>
    
    <!-- 数据一致性警告 -->
    <div v-if="consistencyWarning" class="consistency-warning">
      <div class="warning-icon">⚠️</div>
      <div class="warning-content">
        <div class="warning-title">{{ consistencyWarning.message }}</div>
        <div class="warning-details">{{ consistencyWarning.details }}</div>
        <div class="warning-actions">
          <BaseButton @click="dismissWarning" variant="ghost" size="small">忽略</BaseButton>
          <BaseButton @click="refreshData" variant="primary" size="small">刷新数据</BaseButton>
        </div>
      </div>
    </div>

    <EChartsWrapper
      :options="chartOptions"
      :height="height"
      :loading="loading"
      @chart-ready="onChartReady"
      @chart-click="onChartClick"
    />

    <div class="ratio-summary">
      <div class="summary-item">
        <span class="label">平均产销率：</span>
        <span class="value" :class="getRatioColorClass(averageRatio)">
          {{ formatPercentage(averageRatio) }}
        </span>
      </div>
      <div class="summary-item">
        <span class="label">最高产销率：</span>
        <span class="value">{{ formatPercentage(maxRatio) }}</span>
      </div>
      <div class="summary-item">
        <span class="label">最低产销率：</span>
        <span class="value">{{ formatPercentage(minRatio) }}</span>
      </div>
      <div class="summary-item">
        <span class="label">趋势方向：</span>
        <span class="value" :class="trendColorClass">
          {{ trendIcon }} {{ trendText }}
        </span>
      </div>
    </div>
  </BaseCard>
</template>

<script setup>
import { ref, computed, onMounted, watch, getCurrentInstance } from 'vue'
import { useProductionStore } from '@/stores/production'
import { formatPercentage } from '@/utils/formatters'
import { getBaseChartOption, getTooltipConfig, getXAxisConfig, getYAxisConfig, getLineSeriesConfig, getGridConfig } from '@/utils/charts'
import { CHART_COLORS } from '@/utils/constants'
import BaseCard from '@/components/common/BaseCard.vue'
import BaseButton from '@/components/common/BaseButton.vue'
import EChartsWrapper from '@/components/charts/EChartsWrapper.vue'

const props = defineProps({
  startDate: {
    type: String
  },
  endDate: {
    type: String
  },
  height: {
    type: String,
    default: '400px'
  }
})

const emit = defineEmits(['chart-ready', 'chart-click', 'data-updated', 'date-range-changed'])

const productionStore = useProductionStore()
const loading = ref(false)
const error = ref('')
const showBaseline = ref(true)
const chartInstance = ref(null)
const consistencyWarning = computed(() => productionStore.consistencyWarning)

// 防止重复数据获取的标记
const lastFetchKey = ref('')

// 计算属性
const chartData = computed(() => {
  return productionStore.ratioData.map(item => ({
    date: item.date,
    ratio: item.ratio
  }))
})

const averageRatio = computed(() => {
  // Use authoritative average from store instead of local calculation
  return productionStore.averageRatio
})

const maxRatio = computed(() => {
  if (chartData.value.length === 0) return 0
  return Math.max(...chartData.value.map(item => item.ratio))
})

const minRatio = computed(() => {
  if (chartData.value.length === 0) return 0
  return Math.min(...chartData.value.map(item => item.ratio))
})

const trendDirection = computed(() => {
  if (chartData.value.length < 2) return 'stable'
  
  const first = chartData.value[0]?.ratio || 0
  const last = chartData.value[chartData.value.length - 1]?.ratio || 0
  
  if (last > first * 1.05) return 'increasing'
  if (last < first * 0.95) return 'decreasing'
  return 'stable'
})

const trendIcon = computed(() => {
  const icons = {
    increasing: '📈',
    decreasing: '📉',
    stable: '➡️'
  }
  return icons[trendDirection.value]
})

const trendText = computed(() => {
  const texts = {
    increasing: '上升趋势',
    decreasing: '下降趋势',
    stable: '平稳趋势'
  }
  return texts[trendDirection.value]
})

const trendColorClass = computed(() => {
  const classes = {
    increasing: 'trend-positive',
    decreasing: 'trend-negative',
    stable: 'trend-neutral'
  }
  return classes[trendDirection.value]
})

// 图表配置
const chartOptions = computed(() => {
  if (chartData.value.length === 0) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#999',
          fontSize: 14
        }
      }
    }
  }

  const dates = chartData.value.map(item => item.date)
  const ratios = chartData.value.map(item => item.ratio)

  const option = {
    ...getBaseChartOption(),
    title: {
      text: '产销率趋势分析',
      left: 'center',
      textStyle: {
        color: CHART_COLORS.PRIMARY,
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: getTooltipConfig((params) => {
      const point = params[0]
      return `${point.axisValue}<br/>产销率: ${formatPercentage(point.value)}`
    }),
    xAxis: getXAxisConfig(dates, { rotate: 45 }),
    yAxis: getYAxisConfig({
      name: '产销率(%)',
      formatter: (value) => value.toFixed(1) + '%',
      min: Math.max(0, Math.min(...ratios) - 10),
      max: Math.max(150, Math.max(...ratios) + 10)
    }),
    series: [
      {
        name: '产销率',
        type: 'line',
        data: ratios,
        smooth: true,
        lineStyle: {
          color: CHART_COLORS.PRIMARY,
          width: 3
        },
        itemStyle: {
          color: function(params) {
            // 根据产销率值动态设置颜色：绿色>100%，红色<100%
            const value = params.value
            if (value >= 100) {
              return '#34c759' // 绿色：消费型
            } else {
              return '#D92E2E' // 红色：积压型
            }
          },
          borderColor: '#ffffff',
          borderWidth: 2
        },
        symbol: 'circle',
        symbolSize: 8,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(0, 91, 172, 0.3)' },
              { offset: 1, color: 'rgba(0, 91, 172, 0.1)' }
            ]
          }
        }
      }
    ],
    grid: getGridConfig()
  }

  // 添加100%基准线
  if (showBaseline.value) {
    option.series[0].markLine = {
      silent: true,
      data: [{
        yAxis: 100,
        lineStyle: {
          color: '#D92E2E',
          type: 'dashed',
          width: 3
        },
        label: {
          formatter: '100%基准线（产销平衡点）',
          position: 'end',
          color: '#D92E2E',
          fontWeight: 'bold',
          fontSize: 12
        }
      }]
    }

    // 添加预警区域
    option.series[0].markArea = {
      data: [
        [
          // 积压区域：0-100%，红色背景
          { yAxis: 0, itemStyle: { color: 'rgba(217, 46, 46, 0.1)' } },
          { yAxis: 100, itemStyle: { color: 'rgba(217, 46, 46, 0.1)' } }
        ],
        [
          // 消费区域：100%以上，绿色背景
          { yAxis: 100, itemStyle: { color: 'rgba(52, 199, 89, 0.1)' } },
          { yAxis: 500, itemStyle: { color: 'rgba(52, 199, 89, 0.1)' } }
        ]
      ]
    }
  }

  return option
})

// 方法
const fetchData = async () => {
  // 检查日期参数是否有效
  if (!props.startDate || !props.endDate) {
    console.log('⏳ [ProductionRatioTrendChart] Waiting for valid date range...', { start: props.startDate, end: props.endDate })
    return
  }
  
  // 防止重复获取相同的数据
  const fetchKey = `${props.startDate}-${props.endDate}`
  if (loading.value || lastFetchKey.value === fetchKey) {
    console.log('⏭️ Skipping duplicate fetch for:', fetchKey)
    return
  }
  
  loading.value = true
  error.value = ''
  lastFetchKey.value = fetchKey
  
  try {
    console.log('📊 Fetching production ratio data for:', fetchKey)
    await productionStore.fetchProductionRatioData(props.startDate, props.endDate)
    emit('data-updated', productionStore.ratioData)
    
    // 检查数据一致性
    productionStore.checkDataConsistency()
    console.log('✅ Production ratio data loaded successfully')
  } catch (err) {
    error.value = '数据加载失败'
    console.error('❌ Failed to load production ratio data:', err)
    // 重置fetch key以允许重试
    lastFetchKey.value = ''
  } finally {
    loading.value = false
  }
}

// 忽略一致性警告
const dismissWarning = () => {
  if (productionStore.consistencyWarning) {
    // 记录用户忽略了警告
    console.log('用户忽略了数据一致性警告:', productionStore.consistencyWarning)
    productionStore.consistencyWarning = null
  }
}

// 刷新数据
const refreshData = async () => {
  await productionStore.fetchAllProductionData(props.startDate, props.endDate)
  emit('data-updated', productionStore.ratioData)
}

const toggleBaseline = () => {
  showBaseline.value = !showBaseline.value
  console.log('Baseline toggled:', showBaseline.value)
}

const getRatioColorClass = (ratio) => {
  // 按照用户要求：绿色>100%表示消费，红色<100%表示积压
  if (ratio >= 100) return 'trend-positive' // 绿色：消费型，销量>=产量
  if (ratio < 100) return 'trend-negative'  // 红色：积压型，销量<产量
  return 'trend-neutral'
}

const onChartReady = (chart) => {
  chartInstance.value = chart
  emit('chart-ready', chart)
}

const onChartClick = (params) => {
  emit('chart-click', params)
}

// 监听props变化
watch([() => props.startDate, () => props.endDate], (newDates, oldDates) => {
  // 只有当日期真正发生变化时才重新获取数据
  if (newDates[0] && newDates[1] && 
      (newDates[0] !== oldDates?.[0] || newDates[1] !== oldDates?.[1])) {
    console.log('📅 Date range changed from', oldDates, 'to', newDates)
    fetchData()
  } else {
    console.log('⏭️ Skipping fetch - dates unchanged or invalid:', newDates)
  }
}, { 
  // 不在首次挂载时立即执行，避免与onMounted重复
  immediate: false 
})

onMounted(() => {
  // 只有当props提供了有效日期时才获取数据
  if (props.startDate && props.endDate) {
    console.log('🚀 Initial data fetch with dates:', props.startDate, 'to', props.endDate)
    fetchData()
  } else {
    console.log('⚠️ No valid dates provided to component on mount')
  }
})

// 暴露方法
defineExpose({
  refresh: fetchData,
  getChart: () => chartInstance.value
})
</script>

<style scoped>
.chart-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.time-selector {
  padding: 6px 12px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.9em;
  background: white;
  cursor: pointer;
}

.time-selector:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.ratio-summary {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  padding: 16px;
  background: var(--background-light);
  border-radius: 8px;
  border-top: 1px solid #f0f0f0;
}

.summary-item {
  text-align: center;
  flex: 1;
}

.label {
  display: block;
  color: var(--text-secondary);
  font-size: 0.85em;
  margin-bottom: 4px;
}

.value {
  display: block;
  color: var(--primary-blue);
  font-weight: 600;
  font-size: 1em;
}

.trend-positive {
  color: var(--success-green);
}

.trend-negative {
  color: var(--signal-red);
}

.trend-neutral {
  color: var(--neutral-gray);
}

/* 数据一致性警告样式 */
.consistency-warning {
  display: flex;
  margin: 12px 0;
  padding: 12px;
  background-color: rgba(255, 204, 0, 0.1);
  border: 1px solid rgba(255, 204, 0, 0.5);
  border-radius: 8px;
  align-items: flex-start;
}

.warning-icon {
  font-size: 24px;
  margin-right: 12px;
  flex-shrink: 0;
}

.warning-content {
  flex: 1;
}

.warning-title {
  font-weight: 600;
  color: #d97706;
  margin-bottom: 4px;
}

.warning-details {
  font-size: 0.9em;
  color: #78350f;
  margin-bottom: 8px;
}

.warning-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .chart-controls {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .ratio-summary {
    flex-direction: column;
    gap: 12px;
  }
  
  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .label, .value {
    display: inline;
    margin: 0;
  }
  
  .consistency-warning {
    flex-direction: column;
  }
  
  .warning-icon {
    margin-bottom: 8px;
  }
  
  .warning-actions {
    flex-direction: column;
    width: 100%;
  }
}
</style>
