/**
 * 数据验证工具函数
 */

// 验证邮箱格式
export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// 验证用户名格式
export const validateUsername = (username) => {
  if (!username || username.length < 3) {
    return { valid: false, message: '用户名至少需要3个字符' }
  }
  if (username.length > 20) {
    return { valid: false, message: '用户名不能超过20个字符' }
  }
  if (!/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/.test(username)) {
    return { valid: false, message: '用户名只能包含字母、数字、下划线和中文' }
  }
  return { valid: true }
}

// 验证密码强度
export const validatePassword = (password) => {
  if (!password || password.length < 6) {
    return { valid: false, message: '密码至少需要6个字符' }
  }
  if (password.length > 50) {
    return { valid: false, message: '密码不能超过50个字符' }
  }
  return { valid: true }
}

// 验证邀请码
export const validateInviteCode = (code) => {
  const validCodes = ['SPRING2025', 'ADMIN2024']
  if (!code) {
    return { valid: false, message: '请输入邀请码' }
  }
  if (!validCodes.includes(code.toUpperCase())) {
    return { valid: false, message: '邀请码无效' }
  }
  return { valid: true }
}

// 验证日期格式
export const validateDate = (dateString) => {
  if (!dateString) {
    return { valid: false, message: '日期不能为空' }
  }
  
  const date = new Date(dateString)
  if (isNaN(date.getTime())) {
    return { valid: false, message: '日期格式不正确' }
  }
  
  // 检查日期范围（2020-2030）
  const year = date.getFullYear()
  if (year < 2020 || year > 2030) {
    return { valid: false, message: '日期超出有效范围' }
  }
  
  return { valid: true }
}

// 验证日期范围
export const validateDateRange = (startDate, endDate) => {
  const startValidation = validateDate(startDate)
  if (!startValidation.valid) {
    return { valid: false, message: '开始日期' + startValidation.message }
  }
  
  const endValidation = validateDate(endDate)
  if (!endValidation.valid) {
    return { valid: false, message: '结束日期' + endValidation.message }
  }
  
  const start = new Date(startDate)
  const end = new Date(endDate)
  
  if (start > end) {
    return { valid: false, message: '开始日期不能晚于结束日期' }
  }
  
  // 检查日期范围不能超过1年
  const diffTime = Math.abs(end - start)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  if (diffDays > 365) {
    return { valid: false, message: '日期范围不能超过1年' }
  }
  
  return { valid: true }
}

// 验证数字
export const validateNumber = (value, min = null, max = null) => {
  if (value === null || value === undefined || value === '') {
    return { valid: false, message: '数值不能为空' }
  }
  
  const num = parseFloat(value)
  if (isNaN(num)) {
    return { valid: false, message: '请输入有效的数字' }
  }
  
  if (min !== null && num < min) {
    return { valid: false, message: `数值不能小于${min}` }
  }
  
  if (max !== null && num > max) {
    return { valid: false, message: `数值不能大于${max}` }
  }
  
  return { valid: true }
}

// 验证文件类型
export const validateFileType = (file, allowedTypes = []) => {
  if (!file) {
    return { valid: false, message: '请选择文件' }
  }
  
  if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
    return { valid: false, message: `文件类型不支持，支持的类型：${allowedTypes.join(', ')}` }
  }
  
  return { valid: true }
}

// 验证文件大小
export const validateFileSize = (file, maxSizeMB = 10) => {
  if (!file) {
    return { valid: false, message: '请选择文件' }
  }
  
  const maxSizeBytes = maxSizeMB * 1024 * 1024
  if (file.size > maxSizeBytes) {
    return { valid: false, message: `文件大小不能超过${maxSizeMB}MB` }
  }
  
  return { valid: true }
}

// 验证Excel文件
export const validateExcelFile = (file) => {
  const typeValidation = validateFileType(file, [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel'
  ])
  
  if (!typeValidation.valid) {
    return { valid: false, message: '请选择Excel文件（.xlsx或.xls）' }
  }
  
  const sizeValidation = validateFileSize(file, 50) // 50MB限制
  if (!sizeValidation.valid) {
    return sizeValidation
  }
  
  return { valid: true }
}

// 验证表单数据
export const validateForm = (data, rules) => {
  const errors = {}
  let isValid = true
  
  for (const field in rules) {
    const rule = rules[field]
    const value = data[field]
    
    // 必填验证
    if (rule.required && (!value || value.toString().trim() === '')) {
      errors[field] = rule.message || `${field}不能为空`
      isValid = false
      continue
    }
    
    // 如果不是必填且值为空，跳过其他验证
    if (!rule.required && (!value || value.toString().trim() === '')) {
      continue
    }
    
    // 自定义验证函数
    if (rule.validator && typeof rule.validator === 'function') {
      const result = rule.validator(value)
      if (!result.valid) {
        errors[field] = result.message
        isValid = false
      }
    }
  }
  
  return { valid: isValid, errors }
}
