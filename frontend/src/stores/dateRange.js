import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { fetchData } from '@/utils/api'

export const useDateRangeStore = defineStore('dateRange', () => {
  // 状态
  const availableDateRange = ref({
    start: null,
    end: null
  })
  const isLoading = ref(false)
  const error = ref(null)
  const lastUpdated = ref(null)

  // 计算属性 - 提供默认值以防数据未加载
  const defaultStartDate = computed(() => {
    return availableDateRange.value.start || '2025-06-01'
  })

  const defaultEndDate = computed(() => {
    return availableDateRange.value.end || '2025-07-29'
  })

  const dateRange = computed(() => ({
    start: defaultStartDate.value,
    end: defaultEndDate.value
  }))

  // 检查日期范围是否已加载
  const isDateRangeLoaded = computed(() => {
    return availableDateRange.value.start && availableDateRange.value.end
  })

  // 方法
  const fetchAvailableDateRange = async () => {
    if (isLoading.value) return // 防止重复请求

    isLoading.value = true
    error.value = null

    try {
      console.log('🔄 Fetching available date range from database...')
      
      // 调用后端API获取数据库中的实际日期范围
      const response = await fetchData('/api/system/date-range')
      
      if (response && response.start_date && response.end_date) {
        availableDateRange.value = {
          start: response.start_date,
          end: response.end_date
        }
        lastUpdated.value = new Date().toISOString()
        
        console.log('✅ Available date range loaded:', availableDateRange.value)
      } else {
        throw new Error('Invalid date range response from server')
      }
    } catch (err) {
      console.error('❌ Failed to fetch available date range:', err)
      error.value = err.message || '获取日期范围失败'
      
      // 使用备用默认值
      availableDateRange.value = {
        start: '2025-06-01',
        end: '2025-06-26'
      }
      console.log('⚠️ Using fallback date range:', availableDateRange.value)
    } finally {
      isLoading.value = false
    }
  }

  // 刷新日期范围
  const refreshDateRange = async () => {
    return await fetchAvailableDateRange()
  }

  // 清除数据
  const clearData = () => {
    availableDateRange.value = {
      start: null,
      end: null
    }
    error.value = null
    lastUpdated.value = null
  }

  // 获取格式化的日期范围
  const getFormattedDateRange = (format = 'YYYY-MM-DD') => {
    // 这里可以根据需要添加日期格式化逻辑
    return {
      start: defaultStartDate.value,
      end: defaultEndDate.value
    }
  }

  return {
    // 状态
    availableDateRange,
    isLoading,
    error,
    lastUpdated,
    
    // 计算属性
    defaultStartDate,
    defaultEndDate,
    dateRange,
    isDateRangeLoaded,
    
    // 方法
    fetchAvailableDateRange,
    refreshDateRange,
    clearData,
    getFormattedDateRange
  }
})