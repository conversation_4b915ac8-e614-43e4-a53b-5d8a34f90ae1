# 价格分析报告生成系统

## 项目简介
这是一个用于生成价格分析报告的自动化系统。该系统能够处理多种数据源，进行价格分析，并生成可视化的HTML报告。

## 数据来源
系统处理以下数据源：

1. 调价表数据
   - 来源：Excel文件
   - 格式：包含多个sheet，每个sheet代表一个调价日期
   - 内容：产品分类、品名、规格、调幅、前价格、价格等信息

2. 收发存汇总表数据
   - 来源：Excel文件
   - 格式：包含物料名称、入库、出库、结存等信息
   - 处理：排除鲜品和副产品数据

3. 销售发票执行数据
   - 来源：Excel文件
   - 格式：包含发票日期、物料名称、数量、金额等信息
   - 处理：按日期和物料分组统计

4. 产成品入库数据
   - 来源：Excel文件
   - 格式：包含入库日期、物料名称、主数量等信息
   - 处理：按日期和物料分组统计

5. 价格对比数据
   - 来源：Excel文件（春雪与小明农牧价格对比）
   - 格式：包含品名、规格、春雪价格、小明中间价、中间价差等信息

6. 行业价格数据
   - 来源：多个Excel文件
   - 产品：鸡苗、毛鸡、板冻大胸、琵琶腿
   - 格式：包含日期和价格信息

## 数据处理流程

1. 数据加载与清洗
   - 使用pandas读取Excel文件
   - 删除空行和无效数据
   - 转换日期格式
   - 确保数值列为数值类型
   - 排除特定类型的数据（如鲜品、副产品）

2. 数据预处理
   - 调价表：合并多个sheet的数据，提取日期信息
   - 库存数据：重命名列，转换数据类型
   - 销售数据：按日期和物料分组统计
   - 产量数据：计算每日总产量和分产品产量
   - 价格对比：计算价差
   - 行业价格：计算价格变动

3. 数据分析
   - 价格变动分析：检测异常价格变动
   - 产销比分析：计算每日产销比
   - 销售趋势分析：统计销售量和金额
   - 数据一致性检查：验证数据完整性

4. 数据可视化
   - 生成库存趋势图
   - 生成销售趋势图
   - 生成产销比图表
   - 生成价格对比图表

## 主要功能
- 数据加载与处理
  - 调价表数据
  - 收发存汇总表数据
  - 销售发票执行数据
  - 产成品入库数据
  - 价格对比数据
  - 行业价格数据

- 数据分析
  - 价格变动分析
  - 产销比分析
  - 销售趋势分析
  - 异常价格检测
  - 数据一致性检查

- 报告生成
  - 首页概览
  - 库存报告
  - 产销比报告
  - 销售报告
  - 详细数据报告
  - 价格波动报告
  - 行业价格报告

## 系统要求
- Python 3.x
- 依赖包：
  - pandas
  - datetime
  - os

## 项目结构
```
project/
├── main.py              # 主程序入口
├── data_loader.py       # 数据加载模块
├── analyzer.py          # 数据分析模块
├── visualizer.py        # 数据可视化模块
├── index_report.py      # 首页报告生成
├── inventory_report.py  # 库存报告生成
├── ratio_report.py      # 产销比报告生成
├── sales_report.py      # 销售报告生成
├── details_report.py    # 详细数据报告生成
├── comparison_report.py # 价格波动报告生成
├── industry_report.py   # 行业价格报告生成
└── output_html_report/  # 生成的HTML报告输出目录
```

## 使用说明
1. 确保所有依赖包已正确安装
2. 准备所需的数据文件：
   - 调价表
   - 收发存汇总表
   - 销售发票数据
   - 产成品入库数据
   - 价格对比数据
   - 行业价格数据

3. 运行主程序：
   ```bash
   python main.py
   ```

4. 查看生成的报告：
   - 所有HTML报告将生成在 `output_html_report` 目录下
   - 打开 `index.html` 查看报告首页

## 报告说明
1. 首页概览
   - 显示关键指标汇总
   - 异常价格变动概览
   - 产销比趋势

2. 库存报告
   - 当前库存状态
   - 库存变动趋势

3. 产销比报告
   - 产销比趋势分析
   - 产品级别产销比详情

4. 销售报告
   - 销售趋势分析
   - 综合售价分析

5. 详细数据报告
   - 产品销售详情
   - 销售数据明细

6. 价格波动报告
   - 价格对比分析
   - 价格调整记录

7. 行业价格报告
   - 行业价格趋势
   - 市场行情分析

## 注意事项
- 确保数据文件格式正确
- 定期备份重要数据
- 检查生成的报告完整性
- 数据文件命名需符合规范
- 确保数据文件路径配置正确

## 维护与支持
如有问题或需要支持，请联系系统管理员。

## 版本信息
- 当前版本：1.0.0
- 最后更新：2024年 