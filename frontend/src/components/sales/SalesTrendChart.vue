<template>
  <BaseCard title="销售趋势分析" :loading="loading" :error="error">
    <template #extra>
      <div class="chart-controls">
        <select v-model="selectedMetric" @change="updateChart" class="metric-selector">
          <option value="volume">销量趋势</option>
          <option value="amount">销售额趋势</option>
          <option value="price">价格趋势</option>
        </select>
      </div>
    </template>

    <EChartsWrapper
      :options="chartOptions"
      height="400px"
      :loading="loading"
      @chart-ready="onChartReady"
      @chart-click="onChartClick"
    />

    <div class="trend-summary">
      <div class="summary-item">
        <span class="label">趋势方向：</span>
        <span class="value" :class="trendColorClass">
          {{ trendIcon }} {{ trendText }}
        </span>
      </div>
      <div class="summary-item">
        <span class="label">变化幅度：</span>
        <span class="value">{{ changeAmplitude }}</span>
      </div>
      <div class="summary-item">
        <span class="label">数据点数：</span>
        <span class="value">{{ dataPoints }}个</span>
      </div>
    </div>
  </BaseCard>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useSalesStore } from '@/stores/sales'
import { formatSalesVolume, formatCurrency, formatPrice, formatChangeRate, getTrendIcon, getTrendColorClass, formatAmount } from '@/utils/formatters'
import { getBaseChartOption, getTooltipConfig, getXAxisConfig, getYAxisConfig, getLineSeriesConfig, getGridConfig } from '@/utils/charts'
import { CHART_COLORS } from '@/utils/constants'
import BaseCard from '@/components/common/BaseCard.vue'
import EChartsWrapper from '@/components/charts/EChartsWrapper.vue'

const props = defineProps({
  startDate: {
    type: String,
    default: '2025-06-01' // 备用默认值，实际值由父组件传入
  },
  endDate: {
    type: String,
    default: '2025-07-29' // 备用默认值，实际值由父组件传入
  }
})

const emit = defineEmits(['chart-ready', 'chart-click', 'data-updated', 'date-range-changed'])

const salesStore = useSalesStore()
const loading = ref(false)
const error = ref('')
const selectedMetric = ref('volume')
const chartInstance = ref(null)

// 计算属性
const chartData = computed(() => {
  return salesStore.salesPriceData.map(item => ({
    date: item.date,
    volume: item.volume,
    amount: item.amount / 10000, // 转换为万元
    price: item.price
  }))
})

const currentData = computed(() => {
  const metric = selectedMetric.value
  return chartData.value.map(item => ({
    x: item.date,
    y: item[metric]
  }))
})

const dataPoints = computed(() => chartData.value.length)

const trendDirection = computed(() => {
  if (currentData.value.length < 2) return 'stable'
  
  const first = currentData.value[0]?.y || 0
  const last = currentData.value[currentData.value.length - 1]?.y || 0
  
  if (last > first * 1.05) return 'increasing'
  if (last < first * 0.95) return 'decreasing'
  return 'stable'
})

const trendIcon = computed(() => {
  const icons = {
    increasing: '📈',
    decreasing: '📉',
    stable: '➡️'
  }
  return icons[trendDirection.value]
})

const trendText = computed(() => {
  const texts = {
    increasing: '上升趋势',
    decreasing: '下降趋势',
    stable: '平稳趋势'
  }
  return texts[trendDirection.value]
})

const trendColorClass = computed(() => {
  const classes = {
    increasing: 'trend-positive',
    decreasing: 'trend-negative',
    stable: 'trend-neutral'
  }
  return classes[trendDirection.value]
})

const changeAmplitude = computed(() => {
  if (currentData.value.length < 2) return '--'
  
  const values = currentData.value.map(item => item.y)
  const max = Math.max(...values)
  const min = Math.min(...values)
  
  if (min === 0) return '--'
  
  const amplitude = ((max - min) / min) * 100
  return amplitude.toFixed(1) + '%'
})

// 图表配置
const chartOptions = computed(() => {
  if (chartData.value.length === 0) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#999',
          fontSize: 14
        }
      }
    }
  }

  const metric = selectedMetric.value
  const metricConfig = {
    volume: {
      name: '销量',
      unit: 'T',
      color: CHART_COLORS.PRIMARY,
      formatter: (value) => formatSalesVolume(value)
    },
    amount: {
      name: '销售额',
      unit: '元',
      color: CHART_COLORS.SECONDARY,
      formatter: (value) => formatAmount(value, { showCurrency: false, showUnit: true })
    },
    price: {
      name: '平均单价',
      unit: '元/T',
      color: CHART_COLORS.ACCENT,
      formatter: (value) => formatPrice(value)
    }
  }

  const config = metricConfig[metric]
  const yData = chartData.value.map(item => item[metric])

  return {
    ...getBaseChartOption(),
    title: {
      text: `${config.name}趋势分析`,
      left: 'center',
      textStyle: {
        color: CHART_COLORS.PRIMARY,
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: getTooltipConfig((params) => {
      const point = params[0]
      return `${point.axisValue}<br/>${config.name}: ${config.formatter(point.value)}`
    }),
    xAxis: getXAxisConfig(chartData.value.map(item => item.date), { rotate: 45 }),
    yAxis: getYAxisConfig({
      name: `${config.name}(${config.unit})`,
      formatter: (value) => config.formatter(value)
    }),
    series: [
      getLineSeriesConfig(config.name, yData, {
        color: config.color,
        smooth: true,
        area: true
      })
    ],
    grid: getGridConfig()
  }
})

// 方法
const calculateDateRange = (period) => {
  const endDate = new Date()
  const startDate = new Date()
  
  switch (period) {
    case '7':
      startDate.setDate(endDate.getDate() - 7)
      break
    case '30':
      startDate.setDate(endDate.getDate() - 30)
      break
    case '90':
      startDate.setDate(endDate.getDate() - 90)
      break
    default:
      startDate.setDate(endDate.getDate() - 30)
  }
  
  // 格式化日期为 YYYY-MM-DD
  const formatDate = (date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }
  
  return {
    start: formatDate(startDate),
    end: formatDate(endDate)
  }
}

const fetchData = async () => {
  loading.value = true
  error.value = ''
  
  try {
    // 使用父组件传入的日期范围
    await salesStore.fetchSalesPriceData(props.startDate, props.endDate)
    emit('data-updated', salesStore.salesPriceData)
  } catch (err) {
    error.value = '数据加载失败'
    console.error('Failed to load sales trend data:', err)
  } finally {
    loading.value = false
  }
}

const updateChart = () => {
  // 图表会通过计算属性自动更新
  console.log('Chart updated for metric:', selectedMetric.value)
}

const onChartReady = (chart) => {
  chartInstance.value = chart
  emit('chart-ready', chart)
}

const onChartClick = (params) => {
  emit('chart-click', params)
}

// 监听props变化
watch([() => props.startDate, () => props.endDate], () => {
  if (props.startDate && props.endDate) {
    fetchData()
  }
})

onMounted(() => {
  if (props.startDate && props.endDate) {
    fetchData()
  }
})

onMounted(() => {
  // 初始加载时使用props日期
  fetchDataWithProps()
})

// 暴露方法
defineExpose({
  refresh: fetchData,
  getChart: () => chartInstance.value
})
</script>

<style scoped>
.chart-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.metric-selector,
.period-selector {
  padding: 6px 12px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.9em;
  background: white;
  cursor: pointer;
}

.metric-selector:focus,
.period-selector:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.trend-summary {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  padding: 16px;
  background: var(--background-light);
  border-radius: 8px;
  border-top: 1px solid #f0f0f0;
}

.summary-item {
  text-align: center;
  flex: 1;
}

.label {
  display: block;
  color: var(--text-secondary);
  font-size: 0.85em;
  margin-bottom: 4px;
}

.value {
  display: block;
  color: var(--primary-blue);
  font-weight: 600;
  font-size: 1em;
}

.trend-positive {
  color: var(--success-green);
}

.trend-negative {
  color: var(--signal-red);
}

.trend-neutral {
  color: var(--neutral-gray);
}

@media (max-width: 768px) {
  .chart-controls {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .trend-summary {
    flex-direction: column;
    gap: 12px;
  }
  
  .summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .label, .value {
    display: inline;
    margin: 0;
  }
}
</style>
