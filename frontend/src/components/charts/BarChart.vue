<template>
  <EChartsWrapper
    :options="chartOptions"
    :width="width"
    :height="height"
    :loading="loading"
    @chart-ready="onChartReady"
    @chart-click="onChartClick"
  />
</template>

<script setup>
import { computed } from 'vue'
import EChartsWrapper from './EChartsWrapper.vue'
import { getBaseChartOption, getTooltipConfig, getLegendConfig, getXAxisConfig, getYAxisConfig, getBarSeriesConfig, getGridConfig } from '@/utils/charts'
import { CHART_COLORS } from '@/utils/constants'

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  xField: {
    type: String,
    default: 'x'
  },
  yField: {
    type: String,
    default: 'y'
  },
  seriesField: {
    type: String,
    default: null
  },
  title: {
    type: String,
    default: ''
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '400px'
  },
  loading: {
    type: <PERSON>olean,
    default: false
  },
  horizontal: {
    type: Boolean,
    default: false
  },
  showLabel: {
    type: Boolean,
    default: false
  },
  colors: {
    type: Array,
    default: () => [CHART_COLORS.PRIMARY, CHART_COLORS.SECONDARY, CHART_COLORS.ACCENT]
  },
  yAxisFormatter: {
    type: Function,
    default: (value) => value
  },
  labelFormatter: {
    type: Function,
    default: (params) => params.value
  },
  tooltipFormatter: {
    type: Function,
    default: null
  }
})

const emit = defineEmits(['chart-ready', 'chart-click'])

// 处理数据
const processedData = computed(() => {
  if (!props.data || props.data.length === 0) return { xData: [], series: [] }

  const xData = [...new Set(props.data.map(item => item[props.xField]))]
  
  if (props.seriesField) {
    // 多系列数据
    const seriesMap = new Map()
    
    props.data.forEach(item => {
      const seriesName = item[props.seriesField]
      if (!seriesMap.has(seriesName)) {
        seriesMap.set(seriesName, [])
      }
      seriesMap.get(seriesName).push({
        x: item[props.xField],
        y: item[props.yField]
      })
    })
    
    const series = Array.from(seriesMap.entries()).map(([name, data], index) => {
      const sortedData = xData.map(x => {
        const found = data.find(d => d.x === x)
        return found ? found.y : 0
      })
      
      return getBarSeriesConfig(name, sortedData, {
        color: props.colors[index % props.colors.length],
        showLabel: props.showLabel,
        labelFormatter: props.labelFormatter
      })
    })
    
    return { xData, series }
  } else {
    // 单系列数据
    const yData = xData.map(x => {
      const found = props.data.find(item => item[props.xField] === x)
      return found ? found[props.yField] : 0
    })
    
    const series = [getBarSeriesConfig('数据', yData, {
      color: props.colors[0],
      showLabel: props.showLabel,
      labelFormatter: props.labelFormatter
    })]
    
    return { xData, series }
  }
})

// 图表配置
const chartOptions = computed(() => {
  const { xData, series } = processedData.value
  
  const baseConfig = {
    ...getBaseChartOption(),
    title: props.title ? {
      text: props.title,
      left: 'center',
      textStyle: {
        color: CHART_COLORS.PRIMARY,
        fontSize: 16,
        fontWeight: 'bold'
      }
    } : undefined,
    tooltip: getTooltipConfig(props.tooltipFormatter),
    legend: series.length > 1 ? getLegendConfig(series.map(s => s.name)) : undefined,
    series,
    grid: getGridConfig()
  }
  
  if (props.horizontal) {
    // 水平柱状图
    baseConfig.xAxis = getYAxisConfig({
      formatter: props.yAxisFormatter
    })
    baseConfig.yAxis = {
      type: 'category',
      data: xData,
      axisLabel: {
        color: '#666666',
        fontSize: 11
      },
      axisLine: {
        lineStyle: {
          color: '#E0E0E0'
        }
      },
      axisTick: {
        show: false
      }
    }
  } else {
    // 垂直柱状图
    baseConfig.xAxis = getXAxisConfig(xData, { rotate: xData.length > 8 ? 45 : 0 })
    baseConfig.yAxis = getYAxisConfig({
      formatter: props.yAxisFormatter
    })
  }
  
  return baseConfig
})

const onChartReady = (chart) => {
  emit('chart-ready', chart)
}

const onChartClick = (params) => {
  emit('chart-click', params)
}
</script>
