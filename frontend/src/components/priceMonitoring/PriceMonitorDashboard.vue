<template>
  <div class="price-monitor-dashboard">
    <!-- 页面标题和工具栏 -->
    <div class="dashboard-header">
      <h2 class="page-title">
        <i class="fas fa-chart-line"></i>
        产品价格波动监控
      </h2>
      <div class="toolbar">
        <ProductPriceSelector
          v-model:selected-products="selectedProducts"
          v-model:date-range="dateRange"
          @filter-change="handleFilterChange"
        />
        <button class="btn btn-primary" @click="refreshData" :disabled="loading">
          <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
          刷新数据
        </button>
      </div>
    </div>

    <!-- 统计摘要卡片 -->
    <div class="stats-grid">
      <PriceStatsSummary
        :stats="priceStats"
        :loading="loading"
      />
    </div>

    <!-- 预警面板 -->
    <div class="alert-section" v-if="unacknowledgedAlerts.length > 0">
      <PriceAlertPanel
        :alerts="alerts"
        @acknowledge-alert="handleAcknowledgeAlert"
        @acknowledge-all="handleAcknowledgeAllAlerts"
        @refresh="handleRefreshAlerts"
        @view-details="handleViewAlertDetails"
      />
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 价格趋势图表 -->
      <div class="chart-section">
        <div class="section-header">
          <h3>价格趋势分析</h3>
          <div class="chart-controls">
            <select v-model="chartTimeRange" @change="updateChart">
              <option value="7d">近7天</option>
              <option value="30d">近30天</option>
              <option value="90d">近90天</option>
              <option value="custom">自定义</option>
            </select>
          </div>
        </div>
        <PriceTrendChart
          :data="trendData"
          :loading="chartLoading"
          :time-range="chartTimeRange"
          @point-click="handleChartPointClick"
        />
      </div>

      <!-- 价格变化排行榜 -->
      <div class="ranking-section">
        <div class="section-header">
          <h3>价格变化排行榜</h3>
          <div class="ranking-controls">
            <select v-model="rankingType" @change="updateRankings">
              <option value="drop_amount">按降幅金额</option>
              <option value="drop_percentage">按降幅百分比</option>
            </select>
          </div>
        </div>
        <PriceRankingTable
          :rankings="rankings"
          :ranking-type="rankingType"
          :loading="rankingLoading"
          @product-click="handleProductClick"
        />
      </div>
    </div>

    <!-- 预警配置对话框 -->
    <PriceAlertConfig
      v-model:visible="showAlertConfig"
      :config="alertConfig"
      @save-config="handleSaveAlertConfig"
    />

    <!-- 预警详情对话框 -->
    <BaseModal v-model:visible="showAlertDetails" title="预警详情">
      <div v-if="selectedAlert" class="alert-details">
        <div class="detail-item">
          <label>产品名称:</label>
          <span>{{ selectedAlert.product_name }}</span>
        </div>
        <div class="detail-item">
          <label>预警类型:</label>
          <span>{{ getAlertTypeText(selectedAlert.alert_type) }}</span>
        </div>
        <div class="detail-item">
          <label>预警级别:</label>
          <span :class="`level-${selectedAlert.alert_level.toLowerCase()}`">
            {{ selectedAlert.alert_level }}
          </span>
        </div>
        <div class="detail-item">
          <label>当前价格:</label>
          <span>{{ formatCurrency(selectedAlert.current_price) }}</span>
        </div>
        <div class="detail-item">
          <label>价格变化:</label>
          <span :class="{ 'negative': selectedAlert.price_change < 0 }">
            {{ selectedAlert.price_change > 0 ? '+' : '' }}{{ formatCurrency(selectedAlert.price_change) }}
            ({{ selectedAlert.change_percentage > 0 ? '+' : '' }}{{ selectedAlert.change_percentage }}%)
          </span>
        </div>
        <div class="detail-item">
          <label>预警消息:</label>
          <span>{{ selectedAlert.alert_message }}</span>
        </div>
        <div class="detail-item">
          <label>预警时间:</label>
          <span>{{ formatDateTime(selectedAlert.created_at) }}</span>
        </div>
      </div>
    </BaseModal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch, nextTick } from 'vue'
import { usePricingStore } from '@/stores/pricing'
import { formatCurrency, formatDateTime } from '@/utils/formatters'

// 组件引入
import PriceStatsSummary from './PriceStatsSummary.vue'
import PriceAlertPanel from './PriceAlertPanel.vue'
import PriceTrendChart from './PriceTrendChart.vue'
import PriceRankingTable from './PriceRankingTable.vue'
import ProductPriceSelector from './ProductPriceSelector.vue'
import PriceAlertConfig from './PriceAlertConfig.vue'
import BaseModal from '@/components/common/BaseModal.vue'

// 状态管理
const pricingStore = usePricingStore()

// 响应式数据
const loading = ref(false)
const chartLoading = ref(false)
const rankingLoading = ref(false)
const showAlertConfig = ref(false)
const showAlertDetails = ref(false)
const selectedAlert = ref(null)

const selectedProducts = ref([])
const dateRange = reactive({
  start: '',
  end: ''
})

const chartTimeRange = ref('30d')
const rankingType = ref('drop_amount')

// 计算属性
const priceStats = computed(() => pricingStore.priceStats)
const alerts = computed(() => pricingStore.alerts)
const trendData = computed(() => pricingStore.trendData)
const rankings = computed(() => pricingStore.rankings)
const alertConfig = computed(() => pricingStore.alertConfig)
const unacknowledgedAlerts = computed(() => pricingStore.unacknowledgedAlerts)

// 方法
const refreshData = async () => {
  loading.value = true
  try {
    await pricingStore.refreshMonitoringData()
  } catch (error) {
    console.error('刷新数据失败:', error)
  } finally {
    loading.value = false
  }
}

const handleFilterChange = (filters) => {
  // 处理筛选条件变化
  selectedProducts.value = filters.products || []
  dateRange.start = filters.dateRange?.start || ''
  dateRange.end = filters.dateRange?.end || ''
  
  // 重新获取数据
  updateChart()
}

const handleAcknowledgeAlert = async (alertId) => {
  try {
    await pricingStore.acknowledgeAlert(alertId)
  } catch (error) {
    console.error('确认预警失败:', error)
  }
}

const handleAcknowledgeAllAlerts = async () => {
  try {
    await pricingStore.acknowledgeAllAlerts()
  } catch (error) {
    console.error('确认所有预警失败:', error)
  }
}

const handleRefreshAlerts = async () => {
  try {
    await pricingStore.fetchAlerts()
  } catch (error) {
    console.error('刷新预警失败:', error)
  }
}

const handleViewAlertDetails = (alert) => {
  selectedAlert.value = alert
  showAlertDetails.value = true
}

const updateChart = async () => {
  chartLoading.value = true
  try {
    const productId = selectedProducts.value.length > 0 ? selectedProducts.value[0] : null
    await pricingStore.fetchTrendData(chartTimeRange.value, productId)
  } catch (error) {
    console.error('更新图表失败:', error)
  } finally {
    chartLoading.value = false
  }
}

const updateRankings = async () => {
  rankingLoading.value = true
  try {
    await pricingStore.fetchRankings('daily', rankingType.value)
  } catch (error) {
    console.error('更新排行榜失败:', error)
  } finally {
    rankingLoading.value = false
  }
}

const handleChartPointClick = (point) => {
  console.log('图表点击:', point)
  // 可以在这里处理图表点击事件，比如显示详细信息
}

const handleProductClick = (product) => {
  console.log('产品点击:', product)
  // 选择该产品并更新图表
  selectedProducts.value = [product.product_id]
  updateChart()
}

const handleSaveAlertConfig = async (config) => {
  try {
    await pricingStore.saveAlertConfig(config)
    showAlertConfig.value = false
  } catch (error) {
    console.error('保存预警配置失败:', error)
  }
}

const getAlertTypeText = (type) => {
  const texts = {
    'DAILY_DROP': '单日大跌',
    'CONSECUTIVE_DROP': '连续下跌',
    'VOLATILITY': '波动异常',
    'HISTORICAL_LOW': '历史新低'
  }
  return texts[type] || '未知类型'
}

// 生命周期
onMounted(async () => {
  // 使用 nextTick 确保 DOM 已经完全渲染
  await nextTick()
  refreshData()
})

// 监听器
watch(rankingType, () => {
  updateRankings()
})
</script>

<style scoped>
.price-monitor-dashboard {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 0;
  border-bottom: 2px solid #005BAC;
}

.page-title {
  color: #005BAC;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar {
  display: flex;
  gap: 12px;
  align-items: center;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #005BAC;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #004494;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.stats-grid {
  margin-bottom: 24px;
}

.alert-section {
  margin-bottom: 24px;
}

.main-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

.chart-section,
.ranking-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e9ecef;
}

.section-header h3 {
  color: #005BAC;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.chart-controls,
.ranking-controls {
  display: flex;
  gap: 8px;
}

select {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background: white;
}

.alert-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-item label {
  font-weight: 600;
  color: #666;
  min-width: 100px;
}

.detail-item span {
  text-align: right;
  flex: 1;
}

.level-critical {
  color: #D92E2E;
  font-weight: 600;
}

.level-warning {
  color: #ff9500;
  font-weight: 600;
}

.level-info {
  color: #49A9E8;
  font-weight: 600;
}

.negative {
  color: #D92E2E;
}

@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .toolbar {
    justify-content: center;
  }

  .price-monitor-dashboard {
    padding: 12px;
  }
}
</style>