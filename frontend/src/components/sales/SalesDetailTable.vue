<template>
  <BaseCard title="销售明细数据" :loading="loading" :error="error">
    <template #extra>
      <div class="table-controls">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索产品名称..."
          class="search-input"
        />
        
        <select v-model="sortBy" @change="sortData" class="sort-selector">
          <option value="date">按日期排序</option>
          <option value="volume">按销量排序</option>
          <option value="amount">按销售额排序</option>
          <option value="price">按单价排序</option>
        </select>
        
        <BaseButton
          @click="exportData"
          variant="secondary"
          size="small"
          icon="📊"
        >
          导出
        </BaseButton>
      </div>
    </template>

    <div class="table-container">
      <table class="sales-table">
        <thead>
          <tr>
            <th @click="setSortBy('date')" class="sortable">
              日期
              <span v-if="sortBy === 'date'" class="sort-icon">{{ sortOrder === 'asc' ? '↑' : '↓' }}</span>
            </th>
            <th @click="setSortBy('product_name')" class="sortable">
              产品名称
              <span v-if="sortBy === 'product_name'" class="sort-icon">{{ sortOrder === 'asc' ? '↑' : '↓' }}</span>
            </th>
            <th @click="setSortBy('volume')" class="sortable">
              销量(T)
              <span v-if="sortBy === 'volume'" class="sort-icon">{{ sortOrder === 'asc' ? '↑' : '↓' }}</span>
            </th>
            <th @click="setSortBy('amount')" class="sortable">
              销售额(万元)
              <span v-if="sortBy === 'amount'" class="sort-icon">{{ sortOrder === 'asc' ? '↑' : '↓' }}</span>
            </th>
            <th @click="setSortBy('price')" class="sortable">
              单价(元/T)
              <span v-if="sortBy === 'price'" class="sort-icon">{{ sortOrder === 'asc' ? '↑' : '↓' }}</span>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in paginatedData" :key="`${item.date}-${item.product_name}`" class="table-row">
            <td>{{ formatDate(item.date) }}</td>
            <td class="product-name">{{ item.product_name }}</td>
            <td class="number">{{ formatSalesVolume(item.volume) }}</td>
            <td class="number">{{ formatSalesAmount(item.amount) }}</td>
            <td class="number">{{ formatPrice(item.price) }}</td>
          </tr>
        </tbody>
      </table>

      <!-- 空状态 -->
      <div v-if="filteredData.length === 0" class="empty-state">
        <div class="empty-icon">📋</div>
        <p>暂无销售数据</p>
      </div>
    </div>

    <!-- 分页控制 -->
    <div v-if="totalPages > 1" class="pagination">
      <BaseButton
        @click="currentPage = 1"
        :disabled="currentPage === 1"
        variant="ghost"
        size="small"
      >
        首页
      </BaseButton>
      
      <BaseButton
        @click="currentPage--"
        :disabled="currentPage === 1"
        variant="ghost"
        size="small"
      >
        上一页
      </BaseButton>
      
      <span class="page-info">
        第 {{ currentPage }} 页，共 {{ totalPages }} 页
      </span>
      
      <BaseButton
        @click="currentPage++"
        :disabled="currentPage === totalPages"
        variant="ghost"
        size="small"
      >
        下一页
      </BaseButton>
      
      <BaseButton
        @click="currentPage = totalPages"
        :disabled="currentPage === totalPages"
        variant="ghost"
        size="small"
      >
        末页
      </BaseButton>
    </div>

    <!-- 数据统计 -->
    <div class="data-summary">
      <span>共 {{ filteredData.length }} 条记录</span>
      <span>总销量: {{ formatSalesVolume(totalVolume) }}</span>
      <span>总销售额: {{ formatSalesAmount(totalAmount) }}</span>
    </div>
  </BaseCard>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useSalesStore } from '@/stores/sales'
import { formatDate, formatSalesVolume, formatPrice, formatAmount } from '@/utils/formatters'
import BaseCard from '@/components/common/BaseCard.vue'
import BaseButton from '@/components/common/BaseButton.vue'

const props = defineProps({
  startDate: {
    type: String,
    default: '2025-07-01'
  },
  endDate: {
    type: String,
    default: '2025-07-31'
  }
})

const emit = defineEmits(['export-data'])

const salesStore = useSalesStore()
const loading = ref(false)
const error = ref('')
const searchQuery = ref('')
const sortBy = ref('date')
const sortOrder = ref('desc')
const currentPage = ref(1)
const pageSize = ref(20)

// 计算属性
const filteredData = computed(() => {
  let data = salesStore.salesData

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    data = data.filter(item => 
      item.product_name.toLowerCase().includes(query)
    )
  }

  // 排序
  data = [...data].sort((a, b) => {
    let aVal = a[sortBy.value]
    let bVal = b[sortBy.value]

    // 特殊处理日期字段
    if (sortBy.value === 'date') {
      const dateA = new Date(aVal)
      const dateB = new Date(bVal)
      
      if (sortOrder.value === 'asc') {
        return dateA - dateB
      } else {
        return dateB - dateA
      }
    }

    // 处理数字类型
    if (typeof aVal === 'string' && !isNaN(parseFloat(aVal))) {
      aVal = parseFloat(aVal)
      bVal = parseFloat(bVal)
    }

    if (sortOrder.value === 'asc') {
      return aVal > bVal ? 1 : -1
    } else {
      return aVal < bVal ? 1 : -1
    }
  })

  return data
})

const totalPages = computed(() => {
  return Math.ceil(filteredData.value.length / pageSize.value)
})

const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredData.value.slice(start, end)
})

const totalVolume = computed(() => {
  return filteredData.value.reduce((sum, item) => sum + (item.volume || 0), 0)
})

const totalAmount = computed(() => {
  return filteredData.value.reduce((sum, item) => sum + (item.amount || 0), 0)
})

// 格式化函数
const formatSalesAmount = (value) => {
  return formatAmount(value, { showCurrency: false, showUnit: true })
}

// 方法
const fetchData = async () => {
  loading.value = true
  error.value = ''
  
  try {
    await salesStore.fetchSalesData(props.startDate, props.endDate)
  } catch (err) {
    error.value = '数据加载失败'
    console.error('Failed to load sales detail data:', err)
  } finally {
    loading.value = false
  }
}

const setSortBy = (field) => {
  if (sortBy.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortBy.value = field
    // 日期字段默认为降序（最新在上），其他字段默认为降序
    if (field === 'date') {
      sortOrder.value = 'desc' // 最新日期在上
    } else {
      sortOrder.value = 'desc' // 其他字段也默认降序
    }
  }
  currentPage.value = 1
}

const sortData = () => {
  currentPage.value = 1
}

const exportData = () => {
  emit('export-data', filteredData.value)
  
  // 简单的CSV导出实现
  const headers = ['日期', '产品名称', '销量(T)', '销售额(万元)', '单价(元/T)']
  const csvContent = [
    headers.join(','),
    ...filteredData.value.map(item => [
      item.date,
      `"${item.product_name}"`,
      item.volume,
      (item.amount / 10000).toFixed(2),
      item.price
    ].join(','))
  ].join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = `销售明细_${props.startDate}_${props.endDate}.csv`
  link.click()
}

// 监听搜索查询变化
watch(searchQuery, () => {
  currentPage.value = 1
})

// 监听props变化
watch([() => props.startDate, () => props.endDate], () => {
  fetchData()
})

onMounted(() => {
  fetchData()
})

// 暴露方法
defineExpose({
  refresh: fetchData,
  exportData
})
</script>

<style scoped>
.table-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-input {
  padding: 6px 12px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.9em;
  min-width: 200px;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.sort-selector {
  padding: 6px 12px;
  border: 1px solid var(--border-light);
  border-radius: 6px;
  font-size: 0.9em;
  background: white;
  cursor: pointer;
}

.sort-selector:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.table-container {
  overflow-x: auto;
  margin: 20px 0;
}

.sales-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9em;
}

.sales-table th,
.sales-table td {
  padding: 12px 8px;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
}

.sales-table th {
  background: var(--background-light);
  font-weight: 600;
  color: var(--text-primary);
  position: sticky;
  top: 0;
  z-index: 1;
}

.sales-table th.sortable {
  cursor: pointer;
  user-select: none;
  position: relative;
}

.sales-table th.sortable:hover {
  background: #e8f4fd;
}

.sort-icon {
  margin-left: 4px;
  color: var(--primary-blue);
}

.table-row:hover {
  background: #f8f9fa;
}

.product-name {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.number {
  text-align: right;
  font-family: 'Courier New', monospace;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 3em;
  margin-bottom: 16px;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  margin: 20px 0;
}

.page-info {
  color: var(--text-secondary);
  font-size: 0.9em;
}

.data-summary {
  display: flex;
  justify-content: space-around;
  padding: 16px;
  background: var(--background-light);
  border-radius: 8px;
  font-size: 0.9em;
  color: var(--text-secondary);
}

@media (max-width: 768px) {
  .table-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .search-input {
    min-width: auto;
  }
  
  .data-summary {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
  
  .pagination {
    flex-wrap: wrap;
    gap: 8px;
  }
}
</style>
