<template>
  <form @submit.prevent="handleSubmit" class="auth-form">
    <div class="form-group">
      <input
        v-model="formData.username"
        type="text"
        placeholder="用户名"
        class="form-input"
        :class="{ error: errors.username }"
        required
      />
      <div v-if="errors.username" class="field-error">{{ errors.username }}</div>
    </div>

    <div class="form-group">
      <input
        v-model="formData.password"
        type="password"
        placeholder="密码"
        class="form-input"
        :class="{ error: errors.password }"
        required
      />
      <div v-if="errors.password" class="field-error">{{ errors.password }}</div>
    </div>

    <button 
      type="submit" 
      class="auth-btn primary"
      :disabled="authStore.isLoading"
    >
      <span v-if="authStore.isLoading">登录中...</span>
      <span v-else>登录</span>
    </button>

    <p class="auth-switch">
      还没有账号？
      <button type="button" @click="$emit('switch-to-register')" class="link-btn">
        立即注册
      </button>
    </p>
  </form>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { validateUsername, validatePassword } from '@/utils/validators'

const emit = defineEmits(['success', 'switch-to-register'])

const authStore = useAuthStore()

const formData = reactive({
  username: '',
  password: ''
})

const errors = ref({})

const validateForm = () => {
  errors.value = {}
  let isValid = true

  // 验证用户名
  const usernameValidation = validateUsername(formData.username)
  if (!usernameValidation.valid) {
    errors.value.username = usernameValidation.message
    isValid = false
  }

  // 验证密码
  const passwordValidation = validatePassword(formData.password)
  if (!passwordValidation.valid) {
    errors.value.password = passwordValidation.message
    isValid = false
  }

  return isValid
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }

  const result = await authStore.login(formData)
  
  if (result.success) {
    emit('success')
  }
  // 错误处理在store中进行，这里不需要额外处理
}
</script>

<style scoped>
.auth-form {
  text-align: left;
}

.form-group {
  margin-bottom: 20px;
}

.form-input {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1em;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.form-input.error {
  border-color: var(--signal-red);
}

.field-error {
  color: var(--signal-red);
  font-size: 0.8em;
  margin-top: 5px;
}

.auth-btn {
  width: 100%;
  padding: 14px 20px;
  border: none;
  border-radius: 8px;
  font-size: 1em;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

.auth-btn.primary {
  background: var(--primary-blue);
  color: white;
}

.auth-btn.primary:hover:not(:disabled) {
  background: #004a94;
  transform: translateY(-1px);
}

.auth-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.auth-switch {
  text-align: center;
  color: #666;
  font-size: 0.9em;
}

.link-btn {
  background: none;
  border: none;
  color: var(--primary-blue);
  cursor: pointer;
  text-decoration: underline;
  font-size: inherit;
}

.link-btn:hover {
  color: #004a94;
}
</style>
