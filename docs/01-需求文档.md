# 全栈分析系统需求文档

## 1. 项目背景与目标

当前系统已从最初的 Python 静态页面生成方案，成功重构为一个基于 Cloudflare Pages、Workers 和 D1 数据库的现代化、可交互的全栈应用。

本项目旨在清晰地定义当前系统的功能范围、数据结构和技术要求，确保文档与实际实现保持一致，并为未来的迭代开发提供坚实的基础。

**核心目标:**

*   **功能完整性**: 全面描述系统现有的两大核心模块：**产销分析**与**价格监控**。
*   **动态交互**: 用户能够通过 Web 界面进行交互式的数据探索，如筛选日期、选择产品、配置预警等。
*   **自动化与高性能**: 借助 Cloudflare 的 Serverless 架构，实现自动化部署、全球高性能访问和低运维成本。
*   **可扩展性**: 维护一个健壮、可扩展的系统，为未来增加更复杂的数据分析功能（如销量预测、智能预警等）奠定基础。

## 2. 功能需求

系统主要包含两大核心功能模块：**产销分析模块**和**价格监控模块**。

### 2.1. 数据模型需求

系统需支持以下核心业务实体，以保证数据的一致性和完整性：

*   **`Products` (产品表):** 存储所有唯一的产品信息，包括 `product_id`, `product_name`, `category` 等。
*   **`DailyMetrics` (每日指标表):** 记录每个产品在特定日期的产量、销量、库存和平均价格。
*   **`PriceAdjustments` (价格调整记录表):** 存储产品价格的所有历史变动记录，是价格监控模块的核心数据源。包含 `adjustment_date`, `product_id`, `current_price`, `previous_price` 等字段。
*   **`Users` (用户表):** 存储用户认证信息，包括 `username` 和 `password`。
*   **`PriceAlerts` (价格预警记录表):** 存储由系统自动检测或用户自定义的各类价格预警事件。
*   **`PriceAlertConfigs` (价格预警配置表):** 存储用户自定义的价格预警规则。

**数据关系**:
- `DailyMetrics` 和 `PriceAdjustments` 通过 `product_id` 与 `Products` 表关联。
- `PriceAlerts` 和 `PriceAlertConfigs` 通过 `product_id` 与 `Products` 表关联。

### 2.2. 数据导入需求

*   **价格数据导入**: 支持通过前端界面上传特定格式的 Excel 文件 (`调价表.xlsx`)，后端自动解析并更新 `PriceAdjustments` 表。
*   **产销数据导入**: 支持通过前端界面上传包含每日产销存指标的 Excel 文件，后端自动解析并更新 `DailyMetrics` 表。

### 2.3. 产销分析模块需求

*   **仪表盘**:
    *   展示指定日期范围内的核心业务指标汇总，如总销量、总产量、总库存、平均价格和综合产销率。
*   **库存分析**:
    *   以图表形式展示指定日期的库存 Top-N 产品及其占比。
    *   展示指定时间范围内的每日总库存趋势。
*   **产销趋势**:
    *   以图表形式展示每日产销率的时间序列变化，并提供权威的区间平均产销率。
    *   提供数据一致性校验功能，确保不同计算方法下的产销率结果一致。
*   **销售分析**:
    *   以双轴图展示每日总销量和平均价格的关联趋势。

### 2.4. 价格监控模块需求

*   **关键产品监控**:
    *   提供一个专门的仪表盘，用于监控核心产品的价格活动，包括调价频率、价格波动性等。
*   **价格趋势分析**:
    *   用户可以选择一个或多个产品，查看其在指定时间范围内的详细价格走势图。
    *   支持按不同规格 (specification) 对产品价格进行细分展示。
*   **价格统计与排行**:
    *   提供产品的多维度价格统计数据，如最高价、最低价、平均价、价格振幅等。
    *   提供不同周期（日、周、月）内，价格下降金额或百分比的排行榜。
*   **系统级价格预警**:
    *   后端服务能自动检测并生成系统级预警，例如：
        *   **显著下降**: 单日价格下降超过预设的绝对金额或百分比。
        *   **连续下降**: 产品价格连续 N 天下降。
    *   前端提供一个预警中心，展示所有系统生成的预警事件。

### 2.5. 用户与认证需求

*   **用户注册**: 新用户需要通过有效的邀请码 (`SPRING2025`) 进行注册。
*   **用户登录**: 已注册用户可以通过用户名和密码登录系统，获取访问令牌。

## 3. 非功能性需求

*   **性能**: 核心数据查询和页面加载响应时间应在1-2秒内。
*   **安全性**: 后端 API 需防止 SQL 注入等常见安全漏洞。数据上传和用户管理功能需进行权限控制。
*   **可维护性**: 代码需遵循最佳实践，例如后端 API 使用类型安全的 Hono 框架，前后端代码分离，数据库结构通过 `schema.sql` 文件进行版本管理。
*   **部署**: 整个应用（前端和后端）需能通过 Cloudflare Pages 和 Wrangler CLI 进行一键部署。


---
**文档版本**: v1.0
**最后更新**: 2025-07-30
**负责人**: AI Assistant
