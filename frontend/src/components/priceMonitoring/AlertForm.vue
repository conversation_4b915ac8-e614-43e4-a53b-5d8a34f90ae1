<template>
  <form @submit.prevent="handleSubmit" class="alert-form">
    <div class="form-grid">
      <!-- Product Selection -->
      <div class="form-group">
        <label for="product">产品</label>
        <select id="product" v-model="alertData.product_id" @change="handleProductChange" required>
          <option disabled value="">请选择产品</option>
          <option v-for="product in products" :key="product.product_id" :value="product.product_id">
            {{ product.product_name }}
          </option>
        </select>
      </div>

      <!-- Spec Selection -->
      <div class="form-group">
        <label for="spec">规格</label>
        <select id="spec" v-model="alertData.spec" :disabled="!alertData.product_id" required>
          <option disabled value="">请选择规格</option>
          <option v-for="spec in availableSpecs" :key="spec.id" :value="spec.name">
            {{ spec.name }}
          </option>
        </select>
      </div>

      <!-- Condition -->
      <div class="form-group condition-group">
        <label>预警条件</label>
        <div class="condition-inputs">
          <select v-model="alertData.operator" required>
            <option value="lte">低于或等于</option>
            <option value="gte">高于或等于</option>
          </select>
          <input type="number" v-model.number="alertData.threshold_price" placeholder="价格阈值" required />
          <span>元/吨</span>
        </div>
      </div>

      <!-- Is Active Toggle -->
      <div class="form-group switch-group">
        <label for="is_active">激活状态</label>
        <label class="switch">
          <input type="checkbox" id="is_active" v-model="alertData.is_active">
          <span class="slider round"></span>
        </label>
      </div>
    </div>

    <div v-if="error" class="error-message">{{ error }}</div>

    <div class="form-actions">
      <BaseButton type="button" variant="secondary" @click="$emit('cancel')">取消</BaseButton>
      <BaseButton type="submit" :disabled="loading">
        {{ loading ? '保存中...' : '保存规则' }}
      </BaseButton>
    </div>
  </form>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { fetchData } from '@/utils/api'
import BaseButton from '@/components/common/BaseButton.vue'

const props = defineProps({
  initialAlert: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['save', 'cancel'])

const products = ref([])
const availableSpecs = ref([])
const loading = ref(false)
const error = ref(null)

const alertData = reactive({
  product_id: '',
  spec: '',
  operator: 'lte',
  threshold_price: null,
  is_active: true
})

const fetchProducts = async () => {
  try {
    const data = await fetchData('/api/products')
    products.value = Array.isArray(data) ? data : []
  } catch (err) {
    console.error('获取产品列表失败:', err)
    error.value = '无法加载产品列表。'
  }
}

const updateAvailableSpecs = () => {
  if (alertData.product_id) {
    // TODO: 规格应该由API提供
    availableSpecs.value = [
      { id: 'spec1', name: '30吨' },
      { id: 'spec2', name: '40吨' },
      { id: 'spec3', name: '50吨' }
    ]
  } else {
    availableSpecs.value = []
  }
}

const handleProductChange = () => {
  alertData.spec = ''
  updateAvailableSpecs()
}

const handleSubmit = () => {
  error.value = null
  if (!alertData.product_id || !alertData.spec || !alertData.threshold_price) {
    error.value = '请填写所有必填项。'
    return
  }
  emit('save', { ...alertData })
}

watch(() => props.initialAlert, (newVal) => {
  if (newVal) {
    Object.assign(alertData, newVal)
    updateAvailableSpecs()
  } else {
    // Reset for new alert
    Object.assign(alertData, {
      product_id: '',
      spec: '',
      operator: 'lte',
      threshold_price: null,
      is_active: true
    })
  }
}, { immediate: true, deep: true })

onMounted(() => {
  fetchProducts()
})
</script>

<style scoped>
.alert-form {
  padding: 8px;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.form-group input,
.form-group select {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 14px;
}

.condition-group {
  grid-column: 1 / -1;
}

.condition-inputs {
  display: flex;
  align-items: center;
  gap: 12px;
}

.condition-inputs select {
  flex-basis: 150px;
}

.condition-inputs input {
  flex-grow: 1;
}

.switch-group {
  align-items: flex-start;
}

.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 28px;
}

.switch input { 
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
}

input:checked + .slider {
  background-color: #28a745;
}

input:checked + .slider:before {
  transform: translateX(22px);
}

.slider.round {
  border-radius: 28px;
}

.slider.round:before {
  border-radius: 50%;
}

.error-message {
  color: #d9534f;
  margin-bottom: 16px;
  text-align: center;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #e9ecef;
}
</style>