<template>
  <form @submit.prevent="handleSubmit" class="auth-form">
    <div class="form-group">
      <input
        v-model="formData.username"
        type="text"
        placeholder="用户名"
        class="form-input"
        :class="{ error: errors.username }"
        required
      />
      <div v-if="errors.username" class="field-error">{{ errors.username }}</div>
    </div>

    <div class="form-group">
      <input
        v-model="formData.password"
        type="password"
        placeholder="密码"
        class="form-input"
        :class="{ error: errors.password }"
        required
      />
      <div v-if="errors.password" class="field-error">{{ errors.password }}</div>
    </div>

    <div class="form-group">
      <input
        v-model="formData.confirmPassword"
        type="password"
        placeholder="确认密码"
        class="form-input"
        :class="{ error: errors.confirmPassword }"
        required
      />
      <div v-if="errors.confirmPassword" class="field-error">{{ errors.confirmPassword }}</div>
    </div>

    <div class="form-group">
      <input
        v-model="formData.inviteCode"
        type="text"
        placeholder="邀请码"
        class="form-input"
        :class="{ error: errors.inviteCode }"
        required
      />
      <div v-if="errors.inviteCode" class="field-error">{{ errors.inviteCode }}</div>
      <div class="form-hint">请输入邀请码：SPRING2025</div>
    </div>

    <button 
      type="submit" 
      class="auth-btn primary"
      :disabled="authStore.isLoading"
    >
      <span v-if="authStore.isLoading">注册中...</span>
      <span v-else>注册</span>
    </button>

    <p class="auth-switch">
      已有账号？
      <button type="button" @click="$emit('switch-to-login')" class="link-btn">
        立即登录
      </button>
    </p>

    <!-- 成功消息 -->
    <div v-if="successMessage" class="auth-message success">
      {{ successMessage }}
    </div>
  </form>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { validateUsername, validatePassword, validateInviteCode } from '@/utils/validators'

const emit = defineEmits(['success', 'switch-to-login'])

const authStore = useAuthStore()

const formData = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  inviteCode: ''
})

const errors = ref({})
const successMessage = ref('')

const validateForm = () => {
  errors.value = {}
  let isValid = true

  // 验证用户名
  const usernameValidation = validateUsername(formData.username)
  if (!usernameValidation.valid) {
    errors.value.username = usernameValidation.message
    isValid = false
  }

  // 验证密码
  const passwordValidation = validatePassword(formData.password)
  if (!passwordValidation.valid) {
    errors.value.password = passwordValidation.message
    isValid = false
  }

  // 验证确认密码
  if (formData.password !== formData.confirmPassword) {
    errors.value.confirmPassword = '两次输入的密码不一致'
    isValid = false
  }

  // 验证邀请码
  const inviteCodeValidation = validateInviteCode(formData.inviteCode)
  if (!inviteCodeValidation.valid) {
    errors.value.inviteCode = inviteCodeValidation.message
    isValid = false
  }

  return isValid
}

const handleSubmit = async () => {
  successMessage.value = ''
  
  if (!validateForm()) {
    return
  }

  const result = await authStore.register({
    username: formData.username,
    password: formData.password,
    inviteCode: formData.inviteCode
  })
  
  if (result.success) {
    successMessage.value = result.message
    // 清空表单
    Object.keys(formData).forEach(key => {
      formData[key] = ''
    })
    // 3秒后自动切换到登录页面
    setTimeout(() => {
      emit('success')
    }, 2000)
  }
}
</script>

<style scoped>
.auth-form {
  text-align: left;
}

.form-group {
  margin-bottom: 20px;
}

.form-input {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1em;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.form-input.error {
  border-color: var(--signal-red);
}

.field-error {
  color: var(--signal-red);
  font-size: 0.8em;
  margin-top: 5px;
}

.form-hint {
  color: #999;
  font-size: 0.8em;
  margin-top: 5px;
}

.auth-btn {
  width: 100%;
  padding: 14px 20px;
  border: none;
  border-radius: 8px;
  font-size: 1em;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

.auth-btn.primary {
  background: var(--primary-blue);
  color: white;
}

.auth-btn.primary:hover:not(:disabled) {
  background: #004a94;
  transform: translateY(-1px);
}

.auth-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.auth-switch {
  text-align: center;
  color: #666;
  font-size: 0.9em;
}

.link-btn {
  background: none;
  border: none;
  color: var(--primary-blue);
  cursor: pointer;
  text-decoration: underline;
  font-size: inherit;
}

.link-btn:hover {
  color: #004a94;
}

.auth-message {
  margin-top: 20px;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 0.9em;
  text-align: center;
}

.auth-message.success {
  background: #e8f5e8;
  color: #2d7d2d;
  border: 1px solid #c3e6c3;
}
</style>
