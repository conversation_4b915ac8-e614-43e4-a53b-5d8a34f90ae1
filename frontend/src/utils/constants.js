/**
 * 应用常量定义
 */

// API 相关常量
export const API_ENDPOINTS = {
  SUMMARY: '/api/dashboard/summary',
  INVENTORY_TOP: '/api/inventory/top',
  INVENTORY_SUMMARY: '/api/inventory/summary',
  SALES_PRICE_TRENDS: '/api/trends/sales-price',
  PRODUCTION_RATIO: '/api/trends/ratio',
  AUTH_LOGIN: '/api/auth/login',
  AUTH_REGISTER: '/api/auth/register'
}

// 日期相关常量
export const DATE_FORMATS = {
  STANDARD: 'YYYY-MM-DD',
  DISPLAY: 'MM-DD',
  CHINESE: 'YYYY年MM月DD日'
}

// 默认日期范围（动态获取，避免硬编码）
export const getDefaultDateRange = () => {
  // 这个函数现在只提供备用值，实际日期应该从 useDateRangeStore 获取
  // 后端API有智能日期检测，会自动找到最新可用日期
  console.warn('⚠️ getDefaultDateRange() is deprecated. Use useDateRangeStore instead.');
  return {
    START: '2025-06-01', // 备用值
    END: '2025-07-29'    // 备用值 - 更新到最新日期
  };
};

// 为了向后兼容，保留 DEFAULT_DATE_RANGE 但标记为已弃用
export const DEFAULT_DATE_RANGE = getDefaultDateRange();

// 新的动态日期范围获取函数
export const getDynamicDateRange = async () => {
  try {
    // 这个函数将被 useDateRangeStore 替代
    const { useDateRangeStore } = await import('@/stores/dateRange');
    const dateRangeStore = useDateRangeStore();
    
    if (!dateRangeStore.isDateRangeLoaded) {
      await dateRangeStore.fetchAvailableDateRange();
    }
    
    return {
      START: dateRangeStore.defaultStartDate,
      END: dateRangeStore.defaultEndDate
    };
  } catch (error) {
    console.error('Failed to get dynamic date range:', error);
    return getDefaultDateRange(); // 备用方案
  }
};

// 图表颜色配置（专业财务报表风格）
export const CHART_COLORS = {
  PRIMARY: '#005BAC',      // 主色调
  SECONDARY: '#49A9E8',    // 辅助色
  ACCENT: '#D92E2E',       // 强调色
  SUCCESS: '#34C759',      // 成功色（绿色）
  WARNING: '#FF9500',      // 警告色（橙色）
  DANGER: '#D92E2E',       // 危险色（红色）
  NEUTRAL: '#8E8E93',      // 中性色
  BACKGROUND: '#F8F9FA'    // 背景色
}

// 图表主题配置
export const CHART_THEME = {
  backgroundColor: '#ffffff',
  textStyle: {
    color: '#333333',
    fontFamily: 'Microsoft YaHei, 微软雅黑, Arial, sans-serif'
  },
  title: {
    textStyle: {
      color: CHART_COLORS.PRIMARY,
      fontSize: 16,
      fontWeight: 'bold'
    }
  },
  legend: {
    textStyle: {
      color: '#666666'
    }
  },
  grid: {
    borderColor: '#E0E0E0'
  }
}

// 产品类别配置
export const PRODUCT_CATEGORIES = [
  { value: '', label: '全部类别' },
  { value: '冷冻食品', label: '冷冻食品' },
  { value: '冷鲜食品', label: '冷鲜食品' },
  { value: '加工食品', label: '加工食品' }
]

// 时间范围选项
export const TIME_RANGE_OPTIONS = [
  { value: 7, label: '近7天' },
  { value: 30, label: '近30天' },
  { value: 90, label: '近90天' }
]

// 数据刷新间隔（毫秒）
export const REFRESH_INTERVALS = {
  REALTIME: 30000,    // 30秒
  NORMAL: 300000,     // 5分钟
  SLOW: 600000        // 10分钟
}

// 本地存储键名
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_INFO: 'user_info',
  LAST_REFRESH: 'last_refresh',
  CHART_SETTINGS: 'chart_settings'
}

// 用户角色
export const USER_ROLES = {
  ADMIN: 'admin',
  USER: 'user',
  GUEST: 'guest'
}

// 认证相关常量
export const AUTH_CONFIG = {
  INVITE_CODE: 'SPRING2025',
  TOKEN_EXPIRY: 24 * 60 * 60 * 1000, // 24小时
  DEVELOPMENT_MODE: false // 开发模式标志
}

// 图表类型
export const CHART_TYPES = {
  LINE: 'line',
  BAR: 'bar',
  PIE: 'pie',
  AREA: 'area',
  SCATTER: 'scatter'
}

// 数据状态
export const DATA_STATUS = {
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
  EMPTY: 'empty'
}

// 响应式断点
export const BREAKPOINTS = {
  MOBILE: 768,
  TABLET: 1024,
  DESKTOP: 1200
}

// 动画配置
export const ANIMATION_CONFIG = {
  DURATION: 300,
  EASING: 'ease-in-out'
}

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  AUTH_FAILED: '认证失败，请重新登录',
  DATA_LOAD_FAILED: '数据加载失败，请稍后重试',
  INVALID_INPUT: '输入数据格式不正确',
  SERVER_ERROR: '服务器错误，请联系管理员'
}

// 成功消息
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: '登录成功',
  REGISTER_SUCCESS: '注册成功',
  DATA_UPDATED: '数据更新成功',
  SETTINGS_SAVED: '设置保存成功'
}
