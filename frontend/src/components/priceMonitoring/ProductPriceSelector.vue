<template>
  <div class="product-selector-container">
    <label for="product-search" class="selector-label">选择监控产品:</label>
    <div class="search-container">
      <input
        id="product-search"
        v-model="searchQuery"
        @input="filterProducts"
        type="text"
        placeholder="搜索产品..."
        class="product-search"
      />
      <select v-model="selectedProduct" @change="onProductChange" class="product-select">
        <option disabled value="">请选择一个产品</option>
        <optgroup v-if="recommendedProducts.length > 0" label="🌟 推荐产品">
          <option v-for="product in recommendedProducts" :key="product.product_name" :value="product.product_name">
            {{ product.product_name }} ({{ product.adjustment_days }}天内{{ product.total_adjustments }}次调整)
          </option>
        </optgroup>
        <optgroup v-if="otherProducts.length > 0" label="其他产品">
          <option v-for="product in otherProducts" :key="product.product_name" :value="product.product_name">
            {{ product.product_name }} ({{ product.adjustment_days }}天内{{ product.total_adjustments }}次调整)
          </option>
        </optgroup>
      </select>
    </div>
    <div v-if="loading" class="loader"></div>
    <p v-if="error" class="error-message">{{ error }}</p>
    <p v-if="!loading && filteredProducts.length === 0 && searchQuery" class="no-results">
      没有找到匹配的产品
    </p>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { api } from '@/utils/api';

const emit = defineEmits(['product-selected']);

const products = ref([]);
const selectedProduct = ref('');
const loading = ref(false);
const error = ref(null);
const searchQuery = ref('');

// 过滤后的产品列表
const filteredProducts = computed(() => {
  if (!searchQuery.value) {
    return products.value;
  }
  const query = searchQuery.value.toLowerCase();
  return products.value.filter(product => 
    product.product_name.toLowerCase().includes(query)
  );
});

// 推荐产品列表
const recommendedProducts = computed(() => {
  return filteredProducts.value.filter(product => product.is_recommended);
});

// 其他产品列表
const otherProducts = computed(() => {
  return filteredProducts.value.filter(product => !product.is_recommended);
});

// 过滤产品
function filterProducts() {
  // 如果选中的产品不在过滤结果中，清空选择
  if (selectedProduct.value && !filteredProducts.value.find(p => p.product_name === selectedProduct.value)) {
    selectedProduct.value = '';
  }
}

async function fetchKeyProducts() {
  loading.value = true;
  error.value = null;
  try {
    const response = await api.getKeyProducts();
    if (response.data.success) {
      products.value = response.data.data;
      // 默认选中第一个产品
      if (products.value.length > 0 && !selectedProduct.value) {
        selectedProduct.value = products.value[0].product_name;
        emit('product-selected', selectedProduct.value);
      }
    } else {
      throw new Error(response.data.message || '获取产品列表失败');
    }
  } catch (err) {
    error.value = err.message || '网络错误，请稍后重试';
    console.error(error.value);
  } finally {
    loading.value = false;
  }
}

function onProductChange() {
  if (selectedProduct.value) {
    emit('product-selected', selectedProduct.value);
  }
}

onMounted(fetchKeyProducts);
</script>

<style scoped>
.product-selector-container {
  padding: 1rem;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.selector-label {
  display: block;
  margin-bottom: 10px;
  font-weight: bold;
}
.search-container {
  display: flex;
  gap: 10px;
  align-items: center;
}
.product-search {
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #ccc;
  min-width: 200px;
  flex: 0 0 auto;
}
.product-search:focus {
  outline: none;
  border-color: #3498db;
}
.product-select {
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #ccc;
  min-width: 350px;
  flex: 1;
}
.product-select optgroup {
  font-weight: bold;
  color: #333;
}
.product-select option {
  font-weight: normal;
  padding: 4px 8px;
}
.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
  display: inline-block;
  margin-left: 10px;
  margin-top: 10px;
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
.error-message {
  color: #D92E2E;
  margin-top: 10px;
}
.no-results {
  color: #666;
  margin-top: 10px;
  font-style: italic;
}
</style>
