import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { getDefaultDateRange, DEFAULT_DATE_RANGE } from '../constants'

describe('Dynamic Date Constants', () => {
  beforeEach(() => {
    vi.useFakeTimers()
    vi.setSystemTime(new Date('2025-07-26'))
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('getDefaultDateRange', () => {
    it('should return dynamic date range for 30 days', () => {
      const result = getDefaultDateRange()
      expect(result).toEqual({
        START: '2025-06-27', // 30 days ago from July 26
        END: '2025-07-26'    // Today
      })
    })

    it('should return current dates when called multiple times', () => {
      const result1 = getDefaultDateRange()
      const result2 = getDefaultDateRange()
      expect(result1).toEqual(result2)
    })
  })

  describe('DEFAULT_DATE_RANGE', () => {
    it('should contain dynamic values', () => {
      // Note: DEFAULT_DATE_RANGE is calculated at import time
      // So it will reflect the date when the module was first loaded
      expect(DEFAULT_DATE_RANGE).toHaveProperty('START')
      expect(DEFAULT_DATE_RANGE).toHaveProperty('END')
      expect(typeof DEFAULT_DATE_RANGE.START).toBe('string')
      expect(typeof DEFAULT_DATE_RANGE.END).toBe('string')
      expect(DEFAULT_DATE_RANGE.START).toMatch(/\d{4}-\d{2}-\d{2}/)
      expect(DEFAULT_DATE_RANGE.END).toMatch(/\d{4}-\d{2}-\d{2}/)
    })
  })
})